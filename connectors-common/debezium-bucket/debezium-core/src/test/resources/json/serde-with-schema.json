{"schema": {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "int32", "optional": false, "field": "id"}, {"type": "string", "optional": false, "field": "first_name"}, {"type": "string", "optional": false, "field": "last_name"}, {"type": "string", "optional": false, "field": "email"}], "optional": true, "name": "mysql-server-1.inventory.customers.Value", "field": "before"}, {"type": "struct", "fields": [{"type": "int32", "optional": false, "field": "id"}, {"type": "string", "optional": false, "field": "first_name"}, {"type": "string", "optional": false, "field": "last_name"}, {"type": "string", "optional": false, "field": "email"}], "optional": true, "name": "mysql-server-1.inventory.customers.Value", "field": "after"}, {"type": "struct", "fields": [{"type": "string", "optional": false, "field": "version"}, {"type": "string", "optional": false, "field": "connector"}, {"type": "string", "optional": false, "field": "name"}, {"type": "int64", "optional": false, "field": "ts_ms"}, {"type": "boolean", "optional": true, "default": false, "field": "snapshot"}, {"type": "string", "optional": false, "field": "db"}, {"type": "string", "optional": true, "field": "table"}, {"type": "int64", "optional": false, "field": "server_id"}, {"type": "string", "optional": true, "field": "gtid"}, {"type": "string", "optional": false, "field": "file"}, {"type": "int64", "optional": false, "field": "pos"}, {"type": "int32", "optional": false, "field": "row"}, {"type": "int64", "optional": true, "field": "thread"}, {"type": "string", "optional": true, "field": "query"}], "optional": false, "name": "io.debezium.connector.mysql.Source", "field": "source"}, {"type": "string", "optional": false, "field": "op"}, {"type": "int64", "optional": true, "field": "ts_ms"}], "optional": false, "name": "mysql-server-1.inventory.customers.Envelope"}, "payload": {"op": "c", "ts_ms": 1465491411815, "before": null, "after": {"id": 1004, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "source": {"version": "0.10.0.Final", "connector": "mysql", "name": "mysql-server-1", "ts_ms": 0, "snapshot": false, "db": "inventory", "table": "customers", "server_id": 0, "gtid": null, "file": "mysql-bin.000003", "pos": 154, "row": 0, "thread": 7, "query": "INSERT INTO customers (first_name, last_name, email) VALUES ('<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<EMAIL>')"}}}