<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>io.debezium</groupId>
        <artifactId>debezium-parent</artifactId>
        <version>1.5.4.Final</version>
	<relativePath>../debezium-parent/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>debezium-connector-postgres</artifactId>
    <name>Debezium Connector for PostgreSQL</name>
    <packaging>jar</packaging>
    <properties>
        <!-- 
          Specify the properties that will be used for setting up the integration tests' Docker container.
          Note that the `dockerhost.ip` property is computed from the IP address of DOCKER_HOST, which will
          work on all platforms. We'll set some of these as system properties during integration testing.
      -->
        <version.postgres.server>9.6</version.postgres.server>
        <postgres.port>5432</postgres.port>
        <postgres.user>postgres</postgres.user>
        <postgres.password>postgres</postgres.password>
        <postgres.db.name>postgres</postgres.db.name>
        <postgres.encoding>UTF8</postgres.encoding>
        <postgres.system.lang>en_US.utf8</postgres.system.lang>
        <postgres.image>debezium/postgres:${version.postgres.server}</postgres.image>
        <postgres.config.file>/usr/share/postgresql/postgresql.conf.sample</postgres.config.file>
        <docker.skip>false</docker.skip>
        <docker.showLogs>true</docker.showLogs>
        <docker.initimage>ln -fs /usr/share/zoneinfo/US/Samoa /etc/localtime &amp;&amp; echo timezone=US/Samoa &gt;&gt; ${postgres.config.file}</docker.initimage>

        <protobuf.output.directory>${project.basedir}/generated-sources</protobuf.output.directory>

        <!-- We're tracking the API changes of the SPI. -->
        <revapi.skip>false</revapi.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>connect-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-embedded</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-core</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-embedded</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easytesting</groupId>
            <artifactId>fest-assert</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>kafka-connect-avro-converter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>testcontainers</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-testing-testcontainers</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
   
    <build>
        <plugins>
            <plugin>
                <groupId>com.github.os72</groupId>
                <artifactId>protoc-jar-maven-plugin</artifactId>
                <version>${version.protoc.maven.plugin}</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <protocVersion>${version.com.google.protobuf}</protocVersion> <!-- 2.4.1, 2.5.0, 2.6.1, 3.0.0 -->
                            <outputDirectory>${protobuf.output.directory}</outputDirectory>
                            <inputDirectories>
                                <include>src/main/proto</include>
                            </inputDirectories>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- 
            Unlike surefire, the failsafe plugin ensures 'post-integration-test' phase always runs, even
            when there are failed integration tests. We rely upon this to always shut down the Docker container
            after the integration tests (defined as '*IT.java') are run.
            -->
            <plugin>
                <groupId>org.revapi</groupId>
                <artifactId>revapi-maven-plugin</artifactId>
                <version>${version.revapi.plugin}</version>
                <configuration>
                    <analysisConfiguration combine.children="append">
                        <revapi.java>
                            <filter>
                                <packages>
                                    <include>
                                        <item>io.debezium.connector.postgresql.spi</item>
                                    </include>
                                </packages>
                            </filter>
                        </revapi.java>
                    </analysisConfiguration>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <!-- Apply the properties set in the POM to the resource files -->
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <includes>
                    <include>*</include>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>*</include>
                    <include>**/*</include>
                </includes>
            </testResource>
        </testResources>
    </build>
</project>
