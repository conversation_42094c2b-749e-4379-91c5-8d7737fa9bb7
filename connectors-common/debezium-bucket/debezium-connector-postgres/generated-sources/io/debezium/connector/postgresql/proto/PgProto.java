// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: pg_logicaldec.proto

package io.debezium.connector.postgresql.proto;

public final class PgProto {
  private PgProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code decoderbufs.Op}
   */
  public enum Op
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>UNKNOWN = -1;</code>
     */
    UNKNOWN(-1),
    /**
     * <code>INSERT = 0;</code>
     */
    INSERT(0),
    /**
     * <code>UPDATE = 1;</code>
     */
    UPDATE(1),
    /**
     * <code>DELETE = 2;</code>
     */
    DELETE(2),
    /**
     * <code>BEGIN = 3;</code>
     */
    BEGIN(3),
    /**
     * <code>COMMIT = 4;</code>
     */
    COMMIT(4),
    ;

    /**
     * <code>UNKNOWN = -1;</code>
     */
    public static final int UNKNOWN_VALUE = -1;
    /**
     * <code>INSERT = 0;</code>
     */
    public static final int INSERT_VALUE = 0;
    /**
     * <code>UPDATE = 1;</code>
     */
    public static final int UPDATE_VALUE = 1;
    /**
     * <code>DELETE = 2;</code>
     */
    public static final int DELETE_VALUE = 2;
    /**
     * <code>BEGIN = 3;</code>
     */
    public static final int BEGIN_VALUE = 3;
    /**
     * <code>COMMIT = 4;</code>
     */
    public static final int COMMIT_VALUE = 4;


    public final int getNumber() {
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static Op valueOf(int value) {
      return forNumber(value);
    }

    public static Op forNumber(int value) {
      switch (value) {
        case -1: return UNKNOWN;
        case 0: return INSERT;
        case 1: return UPDATE;
        case 2: return DELETE;
        case 3: return BEGIN;
        case 4: return COMMIT;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<Op>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        Op> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<Op>() {
            public Op findValueByNumber(int number) {
              return Op.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return io.debezium.connector.postgresql.proto.PgProto.getDescriptor().getEnumTypes().get(0);
    }

    private static final Op[] VALUES = values();

    public static Op valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private Op(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:decoderbufs.Op)
  }

  public interface PointOrBuilder extends
      // @@protoc_insertion_point(interface_extends:decoderbufs.Point)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>required double x = 1;</code>
     */
    boolean hasX();
    /**
     * <code>required double x = 1;</code>
     */
    double getX();

    /**
     * <code>required double y = 2;</code>
     */
    boolean hasY();
    /**
     * <code>required double y = 2;</code>
     */
    double getY();
  }
  /**
   * Protobuf type {@code decoderbufs.Point}
   */
  public  static final class Point extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:decoderbufs.Point)
      PointOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Point.newBuilder() to construct.
    private Point(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Point() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Point();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Point(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 9: {
              bitField0_ |= 0x00000001;
              x_ = input.readDouble();
              break;
            }
            case 17: {
              bitField0_ |= 0x00000002;
              y_ = input.readDouble();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_Point_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_Point_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              io.debezium.connector.postgresql.proto.PgProto.Point.class, io.debezium.connector.postgresql.proto.PgProto.Point.Builder.class);
    }

    private int bitField0_;
    public static final int X_FIELD_NUMBER = 1;
    private double x_;
    /**
     * <code>required double x = 1;</code>
     */
    public boolean hasX() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required double x = 1;</code>
     */
    public double getX() {
      return x_;
    }

    public static final int Y_FIELD_NUMBER = 2;
    private double y_;
    /**
     * <code>required double y = 2;</code>
     */
    public boolean hasY() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required double y = 2;</code>
     */
    public double getY() {
      return y_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasX()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasY()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeDouble(1, x_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeDouble(2, y_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(1, x_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(2, y_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof io.debezium.connector.postgresql.proto.PgProto.Point)) {
        return super.equals(obj);
      }
      io.debezium.connector.postgresql.proto.PgProto.Point other = (io.debezium.connector.postgresql.proto.PgProto.Point) obj;

      if (hasX() != other.hasX()) return false;
      if (hasX()) {
        if (java.lang.Double.doubleToLongBits(getX())
            != java.lang.Double.doubleToLongBits(
                other.getX())) return false;
      }
      if (hasY() != other.hasY()) return false;
      if (hasY()) {
        if (java.lang.Double.doubleToLongBits(getY())
            != java.lang.Double.doubleToLongBits(
                other.getY())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasX()) {
        hash = (37 * hash) + X_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            java.lang.Double.doubleToLongBits(getX()));
      }
      if (hasY()) {
        hash = (37 * hash) + Y_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            java.lang.Double.doubleToLongBits(getY()));
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static io.debezium.connector.postgresql.proto.PgProto.Point parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.Point parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.Point parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.Point parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.Point parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.Point parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.Point parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.Point parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.Point parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.Point parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.Point parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.Point parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(io.debezium.connector.postgresql.proto.PgProto.Point prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code decoderbufs.Point}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:decoderbufs.Point)
        io.debezium.connector.postgresql.proto.PgProto.PointOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_Point_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_Point_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                io.debezium.connector.postgresql.proto.PgProto.Point.class, io.debezium.connector.postgresql.proto.PgProto.Point.Builder.class);
      }

      // Construct using io.debezium.connector.postgresql.proto.PgProto.Point.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        x_ = 0D;
        bitField0_ = (bitField0_ & ~0x00000001);
        y_ = 0D;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_Point_descriptor;
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.Point getDefaultInstanceForType() {
        return io.debezium.connector.postgresql.proto.PgProto.Point.getDefaultInstance();
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.Point build() {
        io.debezium.connector.postgresql.proto.PgProto.Point result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.Point buildPartial() {
        io.debezium.connector.postgresql.proto.PgProto.Point result = new io.debezium.connector.postgresql.proto.PgProto.Point(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.x_ = x_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.y_ = y_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof io.debezium.connector.postgresql.proto.PgProto.Point) {
          return mergeFrom((io.debezium.connector.postgresql.proto.PgProto.Point)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(io.debezium.connector.postgresql.proto.PgProto.Point other) {
        if (other == io.debezium.connector.postgresql.proto.PgProto.Point.getDefaultInstance()) return this;
        if (other.hasX()) {
          setX(other.getX());
        }
        if (other.hasY()) {
          setY(other.getY());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasX()) {
          return false;
        }
        if (!hasY()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        io.debezium.connector.postgresql.proto.PgProto.Point parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (io.debezium.connector.postgresql.proto.PgProto.Point) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private double x_ ;
      /**
       * <code>required double x = 1;</code>
       */
      public boolean hasX() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>required double x = 1;</code>
       */
      public double getX() {
        return x_;
      }
      /**
       * <code>required double x = 1;</code>
       */
      public Builder setX(double value) {
        bitField0_ |= 0x00000001;
        x_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required double x = 1;</code>
       */
      public Builder clearX() {
        bitField0_ = (bitField0_ & ~0x00000001);
        x_ = 0D;
        onChanged();
        return this;
      }

      private double y_ ;
      /**
       * <code>required double y = 2;</code>
       */
      public boolean hasY() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>required double y = 2;</code>
       */
      public double getY() {
        return y_;
      }
      /**
       * <code>required double y = 2;</code>
       */
      public Builder setY(double value) {
        bitField0_ |= 0x00000002;
        y_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required double y = 2;</code>
       */
      public Builder clearY() {
        bitField0_ = (bitField0_ & ~0x00000002);
        y_ = 0D;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:decoderbufs.Point)
    }

    // @@protoc_insertion_point(class_scope:decoderbufs.Point)
    private static final io.debezium.connector.postgresql.proto.PgProto.Point DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new io.debezium.connector.postgresql.proto.PgProto.Point();
    }

    public static io.debezium.connector.postgresql.proto.PgProto.Point getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Point>
        PARSER = new com.google.protobuf.AbstractParser<Point>() {
      @java.lang.Override
      public Point parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Point(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Point> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Point> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public io.debezium.connector.postgresql.proto.PgProto.Point getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DatumMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:decoderbufs.DatumMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string column_name = 1;</code>
     */
    boolean hasColumnName();
    /**
     * <code>optional string column_name = 1;</code>
     */
    java.lang.String getColumnName();
    /**
     * <code>optional string column_name = 1;</code>
     */
    com.google.protobuf.ByteString
        getColumnNameBytes();

    /**
     * <code>optional int64 column_type = 2;</code>
     */
    boolean hasColumnType();
    /**
     * <code>optional int64 column_type = 2;</code>
     */
    long getColumnType();

    /**
     * <code>optional int32 datum_int32 = 3;</code>
     */
    boolean hasDatumInt32();
    /**
     * <code>optional int32 datum_int32 = 3;</code>
     */
    int getDatumInt32();

    /**
     * <code>optional int64 datum_int64 = 4;</code>
     */
    boolean hasDatumInt64();
    /**
     * <code>optional int64 datum_int64 = 4;</code>
     */
    long getDatumInt64();

    /**
     * <code>optional float datum_float = 5;</code>
     */
    boolean hasDatumFloat();
    /**
     * <code>optional float datum_float = 5;</code>
     */
    float getDatumFloat();

    /**
     * <code>optional double datum_double = 6;</code>
     */
    boolean hasDatumDouble();
    /**
     * <code>optional double datum_double = 6;</code>
     */
    double getDatumDouble();

    /**
     * <code>optional bool datum_bool = 7;</code>
     */
    boolean hasDatumBool();
    /**
     * <code>optional bool datum_bool = 7;</code>
     */
    boolean getDatumBool();

    /**
     * <code>optional string datum_string = 8;</code>
     */
    boolean hasDatumString();
    /**
     * <code>optional string datum_string = 8;</code>
     */
    java.lang.String getDatumString();
    /**
     * <code>optional string datum_string = 8;</code>
     */
    com.google.protobuf.ByteString
        getDatumStringBytes();

    /**
     * <code>optional bytes datum_bytes = 9;</code>
     */
    boolean hasDatumBytes();
    /**
     * <code>optional bytes datum_bytes = 9;</code>
     */
    com.google.protobuf.ByteString getDatumBytes();

    /**
     * <code>optional .decoderbufs.Point datum_point = 10;</code>
     */
    boolean hasDatumPoint();
    /**
     * <code>optional .decoderbufs.Point datum_point = 10;</code>
     */
    io.debezium.connector.postgresql.proto.PgProto.Point getDatumPoint();
    /**
     * <code>optional .decoderbufs.Point datum_point = 10;</code>
     */
    io.debezium.connector.postgresql.proto.PgProto.PointOrBuilder getDatumPointOrBuilder();

    /**
     * <code>optional bool datum_missing = 11;</code>
     */
    boolean hasDatumMissing();
    /**
     * <code>optional bool datum_missing = 11;</code>
     */
    boolean getDatumMissing();

    public io.debezium.connector.postgresql.proto.PgProto.DatumMessage.DatumCase getDatumCase();
  }
  /**
   * Protobuf type {@code decoderbufs.DatumMessage}
   */
  public  static final class DatumMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:decoderbufs.DatumMessage)
      DatumMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DatumMessage.newBuilder() to construct.
    private DatumMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DatumMessage() {
      columnName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DatumMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DatumMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              columnName_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              columnType_ = input.readInt64();
              break;
            }
            case 24: {
              datumCase_ = 3;
              datum_ = input.readInt32();
              break;
            }
            case 32: {
              datumCase_ = 4;
              datum_ = input.readInt64();
              break;
            }
            case 45: {
              datumCase_ = 5;
              datum_ = input.readFloat();
              break;
            }
            case 49: {
              datumCase_ = 6;
              datum_ = input.readDouble();
              break;
            }
            case 56: {
              datumCase_ = 7;
              datum_ = input.readBool();
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              datumCase_ = 8;
              datum_ = bs;
              break;
            }
            case 74: {
              datumCase_ = 9;
              datum_ = input.readBytes();
              break;
            }
            case 82: {
              io.debezium.connector.postgresql.proto.PgProto.Point.Builder subBuilder = null;
              if (datumCase_ == 10) {
                subBuilder = ((io.debezium.connector.postgresql.proto.PgProto.Point) datum_).toBuilder();
              }
              datum_ =
                  input.readMessage(io.debezium.connector.postgresql.proto.PgProto.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((io.debezium.connector.postgresql.proto.PgProto.Point) datum_);
                datum_ = subBuilder.buildPartial();
              }
              datumCase_ = 10;
              break;
            }
            case 88: {
              datumCase_ = 11;
              datum_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_DatumMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_DatumMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              io.debezium.connector.postgresql.proto.PgProto.DatumMessage.class, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder.class);
    }

    private int bitField0_;
    private int datumCase_ = 0;
    private java.lang.Object datum_;
    public enum DatumCase
        implements com.google.protobuf.Internal.EnumLite {
      DATUM_INT32(3),
      DATUM_INT64(4),
      DATUM_FLOAT(5),
      DATUM_DOUBLE(6),
      DATUM_BOOL(7),
      DATUM_STRING(8),
      DATUM_BYTES(9),
      DATUM_POINT(10),
      DATUM_MISSING(11),
      DATUM_NOT_SET(0);
      private final int value;
      private DatumCase(int value) {
        this.value = value;
      }
      /**
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static DatumCase valueOf(int value) {
        return forNumber(value);
      }

      public static DatumCase forNumber(int value) {
        switch (value) {
          case 3: return DATUM_INT32;
          case 4: return DATUM_INT64;
          case 5: return DATUM_FLOAT;
          case 6: return DATUM_DOUBLE;
          case 7: return DATUM_BOOL;
          case 8: return DATUM_STRING;
          case 9: return DATUM_BYTES;
          case 10: return DATUM_POINT;
          case 11: return DATUM_MISSING;
          case 0: return DATUM_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public DatumCase
    getDatumCase() {
      return DatumCase.forNumber(
          datumCase_);
    }

    public static final int COLUMN_NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object columnName_;
    /**
     * <code>optional string column_name = 1;</code>
     */
    public boolean hasColumnName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string column_name = 1;</code>
     */
    public java.lang.String getColumnName() {
      java.lang.Object ref = columnName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          columnName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string column_name = 1;</code>
     */
    public com.google.protobuf.ByteString
        getColumnNameBytes() {
      java.lang.Object ref = columnName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        columnName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int COLUMN_TYPE_FIELD_NUMBER = 2;
    private long columnType_;
    /**
     * <code>optional int64 column_type = 2;</code>
     */
    public boolean hasColumnType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 column_type = 2;</code>
     */
    public long getColumnType() {
      return columnType_;
    }

    public static final int DATUM_INT32_FIELD_NUMBER = 3;
    /**
     * <code>optional int32 datum_int32 = 3;</code>
     */
    public boolean hasDatumInt32() {
      return datumCase_ == 3;
    }
    /**
     * <code>optional int32 datum_int32 = 3;</code>
     */
    public int getDatumInt32() {
      if (datumCase_ == 3) {
        return (java.lang.Integer) datum_;
      }
      return 0;
    }

    public static final int DATUM_INT64_FIELD_NUMBER = 4;
    /**
     * <code>optional int64 datum_int64 = 4;</code>
     */
    public boolean hasDatumInt64() {
      return datumCase_ == 4;
    }
    /**
     * <code>optional int64 datum_int64 = 4;</code>
     */
    public long getDatumInt64() {
      if (datumCase_ == 4) {
        return (java.lang.Long) datum_;
      }
      return 0L;
    }

    public static final int DATUM_FLOAT_FIELD_NUMBER = 5;
    /**
     * <code>optional float datum_float = 5;</code>
     */
    public boolean hasDatumFloat() {
      return datumCase_ == 5;
    }
    /**
     * <code>optional float datum_float = 5;</code>
     */
    public float getDatumFloat() {
      if (datumCase_ == 5) {
        return (java.lang.Float) datum_;
      }
      return 0F;
    }

    public static final int DATUM_DOUBLE_FIELD_NUMBER = 6;
    /**
     * <code>optional double datum_double = 6;</code>
     */
    public boolean hasDatumDouble() {
      return datumCase_ == 6;
    }
    /**
     * <code>optional double datum_double = 6;</code>
     */
    public double getDatumDouble() {
      if (datumCase_ == 6) {
        return (java.lang.Double) datum_;
      }
      return 0D;
    }

    public static final int DATUM_BOOL_FIELD_NUMBER = 7;
    /**
     * <code>optional bool datum_bool = 7;</code>
     */
    public boolean hasDatumBool() {
      return datumCase_ == 7;
    }
    /**
     * <code>optional bool datum_bool = 7;</code>
     */
    public boolean getDatumBool() {
      if (datumCase_ == 7) {
        return (java.lang.Boolean) datum_;
      }
      return false;
    }

    public static final int DATUM_STRING_FIELD_NUMBER = 8;
    /**
     * <code>optional string datum_string = 8;</code>
     */
    public boolean hasDatumString() {
      return datumCase_ == 8;
    }
    /**
     * <code>optional string datum_string = 8;</code>
     */
    public java.lang.String getDatumString() {
      java.lang.Object ref = "";
      if (datumCase_ == 8) {
        ref = datum_;
      }
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8() && (datumCase_ == 8)) {
          datum_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string datum_string = 8;</code>
     */
    public com.google.protobuf.ByteString
        getDatumStringBytes() {
      java.lang.Object ref = "";
      if (datumCase_ == 8) {
        ref = datum_;
      }
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        if (datumCase_ == 8) {
          datum_ = b;
        }
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DATUM_BYTES_FIELD_NUMBER = 9;
    /**
     * <code>optional bytes datum_bytes = 9;</code>
     */
    public boolean hasDatumBytes() {
      return datumCase_ == 9;
    }
    /**
     * <code>optional bytes datum_bytes = 9;</code>
     */
    public com.google.protobuf.ByteString getDatumBytes() {
      if (datumCase_ == 9) {
        return (com.google.protobuf.ByteString) datum_;
      }
      return com.google.protobuf.ByteString.EMPTY;
    }

    public static final int DATUM_POINT_FIELD_NUMBER = 10;
    /**
     * <code>optional .decoderbufs.Point datum_point = 10;</code>
     */
    public boolean hasDatumPoint() {
      return datumCase_ == 10;
    }
    /**
     * <code>optional .decoderbufs.Point datum_point = 10;</code>
     */
    public io.debezium.connector.postgresql.proto.PgProto.Point getDatumPoint() {
      if (datumCase_ == 10) {
         return (io.debezium.connector.postgresql.proto.PgProto.Point) datum_;
      }
      return io.debezium.connector.postgresql.proto.PgProto.Point.getDefaultInstance();
    }
    /**
     * <code>optional .decoderbufs.Point datum_point = 10;</code>
     */
    public io.debezium.connector.postgresql.proto.PgProto.PointOrBuilder getDatumPointOrBuilder() {
      if (datumCase_ == 10) {
         return (io.debezium.connector.postgresql.proto.PgProto.Point) datum_;
      }
      return io.debezium.connector.postgresql.proto.PgProto.Point.getDefaultInstance();
    }

    public static final int DATUM_MISSING_FIELD_NUMBER = 11;
    /**
     * <code>optional bool datum_missing = 11;</code>
     */
    public boolean hasDatumMissing() {
      return datumCase_ == 11;
    }
    /**
     * <code>optional bool datum_missing = 11;</code>
     */
    public boolean getDatumMissing() {
      if (datumCase_ == 11) {
        return (java.lang.Boolean) datum_;
      }
      return false;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (hasDatumPoint()) {
        if (!getDatumPoint().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, columnName_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, columnType_);
      }
      if (datumCase_ == 3) {
        output.writeInt32(
            3, (int)((java.lang.Integer) datum_));
      }
      if (datumCase_ == 4) {
        output.writeInt64(
            4, (long)((java.lang.Long) datum_));
      }
      if (datumCase_ == 5) {
        output.writeFloat(
            5, (float)((java.lang.Float) datum_));
      }
      if (datumCase_ == 6) {
        output.writeDouble(
            6, (double)((java.lang.Double) datum_));
      }
      if (datumCase_ == 7) {
        output.writeBool(
            7, (boolean)((java.lang.Boolean) datum_));
      }
      if (datumCase_ == 8) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, datum_);
      }
      if (datumCase_ == 9) {
        output.writeBytes(
            9, (com.google.protobuf.ByteString) datum_);
      }
      if (datumCase_ == 10) {
        output.writeMessage(10, (io.debezium.connector.postgresql.proto.PgProto.Point) datum_);
      }
      if (datumCase_ == 11) {
        output.writeBool(
            11, (boolean)((java.lang.Boolean) datum_));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, columnName_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, columnType_);
      }
      if (datumCase_ == 3) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(
              3, (int)((java.lang.Integer) datum_));
      }
      if (datumCase_ == 4) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(
              4, (long)((java.lang.Long) datum_));
      }
      if (datumCase_ == 5) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(
              5, (float)((java.lang.Float) datum_));
      }
      if (datumCase_ == 6) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(
              6, (double)((java.lang.Double) datum_));
      }
      if (datumCase_ == 7) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(
              7, (boolean)((java.lang.Boolean) datum_));
      }
      if (datumCase_ == 8) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, datum_);
      }
      if (datumCase_ == 9) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(
              9, (com.google.protobuf.ByteString) datum_);
      }
      if (datumCase_ == 10) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(10, (io.debezium.connector.postgresql.proto.PgProto.Point) datum_);
      }
      if (datumCase_ == 11) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(
              11, (boolean)((java.lang.Boolean) datum_));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof io.debezium.connector.postgresql.proto.PgProto.DatumMessage)) {
        return super.equals(obj);
      }
      io.debezium.connector.postgresql.proto.PgProto.DatumMessage other = (io.debezium.connector.postgresql.proto.PgProto.DatumMessage) obj;

      if (hasColumnName() != other.hasColumnName()) return false;
      if (hasColumnName()) {
        if (!getColumnName()
            .equals(other.getColumnName())) return false;
      }
      if (hasColumnType() != other.hasColumnType()) return false;
      if (hasColumnType()) {
        if (getColumnType()
            != other.getColumnType()) return false;
      }
      if (!getDatumCase().equals(other.getDatumCase())) return false;
      switch (datumCase_) {
        case 3:
          if (getDatumInt32()
              != other.getDatumInt32()) return false;
          break;
        case 4:
          if (getDatumInt64()
              != other.getDatumInt64()) return false;
          break;
        case 5:
          if (java.lang.Float.floatToIntBits(getDatumFloat())
              != java.lang.Float.floatToIntBits(
                  other.getDatumFloat())) return false;
          break;
        case 6:
          if (java.lang.Double.doubleToLongBits(getDatumDouble())
              != java.lang.Double.doubleToLongBits(
                  other.getDatumDouble())) return false;
          break;
        case 7:
          if (getDatumBool()
              != other.getDatumBool()) return false;
          break;
        case 8:
          if (!getDatumString()
              .equals(other.getDatumString())) return false;
          break;
        case 9:
          if (!getDatumBytes()
              .equals(other.getDatumBytes())) return false;
          break;
        case 10:
          if (!getDatumPoint()
              .equals(other.getDatumPoint())) return false;
          break;
        case 11:
          if (getDatumMissing()
              != other.getDatumMissing()) return false;
          break;
        case 0:
        default:
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasColumnName()) {
        hash = (37 * hash) + COLUMN_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getColumnName().hashCode();
      }
      if (hasColumnType()) {
        hash = (37 * hash) + COLUMN_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getColumnType());
      }
      switch (datumCase_) {
        case 3:
          hash = (37 * hash) + DATUM_INT32_FIELD_NUMBER;
          hash = (53 * hash) + getDatumInt32();
          break;
        case 4:
          hash = (37 * hash) + DATUM_INT64_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getDatumInt64());
          break;
        case 5:
          hash = (37 * hash) + DATUM_FLOAT_FIELD_NUMBER;
          hash = (53 * hash) + java.lang.Float.floatToIntBits(
              getDatumFloat());
          break;
        case 6:
          hash = (37 * hash) + DATUM_DOUBLE_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              java.lang.Double.doubleToLongBits(getDatumDouble()));
          break;
        case 7:
          hash = (37 * hash) + DATUM_BOOL_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getDatumBool());
          break;
        case 8:
          hash = (37 * hash) + DATUM_STRING_FIELD_NUMBER;
          hash = (53 * hash) + getDatumString().hashCode();
          break;
        case 9:
          hash = (37 * hash) + DATUM_BYTES_FIELD_NUMBER;
          hash = (53 * hash) + getDatumBytes().hashCode();
          break;
        case 10:
          hash = (37 * hash) + DATUM_POINT_FIELD_NUMBER;
          hash = (53 * hash) + getDatumPoint().hashCode();
          break;
        case 11:
          hash = (37 * hash) + DATUM_MISSING_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getDatumMissing());
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(io.debezium.connector.postgresql.proto.PgProto.DatumMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code decoderbufs.DatumMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:decoderbufs.DatumMessage)
        io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_DatumMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_DatumMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                io.debezium.connector.postgresql.proto.PgProto.DatumMessage.class, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder.class);
      }

      // Construct using io.debezium.connector.postgresql.proto.PgProto.DatumMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        columnName_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        columnType_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        datumCase_ = 0;
        datum_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_DatumMessage_descriptor;
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessage getDefaultInstanceForType() {
        return io.debezium.connector.postgresql.proto.PgProto.DatumMessage.getDefaultInstance();
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessage build() {
        io.debezium.connector.postgresql.proto.PgProto.DatumMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessage buildPartial() {
        io.debezium.connector.postgresql.proto.PgProto.DatumMessage result = new io.debezium.connector.postgresql.proto.PgProto.DatumMessage(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.columnName_ = columnName_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.columnType_ = columnType_;
          to_bitField0_ |= 0x00000002;
        }
        if (datumCase_ == 3) {
          result.datum_ = datum_;
        }
        if (datumCase_ == 4) {
          result.datum_ = datum_;
        }
        if (datumCase_ == 5) {
          result.datum_ = datum_;
        }
        if (datumCase_ == 6) {
          result.datum_ = datum_;
        }
        if (datumCase_ == 7) {
          result.datum_ = datum_;
        }
        if (datumCase_ == 8) {
          result.datum_ = datum_;
        }
        if (datumCase_ == 9) {
          result.datum_ = datum_;
        }
        if (datumCase_ == 10) {
          if (datumPointBuilder_ == null) {
            result.datum_ = datum_;
          } else {
            result.datum_ = datumPointBuilder_.build();
          }
        }
        if (datumCase_ == 11) {
          result.datum_ = datum_;
        }
        result.bitField0_ = to_bitField0_;
        result.datumCase_ = datumCase_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof io.debezium.connector.postgresql.proto.PgProto.DatumMessage) {
          return mergeFrom((io.debezium.connector.postgresql.proto.PgProto.DatumMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(io.debezium.connector.postgresql.proto.PgProto.DatumMessage other) {
        if (other == io.debezium.connector.postgresql.proto.PgProto.DatumMessage.getDefaultInstance()) return this;
        if (other.hasColumnName()) {
          bitField0_ |= 0x00000001;
          columnName_ = other.columnName_;
          onChanged();
        }
        if (other.hasColumnType()) {
          setColumnType(other.getColumnType());
        }
        switch (other.getDatumCase()) {
          case DATUM_INT32: {
            setDatumInt32(other.getDatumInt32());
            break;
          }
          case DATUM_INT64: {
            setDatumInt64(other.getDatumInt64());
            break;
          }
          case DATUM_FLOAT: {
            setDatumFloat(other.getDatumFloat());
            break;
          }
          case DATUM_DOUBLE: {
            setDatumDouble(other.getDatumDouble());
            break;
          }
          case DATUM_BOOL: {
            setDatumBool(other.getDatumBool());
            break;
          }
          case DATUM_STRING: {
            datumCase_ = 8;
            datum_ = other.datum_;
            onChanged();
            break;
          }
          case DATUM_BYTES: {
            setDatumBytes(other.getDatumBytes());
            break;
          }
          case DATUM_POINT: {
            mergeDatumPoint(other.getDatumPoint());
            break;
          }
          case DATUM_MISSING: {
            setDatumMissing(other.getDatumMissing());
            break;
          }
          case DATUM_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (hasDatumPoint()) {
          if (!getDatumPoint().isInitialized()) {
            return false;
          }
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        io.debezium.connector.postgresql.proto.PgProto.DatumMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (io.debezium.connector.postgresql.proto.PgProto.DatumMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int datumCase_ = 0;
      private java.lang.Object datum_;
      public DatumCase
          getDatumCase() {
        return DatumCase.forNumber(
            datumCase_);
      }

      public Builder clearDatum() {
        datumCase_ = 0;
        datum_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      private java.lang.Object columnName_ = "";
      /**
       * <code>optional string column_name = 1;</code>
       */
      public boolean hasColumnName() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string column_name = 1;</code>
       */
      public java.lang.String getColumnName() {
        java.lang.Object ref = columnName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            columnName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string column_name = 1;</code>
       */
      public com.google.protobuf.ByteString
          getColumnNameBytes() {
        java.lang.Object ref = columnName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          columnName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string column_name = 1;</code>
       */
      public Builder setColumnName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        columnName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string column_name = 1;</code>
       */
      public Builder clearColumnName() {
        bitField0_ = (bitField0_ & ~0x00000001);
        columnName_ = getDefaultInstance().getColumnName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string column_name = 1;</code>
       */
      public Builder setColumnNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        columnName_ = value;
        onChanged();
        return this;
      }

      private long columnType_ ;
      /**
       * <code>optional int64 column_type = 2;</code>
       */
      public boolean hasColumnType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 column_type = 2;</code>
       */
      public long getColumnType() {
        return columnType_;
      }
      /**
       * <code>optional int64 column_type = 2;</code>
       */
      public Builder setColumnType(long value) {
        bitField0_ |= 0x00000002;
        columnType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 column_type = 2;</code>
       */
      public Builder clearColumnType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        columnType_ = 0L;
        onChanged();
        return this;
      }

      /**
       * <code>optional int32 datum_int32 = 3;</code>
       */
      public boolean hasDatumInt32() {
        return datumCase_ == 3;
      }
      /**
       * <code>optional int32 datum_int32 = 3;</code>
       */
      public int getDatumInt32() {
        if (datumCase_ == 3) {
          return (java.lang.Integer) datum_;
        }
        return 0;
      }
      /**
       * <code>optional int32 datum_int32 = 3;</code>
       */
      public Builder setDatumInt32(int value) {
        datumCase_ = 3;
        datum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 datum_int32 = 3;</code>
       */
      public Builder clearDatumInt32() {
        if (datumCase_ == 3) {
          datumCase_ = 0;
          datum_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>optional int64 datum_int64 = 4;</code>
       */
      public boolean hasDatumInt64() {
        return datumCase_ == 4;
      }
      /**
       * <code>optional int64 datum_int64 = 4;</code>
       */
      public long getDatumInt64() {
        if (datumCase_ == 4) {
          return (java.lang.Long) datum_;
        }
        return 0L;
      }
      /**
       * <code>optional int64 datum_int64 = 4;</code>
       */
      public Builder setDatumInt64(long value) {
        datumCase_ = 4;
        datum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 datum_int64 = 4;</code>
       */
      public Builder clearDatumInt64() {
        if (datumCase_ == 4) {
          datumCase_ = 0;
          datum_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>optional float datum_float = 5;</code>
       */
      public boolean hasDatumFloat() {
        return datumCase_ == 5;
      }
      /**
       * <code>optional float datum_float = 5;</code>
       */
      public float getDatumFloat() {
        if (datumCase_ == 5) {
          return (java.lang.Float) datum_;
        }
        return 0F;
      }
      /**
       * <code>optional float datum_float = 5;</code>
       */
      public Builder setDatumFloat(float value) {
        datumCase_ = 5;
        datum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional float datum_float = 5;</code>
       */
      public Builder clearDatumFloat() {
        if (datumCase_ == 5) {
          datumCase_ = 0;
          datum_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>optional double datum_double = 6;</code>
       */
      public boolean hasDatumDouble() {
        return datumCase_ == 6;
      }
      /**
       * <code>optional double datum_double = 6;</code>
       */
      public double getDatumDouble() {
        if (datumCase_ == 6) {
          return (java.lang.Double) datum_;
        }
        return 0D;
      }
      /**
       * <code>optional double datum_double = 6;</code>
       */
      public Builder setDatumDouble(double value) {
        datumCase_ = 6;
        datum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional double datum_double = 6;</code>
       */
      public Builder clearDatumDouble() {
        if (datumCase_ == 6) {
          datumCase_ = 0;
          datum_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>optional bool datum_bool = 7;</code>
       */
      public boolean hasDatumBool() {
        return datumCase_ == 7;
      }
      /**
       * <code>optional bool datum_bool = 7;</code>
       */
      public boolean getDatumBool() {
        if (datumCase_ == 7) {
          return (java.lang.Boolean) datum_;
        }
        return false;
      }
      /**
       * <code>optional bool datum_bool = 7;</code>
       */
      public Builder setDatumBool(boolean value) {
        datumCase_ = 7;
        datum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool datum_bool = 7;</code>
       */
      public Builder clearDatumBool() {
        if (datumCase_ == 7) {
          datumCase_ = 0;
          datum_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>optional string datum_string = 8;</code>
       */
      public boolean hasDatumString() {
        return datumCase_ == 8;
      }
      /**
       * <code>optional string datum_string = 8;</code>
       */
      public java.lang.String getDatumString() {
        java.lang.Object ref = "";
        if (datumCase_ == 8) {
          ref = datum_;
        }
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (datumCase_ == 8) {
            if (bs.isValidUtf8()) {
              datum_ = s;
            }
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string datum_string = 8;</code>
       */
      public com.google.protobuf.ByteString
          getDatumStringBytes() {
        java.lang.Object ref = "";
        if (datumCase_ == 8) {
          ref = datum_;
        }
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          if (datumCase_ == 8) {
            datum_ = b;
          }
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string datum_string = 8;</code>
       */
      public Builder setDatumString(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  datumCase_ = 8;
        datum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string datum_string = 8;</code>
       */
      public Builder clearDatumString() {
        if (datumCase_ == 8) {
          datumCase_ = 0;
          datum_ = null;
          onChanged();
        }
        return this;
      }
      /**
       * <code>optional string datum_string = 8;</code>
       */
      public Builder setDatumStringBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  datumCase_ = 8;
        datum_ = value;
        onChanged();
        return this;
      }

      /**
       * <code>optional bytes datum_bytes = 9;</code>
       */
      public boolean hasDatumBytes() {
        return datumCase_ == 9;
      }
      /**
       * <code>optional bytes datum_bytes = 9;</code>
       */
      public com.google.protobuf.ByteString getDatumBytes() {
        if (datumCase_ == 9) {
          return (com.google.protobuf.ByteString) datum_;
        }
        return com.google.protobuf.ByteString.EMPTY;
      }
      /**
       * <code>optional bytes datum_bytes = 9;</code>
       */
      public Builder setDatumBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  datumCase_ = 9;
        datum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes datum_bytes = 9;</code>
       */
      public Builder clearDatumBytes() {
        if (datumCase_ == 9) {
          datumCase_ = 0;
          datum_ = null;
          onChanged();
        }
        return this;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          io.debezium.connector.postgresql.proto.PgProto.Point, io.debezium.connector.postgresql.proto.PgProto.Point.Builder, io.debezium.connector.postgresql.proto.PgProto.PointOrBuilder> datumPointBuilder_;
      /**
       * <code>optional .decoderbufs.Point datum_point = 10;</code>
       */
      public boolean hasDatumPoint() {
        return datumCase_ == 10;
      }
      /**
       * <code>optional .decoderbufs.Point datum_point = 10;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.Point getDatumPoint() {
        if (datumPointBuilder_ == null) {
          if (datumCase_ == 10) {
            return (io.debezium.connector.postgresql.proto.PgProto.Point) datum_;
          }
          return io.debezium.connector.postgresql.proto.PgProto.Point.getDefaultInstance();
        } else {
          if (datumCase_ == 10) {
            return datumPointBuilder_.getMessage();
          }
          return io.debezium.connector.postgresql.proto.PgProto.Point.getDefaultInstance();
        }
      }
      /**
       * <code>optional .decoderbufs.Point datum_point = 10;</code>
       */
      public Builder setDatumPoint(io.debezium.connector.postgresql.proto.PgProto.Point value) {
        if (datumPointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          datum_ = value;
          onChanged();
        } else {
          datumPointBuilder_.setMessage(value);
        }
        datumCase_ = 10;
        return this;
      }
      /**
       * <code>optional .decoderbufs.Point datum_point = 10;</code>
       */
      public Builder setDatumPoint(
          io.debezium.connector.postgresql.proto.PgProto.Point.Builder builderForValue) {
        if (datumPointBuilder_ == null) {
          datum_ = builderForValue.build();
          onChanged();
        } else {
          datumPointBuilder_.setMessage(builderForValue.build());
        }
        datumCase_ = 10;
        return this;
      }
      /**
       * <code>optional .decoderbufs.Point datum_point = 10;</code>
       */
      public Builder mergeDatumPoint(io.debezium.connector.postgresql.proto.PgProto.Point value) {
        if (datumPointBuilder_ == null) {
          if (datumCase_ == 10 &&
              datum_ != io.debezium.connector.postgresql.proto.PgProto.Point.getDefaultInstance()) {
            datum_ = io.debezium.connector.postgresql.proto.PgProto.Point.newBuilder((io.debezium.connector.postgresql.proto.PgProto.Point) datum_)
                .mergeFrom(value).buildPartial();
          } else {
            datum_ = value;
          }
          onChanged();
        } else {
          if (datumCase_ == 10) {
            datumPointBuilder_.mergeFrom(value);
          }
          datumPointBuilder_.setMessage(value);
        }
        datumCase_ = 10;
        return this;
      }
      /**
       * <code>optional .decoderbufs.Point datum_point = 10;</code>
       */
      public Builder clearDatumPoint() {
        if (datumPointBuilder_ == null) {
          if (datumCase_ == 10) {
            datumCase_ = 0;
            datum_ = null;
            onChanged();
          }
        } else {
          if (datumCase_ == 10) {
            datumCase_ = 0;
            datum_ = null;
          }
          datumPointBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>optional .decoderbufs.Point datum_point = 10;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.Point.Builder getDatumPointBuilder() {
        return getDatumPointFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .decoderbufs.Point datum_point = 10;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.PointOrBuilder getDatumPointOrBuilder() {
        if ((datumCase_ == 10) && (datumPointBuilder_ != null)) {
          return datumPointBuilder_.getMessageOrBuilder();
        } else {
          if (datumCase_ == 10) {
            return (io.debezium.connector.postgresql.proto.PgProto.Point) datum_;
          }
          return io.debezium.connector.postgresql.proto.PgProto.Point.getDefaultInstance();
        }
      }
      /**
       * <code>optional .decoderbufs.Point datum_point = 10;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          io.debezium.connector.postgresql.proto.PgProto.Point, io.debezium.connector.postgresql.proto.PgProto.Point.Builder, io.debezium.connector.postgresql.proto.PgProto.PointOrBuilder> 
          getDatumPointFieldBuilder() {
        if (datumPointBuilder_ == null) {
          if (!(datumCase_ == 10)) {
            datum_ = io.debezium.connector.postgresql.proto.PgProto.Point.getDefaultInstance();
          }
          datumPointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              io.debezium.connector.postgresql.proto.PgProto.Point, io.debezium.connector.postgresql.proto.PgProto.Point.Builder, io.debezium.connector.postgresql.proto.PgProto.PointOrBuilder>(
                  (io.debezium.connector.postgresql.proto.PgProto.Point) datum_,
                  getParentForChildren(),
                  isClean());
          datum_ = null;
        }
        datumCase_ = 10;
        onChanged();;
        return datumPointBuilder_;
      }

      /**
       * <code>optional bool datum_missing = 11;</code>
       */
      public boolean hasDatumMissing() {
        return datumCase_ == 11;
      }
      /**
       * <code>optional bool datum_missing = 11;</code>
       */
      public boolean getDatumMissing() {
        if (datumCase_ == 11) {
          return (java.lang.Boolean) datum_;
        }
        return false;
      }
      /**
       * <code>optional bool datum_missing = 11;</code>
       */
      public Builder setDatumMissing(boolean value) {
        datumCase_ = 11;
        datum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool datum_missing = 11;</code>
       */
      public Builder clearDatumMissing() {
        if (datumCase_ == 11) {
          datumCase_ = 0;
          datum_ = null;
          onChanged();
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:decoderbufs.DatumMessage)
    }

    // @@protoc_insertion_point(class_scope:decoderbufs.DatumMessage)
    private static final io.debezium.connector.postgresql.proto.PgProto.DatumMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new io.debezium.connector.postgresql.proto.PgProto.DatumMessage();
    }

    public static io.debezium.connector.postgresql.proto.PgProto.DatumMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DatumMessage>
        PARSER = new com.google.protobuf.AbstractParser<DatumMessage>() {
      @java.lang.Override
      public DatumMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DatumMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DatumMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DatumMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public io.debezium.connector.postgresql.proto.PgProto.DatumMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TypeInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:decoderbufs.TypeInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>required string modifier = 1;</code>
     */
    boolean hasModifier();
    /**
     * <code>required string modifier = 1;</code>
     */
    java.lang.String getModifier();
    /**
     * <code>required string modifier = 1;</code>
     */
    com.google.protobuf.ByteString
        getModifierBytes();

    /**
     * <code>required bool value_optional = 2;</code>
     */
    boolean hasValueOptional();
    /**
     * <code>required bool value_optional = 2;</code>
     */
    boolean getValueOptional();
  }
  /**
   * Protobuf type {@code decoderbufs.TypeInfo}
   */
  public  static final class TypeInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:decoderbufs.TypeInfo)
      TypeInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TypeInfo.newBuilder() to construct.
    private TypeInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TypeInfo() {
      modifier_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TypeInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TypeInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              modifier_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              valueOptional_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_TypeInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_TypeInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              io.debezium.connector.postgresql.proto.PgProto.TypeInfo.class, io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder.class);
    }

    private int bitField0_;
    public static final int MODIFIER_FIELD_NUMBER = 1;
    private volatile java.lang.Object modifier_;
    /**
     * <code>required string modifier = 1;</code>
     */
    public boolean hasModifier() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required string modifier = 1;</code>
     */
    public java.lang.String getModifier() {
      java.lang.Object ref = modifier_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          modifier_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string modifier = 1;</code>
     */
    public com.google.protobuf.ByteString
        getModifierBytes() {
      java.lang.Object ref = modifier_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        modifier_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VALUE_OPTIONAL_FIELD_NUMBER = 2;
    private boolean valueOptional_;
    /**
     * <code>required bool value_optional = 2;</code>
     */
    public boolean hasValueOptional() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required bool value_optional = 2;</code>
     */
    public boolean getValueOptional() {
      return valueOptional_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasModifier()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasValueOptional()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, modifier_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(2, valueOptional_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, modifier_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, valueOptional_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof io.debezium.connector.postgresql.proto.PgProto.TypeInfo)) {
        return super.equals(obj);
      }
      io.debezium.connector.postgresql.proto.PgProto.TypeInfo other = (io.debezium.connector.postgresql.proto.PgProto.TypeInfo) obj;

      if (hasModifier() != other.hasModifier()) return false;
      if (hasModifier()) {
        if (!getModifier()
            .equals(other.getModifier())) return false;
      }
      if (hasValueOptional() != other.hasValueOptional()) return false;
      if (hasValueOptional()) {
        if (getValueOptional()
            != other.getValueOptional()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasModifier()) {
        hash = (37 * hash) + MODIFIER_FIELD_NUMBER;
        hash = (53 * hash) + getModifier().hashCode();
      }
      if (hasValueOptional()) {
        hash = (37 * hash) + VALUE_OPTIONAL_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getValueOptional());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(io.debezium.connector.postgresql.proto.PgProto.TypeInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code decoderbufs.TypeInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:decoderbufs.TypeInfo)
        io.debezium.connector.postgresql.proto.PgProto.TypeInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_TypeInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_TypeInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                io.debezium.connector.postgresql.proto.PgProto.TypeInfo.class, io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder.class);
      }

      // Construct using io.debezium.connector.postgresql.proto.PgProto.TypeInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        modifier_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        valueOptional_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_TypeInfo_descriptor;
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.TypeInfo getDefaultInstanceForType() {
        return io.debezium.connector.postgresql.proto.PgProto.TypeInfo.getDefaultInstance();
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.TypeInfo build() {
        io.debezium.connector.postgresql.proto.PgProto.TypeInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.TypeInfo buildPartial() {
        io.debezium.connector.postgresql.proto.PgProto.TypeInfo result = new io.debezium.connector.postgresql.proto.PgProto.TypeInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.modifier_ = modifier_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.valueOptional_ = valueOptional_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof io.debezium.connector.postgresql.proto.PgProto.TypeInfo) {
          return mergeFrom((io.debezium.connector.postgresql.proto.PgProto.TypeInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(io.debezium.connector.postgresql.proto.PgProto.TypeInfo other) {
        if (other == io.debezium.connector.postgresql.proto.PgProto.TypeInfo.getDefaultInstance()) return this;
        if (other.hasModifier()) {
          bitField0_ |= 0x00000001;
          modifier_ = other.modifier_;
          onChanged();
        }
        if (other.hasValueOptional()) {
          setValueOptional(other.getValueOptional());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasModifier()) {
          return false;
        }
        if (!hasValueOptional()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        io.debezium.connector.postgresql.proto.PgProto.TypeInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (io.debezium.connector.postgresql.proto.PgProto.TypeInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object modifier_ = "";
      /**
       * <code>required string modifier = 1;</code>
       */
      public boolean hasModifier() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>required string modifier = 1;</code>
       */
      public java.lang.String getModifier() {
        java.lang.Object ref = modifier_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            modifier_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string modifier = 1;</code>
       */
      public com.google.protobuf.ByteString
          getModifierBytes() {
        java.lang.Object ref = modifier_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          modifier_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string modifier = 1;</code>
       */
      public Builder setModifier(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        modifier_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string modifier = 1;</code>
       */
      public Builder clearModifier() {
        bitField0_ = (bitField0_ & ~0x00000001);
        modifier_ = getDefaultInstance().getModifier();
        onChanged();
        return this;
      }
      /**
       * <code>required string modifier = 1;</code>
       */
      public Builder setModifierBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        modifier_ = value;
        onChanged();
        return this;
      }

      private boolean valueOptional_ ;
      /**
       * <code>required bool value_optional = 2;</code>
       */
      public boolean hasValueOptional() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>required bool value_optional = 2;</code>
       */
      public boolean getValueOptional() {
        return valueOptional_;
      }
      /**
       * <code>required bool value_optional = 2;</code>
       */
      public Builder setValueOptional(boolean value) {
        bitField0_ |= 0x00000002;
        valueOptional_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool value_optional = 2;</code>
       */
      public Builder clearValueOptional() {
        bitField0_ = (bitField0_ & ~0x00000002);
        valueOptional_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:decoderbufs.TypeInfo)
    }

    // @@protoc_insertion_point(class_scope:decoderbufs.TypeInfo)
    private static final io.debezium.connector.postgresql.proto.PgProto.TypeInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new io.debezium.connector.postgresql.proto.PgProto.TypeInfo();
    }

    public static io.debezium.connector.postgresql.proto.PgProto.TypeInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TypeInfo>
        PARSER = new com.google.protobuf.AbstractParser<TypeInfo>() {
      @java.lang.Override
      public TypeInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TypeInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TypeInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TypeInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public io.debezium.connector.postgresql.proto.PgProto.TypeInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RowMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:decoderbufs.RowMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional uint32 transaction_id = 1;</code>
     */
    boolean hasTransactionId();
    /**
     * <code>optional uint32 transaction_id = 1;</code>
     */
    int getTransactionId();

    /**
     * <code>optional uint64 commit_time = 2;</code>
     */
    boolean hasCommitTime();
    /**
     * <code>optional uint64 commit_time = 2;</code>
     */
    long getCommitTime();

    /**
     * <code>optional string table = 3;</code>
     */
    boolean hasTable();
    /**
     * <code>optional string table = 3;</code>
     */
    java.lang.String getTable();
    /**
     * <code>optional string table = 3;</code>
     */
    com.google.protobuf.ByteString
        getTableBytes();

    /**
     * <code>optional .decoderbufs.Op op = 4;</code>
     */
    boolean hasOp();
    /**
     * <code>optional .decoderbufs.Op op = 4;</code>
     */
    io.debezium.connector.postgresql.proto.PgProto.Op getOp();

    /**
     * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
     */
    java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage> 
        getNewTupleList();
    /**
     * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
     */
    io.debezium.connector.postgresql.proto.PgProto.DatumMessage getNewTuple(int index);
    /**
     * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
     */
    int getNewTupleCount();
    /**
     * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
     */
    java.util.List<? extends io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder> 
        getNewTupleOrBuilderList();
    /**
     * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
     */
    io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder getNewTupleOrBuilder(
        int index);

    /**
     * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
     */
    java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage> 
        getOldTupleList();
    /**
     * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
     */
    io.debezium.connector.postgresql.proto.PgProto.DatumMessage getOldTuple(int index);
    /**
     * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
     */
    int getOldTupleCount();
    /**
     * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
     */
    java.util.List<? extends io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder> 
        getOldTupleOrBuilderList();
    /**
     * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
     */
    io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder getOldTupleOrBuilder(
        int index);

    /**
     * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
     */
    java.util.List<io.debezium.connector.postgresql.proto.PgProto.TypeInfo> 
        getNewTypeinfoList();
    /**
     * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
     */
    io.debezium.connector.postgresql.proto.PgProto.TypeInfo getNewTypeinfo(int index);
    /**
     * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
     */
    int getNewTypeinfoCount();
    /**
     * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
     */
    java.util.List<? extends io.debezium.connector.postgresql.proto.PgProto.TypeInfoOrBuilder> 
        getNewTypeinfoOrBuilderList();
    /**
     * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
     */
    io.debezium.connector.postgresql.proto.PgProto.TypeInfoOrBuilder getNewTypeinfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code decoderbufs.RowMessage}
   */
  public  static final class RowMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:decoderbufs.RowMessage)
      RowMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RowMessage.newBuilder() to construct.
    private RowMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RowMessage() {
      table_ = "";
      op_ = -1;
      newTuple_ = java.util.Collections.emptyList();
      oldTuple_ = java.util.Collections.emptyList();
      newTypeinfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RowMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RowMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              transactionId_ = input.readUInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              commitTime_ = input.readUInt64();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              table_ = bs;
              break;
            }
            case 32: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              io.debezium.connector.postgresql.proto.PgProto.Op value = io.debezium.connector.postgresql.proto.PgProto.Op.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(4, rawValue);
              } else {
                bitField0_ |= 0x00000008;
                op_ = rawValue;
              }
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) != 0)) {
                newTuple_ = new java.util.ArrayList<io.debezium.connector.postgresql.proto.PgProto.DatumMessage>();
                mutable_bitField0_ |= 0x00000010;
              }
              newTuple_.add(
                  input.readMessage(io.debezium.connector.postgresql.proto.PgProto.DatumMessage.PARSER, extensionRegistry));
              break;
            }
            case 50: {
              if (!((mutable_bitField0_ & 0x00000020) != 0)) {
                oldTuple_ = new java.util.ArrayList<io.debezium.connector.postgresql.proto.PgProto.DatumMessage>();
                mutable_bitField0_ |= 0x00000020;
              }
              oldTuple_.add(
                  input.readMessage(io.debezium.connector.postgresql.proto.PgProto.DatumMessage.PARSER, extensionRegistry));
              break;
            }
            case 58: {
              if (!((mutable_bitField0_ & 0x00000040) != 0)) {
                newTypeinfo_ = new java.util.ArrayList<io.debezium.connector.postgresql.proto.PgProto.TypeInfo>();
                mutable_bitField0_ |= 0x00000040;
              }
              newTypeinfo_.add(
                  input.readMessage(io.debezium.connector.postgresql.proto.PgProto.TypeInfo.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000010) != 0)) {
          newTuple_ = java.util.Collections.unmodifiableList(newTuple_);
        }
        if (((mutable_bitField0_ & 0x00000020) != 0)) {
          oldTuple_ = java.util.Collections.unmodifiableList(oldTuple_);
        }
        if (((mutable_bitField0_ & 0x00000040) != 0)) {
          newTypeinfo_ = java.util.Collections.unmodifiableList(newTypeinfo_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_RowMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_RowMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              io.debezium.connector.postgresql.proto.PgProto.RowMessage.class, io.debezium.connector.postgresql.proto.PgProto.RowMessage.Builder.class);
    }

    private int bitField0_;
    public static final int TRANSACTION_ID_FIELD_NUMBER = 1;
    private int transactionId_;
    /**
     * <code>optional uint32 transaction_id = 1;</code>
     */
    public boolean hasTransactionId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint32 transaction_id = 1;</code>
     */
    public int getTransactionId() {
      return transactionId_;
    }

    public static final int COMMIT_TIME_FIELD_NUMBER = 2;
    private long commitTime_;
    /**
     * <code>optional uint64 commit_time = 2;</code>
     */
    public boolean hasCommitTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint64 commit_time = 2;</code>
     */
    public long getCommitTime() {
      return commitTime_;
    }

    public static final int TABLE_FIELD_NUMBER = 3;
    private volatile java.lang.Object table_;
    /**
     * <code>optional string table = 3;</code>
     */
    public boolean hasTable() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string table = 3;</code>
     */
    public java.lang.String getTable() {
      java.lang.Object ref = table_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          table_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string table = 3;</code>
     */
    public com.google.protobuf.ByteString
        getTableBytes() {
      java.lang.Object ref = table_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        table_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OP_FIELD_NUMBER = 4;
    private int op_;
    /**
     * <code>optional .decoderbufs.Op op = 4;</code>
     */
    public boolean hasOp() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .decoderbufs.Op op = 4;</code>
     */
    public io.debezium.connector.postgresql.proto.PgProto.Op getOp() {
      @SuppressWarnings("deprecation")
      io.debezium.connector.postgresql.proto.PgProto.Op result = io.debezium.connector.postgresql.proto.PgProto.Op.valueOf(op_);
      return result == null ? io.debezium.connector.postgresql.proto.PgProto.Op.UNKNOWN : result;
    }

    public static final int NEW_TUPLE_FIELD_NUMBER = 5;
    private java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage> newTuple_;
    /**
     * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
     */
    public java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage> getNewTupleList() {
      return newTuple_;
    }
    /**
     * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
     */
    public java.util.List<? extends io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder> 
        getNewTupleOrBuilderList() {
      return newTuple_;
    }
    /**
     * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
     */
    public int getNewTupleCount() {
      return newTuple_.size();
    }
    /**
     * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
     */
    public io.debezium.connector.postgresql.proto.PgProto.DatumMessage getNewTuple(int index) {
      return newTuple_.get(index);
    }
    /**
     * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
     */
    public io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder getNewTupleOrBuilder(
        int index) {
      return newTuple_.get(index);
    }

    public static final int OLD_TUPLE_FIELD_NUMBER = 6;
    private java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage> oldTuple_;
    /**
     * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
     */
    public java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage> getOldTupleList() {
      return oldTuple_;
    }
    /**
     * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
     */
    public java.util.List<? extends io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder> 
        getOldTupleOrBuilderList() {
      return oldTuple_;
    }
    /**
     * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
     */
    public int getOldTupleCount() {
      return oldTuple_.size();
    }
    /**
     * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
     */
    public io.debezium.connector.postgresql.proto.PgProto.DatumMessage getOldTuple(int index) {
      return oldTuple_.get(index);
    }
    /**
     * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
     */
    public io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder getOldTupleOrBuilder(
        int index) {
      return oldTuple_.get(index);
    }

    public static final int NEW_TYPEINFO_FIELD_NUMBER = 7;
    private java.util.List<io.debezium.connector.postgresql.proto.PgProto.TypeInfo> newTypeinfo_;
    /**
     * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
     */
    public java.util.List<io.debezium.connector.postgresql.proto.PgProto.TypeInfo> getNewTypeinfoList() {
      return newTypeinfo_;
    }
    /**
     * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
     */
    public java.util.List<? extends io.debezium.connector.postgresql.proto.PgProto.TypeInfoOrBuilder> 
        getNewTypeinfoOrBuilderList() {
      return newTypeinfo_;
    }
    /**
     * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
     */
    public int getNewTypeinfoCount() {
      return newTypeinfo_.size();
    }
    /**
     * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
     */
    public io.debezium.connector.postgresql.proto.PgProto.TypeInfo getNewTypeinfo(int index) {
      return newTypeinfo_.get(index);
    }
    /**
     * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
     */
    public io.debezium.connector.postgresql.proto.PgProto.TypeInfoOrBuilder getNewTypeinfoOrBuilder(
        int index) {
      return newTypeinfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      for (int i = 0; i < getNewTupleCount(); i++) {
        if (!getNewTuple(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      for (int i = 0; i < getOldTupleCount(); i++) {
        if (!getOldTuple(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      for (int i = 0; i < getNewTypeinfoCount(); i++) {
        if (!getNewTypeinfo(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(1, transactionId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt64(2, commitTime_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, table_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeEnum(4, op_);
      }
      for (int i = 0; i < newTuple_.size(); i++) {
        output.writeMessage(5, newTuple_.get(i));
      }
      for (int i = 0; i < oldTuple_.size(); i++) {
        output.writeMessage(6, oldTuple_.get(i));
      }
      for (int i = 0; i < newTypeinfo_.size(); i++) {
        output.writeMessage(7, newTypeinfo_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, transactionId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, commitTime_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, table_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, op_);
      }
      for (int i = 0; i < newTuple_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, newTuple_.get(i));
      }
      for (int i = 0; i < oldTuple_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, oldTuple_.get(i));
      }
      for (int i = 0; i < newTypeinfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, newTypeinfo_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof io.debezium.connector.postgresql.proto.PgProto.RowMessage)) {
        return super.equals(obj);
      }
      io.debezium.connector.postgresql.proto.PgProto.RowMessage other = (io.debezium.connector.postgresql.proto.PgProto.RowMessage) obj;

      if (hasTransactionId() != other.hasTransactionId()) return false;
      if (hasTransactionId()) {
        if (getTransactionId()
            != other.getTransactionId()) return false;
      }
      if (hasCommitTime() != other.hasCommitTime()) return false;
      if (hasCommitTime()) {
        if (getCommitTime()
            != other.getCommitTime()) return false;
      }
      if (hasTable() != other.hasTable()) return false;
      if (hasTable()) {
        if (!getTable()
            .equals(other.getTable())) return false;
      }
      if (hasOp() != other.hasOp()) return false;
      if (hasOp()) {
        if (op_ != other.op_) return false;
      }
      if (!getNewTupleList()
          .equals(other.getNewTupleList())) return false;
      if (!getOldTupleList()
          .equals(other.getOldTupleList())) return false;
      if (!getNewTypeinfoList()
          .equals(other.getNewTypeinfoList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTransactionId()) {
        hash = (37 * hash) + TRANSACTION_ID_FIELD_NUMBER;
        hash = (53 * hash) + getTransactionId();
      }
      if (hasCommitTime()) {
        hash = (37 * hash) + COMMIT_TIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCommitTime());
      }
      if (hasTable()) {
        hash = (37 * hash) + TABLE_FIELD_NUMBER;
        hash = (53 * hash) + getTable().hashCode();
      }
      if (hasOp()) {
        hash = (37 * hash) + OP_FIELD_NUMBER;
        hash = (53 * hash) + op_;
      }
      if (getNewTupleCount() > 0) {
        hash = (37 * hash) + NEW_TUPLE_FIELD_NUMBER;
        hash = (53 * hash) + getNewTupleList().hashCode();
      }
      if (getOldTupleCount() > 0) {
        hash = (37 * hash) + OLD_TUPLE_FIELD_NUMBER;
        hash = (53 * hash) + getOldTupleList().hashCode();
      }
      if (getNewTypeinfoCount() > 0) {
        hash = (37 * hash) + NEW_TYPEINFO_FIELD_NUMBER;
        hash = (53 * hash) + getNewTypeinfoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(io.debezium.connector.postgresql.proto.PgProto.RowMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code decoderbufs.RowMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:decoderbufs.RowMessage)
        io.debezium.connector.postgresql.proto.PgProto.RowMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_RowMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_RowMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                io.debezium.connector.postgresql.proto.PgProto.RowMessage.class, io.debezium.connector.postgresql.proto.PgProto.RowMessage.Builder.class);
      }

      // Construct using io.debezium.connector.postgresql.proto.PgProto.RowMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getNewTupleFieldBuilder();
          getOldTupleFieldBuilder();
          getNewTypeinfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        transactionId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        commitTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        table_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        op_ = -1;
        bitField0_ = (bitField0_ & ~0x00000008);
        if (newTupleBuilder_ == null) {
          newTuple_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
        } else {
          newTupleBuilder_.clear();
        }
        if (oldTupleBuilder_ == null) {
          oldTuple_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000020);
        } else {
          oldTupleBuilder_.clear();
        }
        if (newTypeinfoBuilder_ == null) {
          newTypeinfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);
        } else {
          newTypeinfoBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return io.debezium.connector.postgresql.proto.PgProto.internal_static_decoderbufs_RowMessage_descriptor;
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.RowMessage getDefaultInstanceForType() {
        return io.debezium.connector.postgresql.proto.PgProto.RowMessage.getDefaultInstance();
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.RowMessage build() {
        io.debezium.connector.postgresql.proto.PgProto.RowMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public io.debezium.connector.postgresql.proto.PgProto.RowMessage buildPartial() {
        io.debezium.connector.postgresql.proto.PgProto.RowMessage result = new io.debezium.connector.postgresql.proto.PgProto.RowMessage(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.transactionId_ = transactionId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commitTime_ = commitTime_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.table_ = table_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.op_ = op_;
        if (newTupleBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0)) {
            newTuple_ = java.util.Collections.unmodifiableList(newTuple_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.newTuple_ = newTuple_;
        } else {
          result.newTuple_ = newTupleBuilder_.build();
        }
        if (oldTupleBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0)) {
            oldTuple_ = java.util.Collections.unmodifiableList(oldTuple_);
            bitField0_ = (bitField0_ & ~0x00000020);
          }
          result.oldTuple_ = oldTuple_;
        } else {
          result.oldTuple_ = oldTupleBuilder_.build();
        }
        if (newTypeinfoBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0)) {
            newTypeinfo_ = java.util.Collections.unmodifiableList(newTypeinfo_);
            bitField0_ = (bitField0_ & ~0x00000040);
          }
          result.newTypeinfo_ = newTypeinfo_;
        } else {
          result.newTypeinfo_ = newTypeinfoBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof io.debezium.connector.postgresql.proto.PgProto.RowMessage) {
          return mergeFrom((io.debezium.connector.postgresql.proto.PgProto.RowMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(io.debezium.connector.postgresql.proto.PgProto.RowMessage other) {
        if (other == io.debezium.connector.postgresql.proto.PgProto.RowMessage.getDefaultInstance()) return this;
        if (other.hasTransactionId()) {
          setTransactionId(other.getTransactionId());
        }
        if (other.hasCommitTime()) {
          setCommitTime(other.getCommitTime());
        }
        if (other.hasTable()) {
          bitField0_ |= 0x00000004;
          table_ = other.table_;
          onChanged();
        }
        if (other.hasOp()) {
          setOp(other.getOp());
        }
        if (newTupleBuilder_ == null) {
          if (!other.newTuple_.isEmpty()) {
            if (newTuple_.isEmpty()) {
              newTuple_ = other.newTuple_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureNewTupleIsMutable();
              newTuple_.addAll(other.newTuple_);
            }
            onChanged();
          }
        } else {
          if (!other.newTuple_.isEmpty()) {
            if (newTupleBuilder_.isEmpty()) {
              newTupleBuilder_.dispose();
              newTupleBuilder_ = null;
              newTuple_ = other.newTuple_;
              bitField0_ = (bitField0_ & ~0x00000010);
              newTupleBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getNewTupleFieldBuilder() : null;
            } else {
              newTupleBuilder_.addAllMessages(other.newTuple_);
            }
          }
        }
        if (oldTupleBuilder_ == null) {
          if (!other.oldTuple_.isEmpty()) {
            if (oldTuple_.isEmpty()) {
              oldTuple_ = other.oldTuple_;
              bitField0_ = (bitField0_ & ~0x00000020);
            } else {
              ensureOldTupleIsMutable();
              oldTuple_.addAll(other.oldTuple_);
            }
            onChanged();
          }
        } else {
          if (!other.oldTuple_.isEmpty()) {
            if (oldTupleBuilder_.isEmpty()) {
              oldTupleBuilder_.dispose();
              oldTupleBuilder_ = null;
              oldTuple_ = other.oldTuple_;
              bitField0_ = (bitField0_ & ~0x00000020);
              oldTupleBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getOldTupleFieldBuilder() : null;
            } else {
              oldTupleBuilder_.addAllMessages(other.oldTuple_);
            }
          }
        }
        if (newTypeinfoBuilder_ == null) {
          if (!other.newTypeinfo_.isEmpty()) {
            if (newTypeinfo_.isEmpty()) {
              newTypeinfo_ = other.newTypeinfo_;
              bitField0_ = (bitField0_ & ~0x00000040);
            } else {
              ensureNewTypeinfoIsMutable();
              newTypeinfo_.addAll(other.newTypeinfo_);
            }
            onChanged();
          }
        } else {
          if (!other.newTypeinfo_.isEmpty()) {
            if (newTypeinfoBuilder_.isEmpty()) {
              newTypeinfoBuilder_.dispose();
              newTypeinfoBuilder_ = null;
              newTypeinfo_ = other.newTypeinfo_;
              bitField0_ = (bitField0_ & ~0x00000040);
              newTypeinfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getNewTypeinfoFieldBuilder() : null;
            } else {
              newTypeinfoBuilder_.addAllMessages(other.newTypeinfo_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        for (int i = 0; i < getNewTupleCount(); i++) {
          if (!getNewTuple(i).isInitialized()) {
            return false;
          }
        }
        for (int i = 0; i < getOldTupleCount(); i++) {
          if (!getOldTuple(i).isInitialized()) {
            return false;
          }
        }
        for (int i = 0; i < getNewTypeinfoCount(); i++) {
          if (!getNewTypeinfo(i).isInitialized()) {
            return false;
          }
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        io.debezium.connector.postgresql.proto.PgProto.RowMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (io.debezium.connector.postgresql.proto.PgProto.RowMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int transactionId_ ;
      /**
       * <code>optional uint32 transaction_id = 1;</code>
       */
      public boolean hasTransactionId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint32 transaction_id = 1;</code>
       */
      public int getTransactionId() {
        return transactionId_;
      }
      /**
       * <code>optional uint32 transaction_id = 1;</code>
       */
      public Builder setTransactionId(int value) {
        bitField0_ |= 0x00000001;
        transactionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 transaction_id = 1;</code>
       */
      public Builder clearTransactionId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        transactionId_ = 0;
        onChanged();
        return this;
      }

      private long commitTime_ ;
      /**
       * <code>optional uint64 commit_time = 2;</code>
       */
      public boolean hasCommitTime() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint64 commit_time = 2;</code>
       */
      public long getCommitTime() {
        return commitTime_;
      }
      /**
       * <code>optional uint64 commit_time = 2;</code>
       */
      public Builder setCommitTime(long value) {
        bitField0_ |= 0x00000002;
        commitTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 commit_time = 2;</code>
       */
      public Builder clearCommitTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commitTime_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object table_ = "";
      /**
       * <code>optional string table = 3;</code>
       */
      public boolean hasTable() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string table = 3;</code>
       */
      public java.lang.String getTable() {
        java.lang.Object ref = table_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            table_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string table = 3;</code>
       */
      public com.google.protobuf.ByteString
          getTableBytes() {
        java.lang.Object ref = table_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          table_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string table = 3;</code>
       */
      public Builder setTable(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        table_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string table = 3;</code>
       */
      public Builder clearTable() {
        bitField0_ = (bitField0_ & ~0x00000004);
        table_ = getDefaultInstance().getTable();
        onChanged();
        return this;
      }
      /**
       * <code>optional string table = 3;</code>
       */
      public Builder setTableBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        table_ = value;
        onChanged();
        return this;
      }

      private int op_ = -1;
      /**
       * <code>optional .decoderbufs.Op op = 4;</code>
       */
      public boolean hasOp() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .decoderbufs.Op op = 4;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.Op getOp() {
        @SuppressWarnings("deprecation")
        io.debezium.connector.postgresql.proto.PgProto.Op result = io.debezium.connector.postgresql.proto.PgProto.Op.valueOf(op_);
        return result == null ? io.debezium.connector.postgresql.proto.PgProto.Op.UNKNOWN : result;
      }
      /**
       * <code>optional .decoderbufs.Op op = 4;</code>
       */
      public Builder setOp(io.debezium.connector.postgresql.proto.PgProto.Op value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000008;
        op_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .decoderbufs.Op op = 4;</code>
       */
      public Builder clearOp() {
        bitField0_ = (bitField0_ & ~0x00000008);
        op_ = -1;
        onChanged();
        return this;
      }

      private java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage> newTuple_ =
        java.util.Collections.emptyList();
      private void ensureNewTupleIsMutable() {
        if (!((bitField0_ & 0x00000010) != 0)) {
          newTuple_ = new java.util.ArrayList<io.debezium.connector.postgresql.proto.PgProto.DatumMessage>(newTuple_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          io.debezium.connector.postgresql.proto.PgProto.DatumMessage, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder, io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder> newTupleBuilder_;

      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage> getNewTupleList() {
        if (newTupleBuilder_ == null) {
          return java.util.Collections.unmodifiableList(newTuple_);
        } else {
          return newTupleBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public int getNewTupleCount() {
        if (newTupleBuilder_ == null) {
          return newTuple_.size();
        } else {
          return newTupleBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessage getNewTuple(int index) {
        if (newTupleBuilder_ == null) {
          return newTuple_.get(index);
        } else {
          return newTupleBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public Builder setNewTuple(
          int index, io.debezium.connector.postgresql.proto.PgProto.DatumMessage value) {
        if (newTupleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewTupleIsMutable();
          newTuple_.set(index, value);
          onChanged();
        } else {
          newTupleBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public Builder setNewTuple(
          int index, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder builderForValue) {
        if (newTupleBuilder_ == null) {
          ensureNewTupleIsMutable();
          newTuple_.set(index, builderForValue.build());
          onChanged();
        } else {
          newTupleBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public Builder addNewTuple(io.debezium.connector.postgresql.proto.PgProto.DatumMessage value) {
        if (newTupleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewTupleIsMutable();
          newTuple_.add(value);
          onChanged();
        } else {
          newTupleBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public Builder addNewTuple(
          int index, io.debezium.connector.postgresql.proto.PgProto.DatumMessage value) {
        if (newTupleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewTupleIsMutable();
          newTuple_.add(index, value);
          onChanged();
        } else {
          newTupleBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public Builder addNewTuple(
          io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder builderForValue) {
        if (newTupleBuilder_ == null) {
          ensureNewTupleIsMutable();
          newTuple_.add(builderForValue.build());
          onChanged();
        } else {
          newTupleBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public Builder addNewTuple(
          int index, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder builderForValue) {
        if (newTupleBuilder_ == null) {
          ensureNewTupleIsMutable();
          newTuple_.add(index, builderForValue.build());
          onChanged();
        } else {
          newTupleBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public Builder addAllNewTuple(
          java.lang.Iterable<? extends io.debezium.connector.postgresql.proto.PgProto.DatumMessage> values) {
        if (newTupleBuilder_ == null) {
          ensureNewTupleIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, newTuple_);
          onChanged();
        } else {
          newTupleBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public Builder clearNewTuple() {
        if (newTupleBuilder_ == null) {
          newTuple_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          newTupleBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public Builder removeNewTuple(int index) {
        if (newTupleBuilder_ == null) {
          ensureNewTupleIsMutable();
          newTuple_.remove(index);
          onChanged();
        } else {
          newTupleBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder getNewTupleBuilder(
          int index) {
        return getNewTupleFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder getNewTupleOrBuilder(
          int index) {
        if (newTupleBuilder_ == null) {
          return newTuple_.get(index);  } else {
          return newTupleBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public java.util.List<? extends io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder> 
           getNewTupleOrBuilderList() {
        if (newTupleBuilder_ != null) {
          return newTupleBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(newTuple_);
        }
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder addNewTupleBuilder() {
        return getNewTupleFieldBuilder().addBuilder(
            io.debezium.connector.postgresql.proto.PgProto.DatumMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder addNewTupleBuilder(
          int index) {
        return getNewTupleFieldBuilder().addBuilder(
            index, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage new_tuple = 5;</code>
       */
      public java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder> 
           getNewTupleBuilderList() {
        return getNewTupleFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          io.debezium.connector.postgresql.proto.PgProto.DatumMessage, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder, io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder> 
          getNewTupleFieldBuilder() {
        if (newTupleBuilder_ == null) {
          newTupleBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              io.debezium.connector.postgresql.proto.PgProto.DatumMessage, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder, io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder>(
                  newTuple_,
                  ((bitField0_ & 0x00000010) != 0),
                  getParentForChildren(),
                  isClean());
          newTuple_ = null;
        }
        return newTupleBuilder_;
      }

      private java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage> oldTuple_ =
        java.util.Collections.emptyList();
      private void ensureOldTupleIsMutable() {
        if (!((bitField0_ & 0x00000020) != 0)) {
          oldTuple_ = new java.util.ArrayList<io.debezium.connector.postgresql.proto.PgProto.DatumMessage>(oldTuple_);
          bitField0_ |= 0x00000020;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          io.debezium.connector.postgresql.proto.PgProto.DatumMessage, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder, io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder> oldTupleBuilder_;

      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage> getOldTupleList() {
        if (oldTupleBuilder_ == null) {
          return java.util.Collections.unmodifiableList(oldTuple_);
        } else {
          return oldTupleBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public int getOldTupleCount() {
        if (oldTupleBuilder_ == null) {
          return oldTuple_.size();
        } else {
          return oldTupleBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessage getOldTuple(int index) {
        if (oldTupleBuilder_ == null) {
          return oldTuple_.get(index);
        } else {
          return oldTupleBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public Builder setOldTuple(
          int index, io.debezium.connector.postgresql.proto.PgProto.DatumMessage value) {
        if (oldTupleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOldTupleIsMutable();
          oldTuple_.set(index, value);
          onChanged();
        } else {
          oldTupleBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public Builder setOldTuple(
          int index, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder builderForValue) {
        if (oldTupleBuilder_ == null) {
          ensureOldTupleIsMutable();
          oldTuple_.set(index, builderForValue.build());
          onChanged();
        } else {
          oldTupleBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public Builder addOldTuple(io.debezium.connector.postgresql.proto.PgProto.DatumMessage value) {
        if (oldTupleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOldTupleIsMutable();
          oldTuple_.add(value);
          onChanged();
        } else {
          oldTupleBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public Builder addOldTuple(
          int index, io.debezium.connector.postgresql.proto.PgProto.DatumMessage value) {
        if (oldTupleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOldTupleIsMutable();
          oldTuple_.add(index, value);
          onChanged();
        } else {
          oldTupleBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public Builder addOldTuple(
          io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder builderForValue) {
        if (oldTupleBuilder_ == null) {
          ensureOldTupleIsMutable();
          oldTuple_.add(builderForValue.build());
          onChanged();
        } else {
          oldTupleBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public Builder addOldTuple(
          int index, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder builderForValue) {
        if (oldTupleBuilder_ == null) {
          ensureOldTupleIsMutable();
          oldTuple_.add(index, builderForValue.build());
          onChanged();
        } else {
          oldTupleBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public Builder addAllOldTuple(
          java.lang.Iterable<? extends io.debezium.connector.postgresql.proto.PgProto.DatumMessage> values) {
        if (oldTupleBuilder_ == null) {
          ensureOldTupleIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, oldTuple_);
          onChanged();
        } else {
          oldTupleBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public Builder clearOldTuple() {
        if (oldTupleBuilder_ == null) {
          oldTuple_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000020);
          onChanged();
        } else {
          oldTupleBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public Builder removeOldTuple(int index) {
        if (oldTupleBuilder_ == null) {
          ensureOldTupleIsMutable();
          oldTuple_.remove(index);
          onChanged();
        } else {
          oldTupleBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder getOldTupleBuilder(
          int index) {
        return getOldTupleFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder getOldTupleOrBuilder(
          int index) {
        if (oldTupleBuilder_ == null) {
          return oldTuple_.get(index);  } else {
          return oldTupleBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public java.util.List<? extends io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder> 
           getOldTupleOrBuilderList() {
        if (oldTupleBuilder_ != null) {
          return oldTupleBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(oldTuple_);
        }
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder addOldTupleBuilder() {
        return getOldTupleFieldBuilder().addBuilder(
            io.debezium.connector.postgresql.proto.PgProto.DatumMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder addOldTupleBuilder(
          int index) {
        return getOldTupleFieldBuilder().addBuilder(
            index, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .decoderbufs.DatumMessage old_tuple = 6;</code>
       */
      public java.util.List<io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder> 
           getOldTupleBuilderList() {
        return getOldTupleFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          io.debezium.connector.postgresql.proto.PgProto.DatumMessage, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder, io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder> 
          getOldTupleFieldBuilder() {
        if (oldTupleBuilder_ == null) {
          oldTupleBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              io.debezium.connector.postgresql.proto.PgProto.DatumMessage, io.debezium.connector.postgresql.proto.PgProto.DatumMessage.Builder, io.debezium.connector.postgresql.proto.PgProto.DatumMessageOrBuilder>(
                  oldTuple_,
                  ((bitField0_ & 0x00000020) != 0),
                  getParentForChildren(),
                  isClean());
          oldTuple_ = null;
        }
        return oldTupleBuilder_;
      }

      private java.util.List<io.debezium.connector.postgresql.proto.PgProto.TypeInfo> newTypeinfo_ =
        java.util.Collections.emptyList();
      private void ensureNewTypeinfoIsMutable() {
        if (!((bitField0_ & 0x00000040) != 0)) {
          newTypeinfo_ = new java.util.ArrayList<io.debezium.connector.postgresql.proto.PgProto.TypeInfo>(newTypeinfo_);
          bitField0_ |= 0x00000040;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          io.debezium.connector.postgresql.proto.PgProto.TypeInfo, io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder, io.debezium.connector.postgresql.proto.PgProto.TypeInfoOrBuilder> newTypeinfoBuilder_;

      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public java.util.List<io.debezium.connector.postgresql.proto.PgProto.TypeInfo> getNewTypeinfoList() {
        if (newTypeinfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(newTypeinfo_);
        } else {
          return newTypeinfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public int getNewTypeinfoCount() {
        if (newTypeinfoBuilder_ == null) {
          return newTypeinfo_.size();
        } else {
          return newTypeinfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.TypeInfo getNewTypeinfo(int index) {
        if (newTypeinfoBuilder_ == null) {
          return newTypeinfo_.get(index);
        } else {
          return newTypeinfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public Builder setNewTypeinfo(
          int index, io.debezium.connector.postgresql.proto.PgProto.TypeInfo value) {
        if (newTypeinfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewTypeinfoIsMutable();
          newTypeinfo_.set(index, value);
          onChanged();
        } else {
          newTypeinfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public Builder setNewTypeinfo(
          int index, io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder builderForValue) {
        if (newTypeinfoBuilder_ == null) {
          ensureNewTypeinfoIsMutable();
          newTypeinfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          newTypeinfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public Builder addNewTypeinfo(io.debezium.connector.postgresql.proto.PgProto.TypeInfo value) {
        if (newTypeinfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewTypeinfoIsMutable();
          newTypeinfo_.add(value);
          onChanged();
        } else {
          newTypeinfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public Builder addNewTypeinfo(
          int index, io.debezium.connector.postgresql.proto.PgProto.TypeInfo value) {
        if (newTypeinfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewTypeinfoIsMutable();
          newTypeinfo_.add(index, value);
          onChanged();
        } else {
          newTypeinfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public Builder addNewTypeinfo(
          io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder builderForValue) {
        if (newTypeinfoBuilder_ == null) {
          ensureNewTypeinfoIsMutable();
          newTypeinfo_.add(builderForValue.build());
          onChanged();
        } else {
          newTypeinfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public Builder addNewTypeinfo(
          int index, io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder builderForValue) {
        if (newTypeinfoBuilder_ == null) {
          ensureNewTypeinfoIsMutable();
          newTypeinfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          newTypeinfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public Builder addAllNewTypeinfo(
          java.lang.Iterable<? extends io.debezium.connector.postgresql.proto.PgProto.TypeInfo> values) {
        if (newTypeinfoBuilder_ == null) {
          ensureNewTypeinfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, newTypeinfo_);
          onChanged();
        } else {
          newTypeinfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public Builder clearNewTypeinfo() {
        if (newTypeinfoBuilder_ == null) {
          newTypeinfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);
          onChanged();
        } else {
          newTypeinfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public Builder removeNewTypeinfo(int index) {
        if (newTypeinfoBuilder_ == null) {
          ensureNewTypeinfoIsMutable();
          newTypeinfo_.remove(index);
          onChanged();
        } else {
          newTypeinfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder getNewTypeinfoBuilder(
          int index) {
        return getNewTypeinfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.TypeInfoOrBuilder getNewTypeinfoOrBuilder(
          int index) {
        if (newTypeinfoBuilder_ == null) {
          return newTypeinfo_.get(index);  } else {
          return newTypeinfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public java.util.List<? extends io.debezium.connector.postgresql.proto.PgProto.TypeInfoOrBuilder> 
           getNewTypeinfoOrBuilderList() {
        if (newTypeinfoBuilder_ != null) {
          return newTypeinfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(newTypeinfo_);
        }
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder addNewTypeinfoBuilder() {
        return getNewTypeinfoFieldBuilder().addBuilder(
            io.debezium.connector.postgresql.proto.PgProto.TypeInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder addNewTypeinfoBuilder(
          int index) {
        return getNewTypeinfoFieldBuilder().addBuilder(
            index, io.debezium.connector.postgresql.proto.PgProto.TypeInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .decoderbufs.TypeInfo new_typeinfo = 7;</code>
       */
      public java.util.List<io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder> 
           getNewTypeinfoBuilderList() {
        return getNewTypeinfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          io.debezium.connector.postgresql.proto.PgProto.TypeInfo, io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder, io.debezium.connector.postgresql.proto.PgProto.TypeInfoOrBuilder> 
          getNewTypeinfoFieldBuilder() {
        if (newTypeinfoBuilder_ == null) {
          newTypeinfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              io.debezium.connector.postgresql.proto.PgProto.TypeInfo, io.debezium.connector.postgresql.proto.PgProto.TypeInfo.Builder, io.debezium.connector.postgresql.proto.PgProto.TypeInfoOrBuilder>(
                  newTypeinfo_,
                  ((bitField0_ & 0x00000040) != 0),
                  getParentForChildren(),
                  isClean());
          newTypeinfo_ = null;
        }
        return newTypeinfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:decoderbufs.RowMessage)
    }

    // @@protoc_insertion_point(class_scope:decoderbufs.RowMessage)
    private static final io.debezium.connector.postgresql.proto.PgProto.RowMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new io.debezium.connector.postgresql.proto.PgProto.RowMessage();
    }

    public static io.debezium.connector.postgresql.proto.PgProto.RowMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RowMessage>
        PARSER = new com.google.protobuf.AbstractParser<RowMessage>() {
      @java.lang.Override
      public RowMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RowMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RowMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RowMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public io.debezium.connector.postgresql.proto.PgProto.RowMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_decoderbufs_Point_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_decoderbufs_Point_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_decoderbufs_DatumMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_decoderbufs_DatumMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_decoderbufs_TypeInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_decoderbufs_TypeInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_decoderbufs_RowMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_decoderbufs_RowMessage_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023pg_logicaldec.proto\022\013decoderbufs\"\035\n\005Po" +
      "int\022\t\n\001x\030\001 \002(\001\022\t\n\001y\030\002 \002(\001\"\247\002\n\014DatumMessa" +
      "ge\022\023\n\013column_name\030\001 \001(\t\022\023\n\013column_type\030\002" +
      " \001(\003\022\025\n\013datum_int32\030\003 \001(\005H\000\022\025\n\013datum_int" +
      "64\030\004 \001(\003H\000\022\025\n\013datum_float\030\005 \001(\002H\000\022\026\n\014dat" +
      "um_double\030\006 \001(\001H\000\022\024\n\ndatum_bool\030\007 \001(\010H\000\022" +
      "\026\n\014datum_string\030\010 \001(\tH\000\022\025\n\013datum_bytes\030\t" +
      " \001(\014H\000\022)\n\013datum_point\030\n \001(\0132\022.decoderbuf" +
      "s.PointH\000\022\027\n\rdatum_missing\030\013 \001(\010H\000B\007\n\005da" +
      "tum\"4\n\010TypeInfo\022\020\n\010modifier\030\001 \002(\t\022\026\n\016val" +
      "ue_optional\030\002 \002(\010\"\356\001\n\nRowMessage\022\026\n\016tran" +
      "saction_id\030\001 \001(\r\022\023\n\013commit_time\030\002 \001(\004\022\r\n" +
      "\005table\030\003 \001(\t\022\033\n\002op\030\004 \001(\0162\017.decoderbufs.O" +
      "p\022,\n\tnew_tuple\030\005 \003(\0132\031.decoderbufs.Datum" +
      "Message\022,\n\told_tuple\030\006 \003(\0132\031.decoderbufs" +
      ".DatumMessage\022+\n\014new_typeinfo\030\007 \003(\0132\025.de" +
      "coderbufs.TypeInfo*U\n\002Op\022\024\n\007UNKNOWN\020\377\377\377\377" +
      "\377\377\377\377\377\001\022\n\n\006INSERT\020\000\022\n\n\006UPDATE\020\001\022\n\n\006DELETE" +
      "\020\002\022\t\n\005BEGIN\020\003\022\n\n\006COMMIT\020\004B3\n&io.debezium" +
      ".connector.postgresql.protoB\007PgProtoH\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_decoderbufs_Point_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_decoderbufs_Point_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_decoderbufs_Point_descriptor,
        new java.lang.String[] { "X", "Y", });
    internal_static_decoderbufs_DatumMessage_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_decoderbufs_DatumMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_decoderbufs_DatumMessage_descriptor,
        new java.lang.String[] { "ColumnName", "ColumnType", "DatumInt32", "DatumInt64", "DatumFloat", "DatumDouble", "DatumBool", "DatumString", "DatumBytes", "DatumPoint", "DatumMissing", "Datum", });
    internal_static_decoderbufs_TypeInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_decoderbufs_TypeInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_decoderbufs_TypeInfo_descriptor,
        new java.lang.String[] { "Modifier", "ValueOptional", });
    internal_static_decoderbufs_RowMessage_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_decoderbufs_RowMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_decoderbufs_RowMessage_descriptor,
        new java.lang.String[] { "TransactionId", "CommitTime", "Table", "Op", "NewTuple", "OldTuple", "NewTypeinfo", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
