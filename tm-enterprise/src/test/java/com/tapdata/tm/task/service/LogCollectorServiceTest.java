package com.tapdata.tm.task.service;

import com.tapdata.tm.base.dto.Field;
import com.tapdata.tm.base.dto.Filter;
import com.tapdata.tm.base.dto.Page;
import com.tapdata.tm.base.exception.BizException;
import com.tapdata.tm.commons.dag.DAG;
import com.tapdata.tm.commons.dag.Node;
import com.tapdata.tm.commons.dag.logCollector.LogCollecotrConnConfig;
import com.tapdata.tm.commons.dag.logCollector.LogCollectorNode;
import com.tapdata.tm.commons.dag.nodes.TableNode;
import com.tapdata.tm.commons.schema.DataSourceConnectionDto;
import com.tapdata.tm.commons.schema.DataSourceDefinitionDto;
import com.tapdata.tm.commons.schema.MetadataInstancesDto;
import com.tapdata.tm.commons.task.dto.ParentTaskDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.commons.util.CapabilityEnum;
import com.tapdata.tm.commons.util.ConnHeartbeatUtils;
import com.tapdata.tm.commons.util.CreateTypeEnum;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.ds.service.impl.DataSourceDefinitionService;
import com.tapdata.tm.ds.service.impl.DataSourceService;
import com.tapdata.tm.externalStorage.service.ExternalStorageService;
import com.tapdata.tm.message.constant.Level;
import com.tapdata.tm.metadatainstance.service.MetadataInstancesService;
import com.tapdata.tm.monitoringlogs.service.MonitoringLogsService;
import com.tapdata.tm.shareCdcTableMapping.ShareCdcTableMappingDto;
import com.tapdata.tm.shareCdcTableMapping.service.ShareCdcTableMappingService;
import com.tapdata.tm.shareCdcTableMetrics.ShareCdcTableMetricsDto;
import com.tapdata.tm.shareCdcTableMetrics.service.ShareCdcTableMetricsService;
import com.tapdata.tm.task.bean.LogCollectorVo;
import com.tapdata.tm.task.bean.ShareCdcTableInfo;
import com.tapdata.tm.task.entity.TableNameAndConnId;
import com.tapdata.tm.utils.MessageUtil;
import com.tapdata.tm.utils.MongoUtils;
import com.tapdata.tm.utils.UUIDUtil;
import io.tapdata.pdk.apis.entity.Capability;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-02-19 15:56
 **/
@DisplayName("LogCollectorService Class Test")
public class LogCollectorServiceTest {
	private LogCollectorServiceImpl logCollectorService;
	private UserDetail userDetail;
	private TaskService taskService;

	@BeforeEach
	void setUp() {
		logCollectorService = spy(new LogCollectorServiceImpl());
		userDetail = mock(UserDetail.class);
		taskService = mock(TaskService.class);
		ReflectionTestUtils.setField(logCollectorService,"taskService",taskService);
	}

	@Nested
	@DisplayName("GetShareCdcTableInfoPage Method Test")
	class GetShareCdcTableInfoPageTest {

		private Map<TableNameAndConnId, String> tableNameConnectionIdMap;
		private DataSourceService dataSourceService;
		private String connectionId;
		private String connectionName;
		private String taskId;
		private String nodeId;

		@BeforeEach
		void setUp() {
			taskId = new ObjectId().toHexString();
			nodeId = UUIDUtil.getUUID();
			connectionId = new ObjectId().toHexString();
			connectionName = "test connection";
			dataSourceService = mock(DataSourceService.class);
			ReflectionTestUtils.setField(logCollectorService, "dataSourceService", dataSourceService);
			tableNameConnectionIdMap = new HashMap<>();
			for (int i = 0; i < 50; i++) {
				tableNameConnectionIdMap.put(new TableNameAndConnId("table_" + i, connectionId), connectionId);
			}
			DataSourceConnectionDto dataSourceConnectionDto = new DataSourceConnectionDto();
			dataSourceConnectionDto.setName(connectionName);
			when(dataSourceService.findById(eq(MongoUtils.toObjectId(connectionId)), any(Field.class), eq(userDetail)))
					.thenReturn(dataSourceConnectionDto);
			doAnswer(invocationOnMock -> null).when(logCollectorService).setShareTableInfo(any(ShareCdcTableInfo.class), eq(userDetail), eq(taskId), eq(nodeId));
		}

		@Test
		@DisplayName("Test Different Page")
		void testDifferentPage() {
			Page<ShareCdcTableInfo> shareCdcTableInfoPage = logCollectorService.getShareCdcTableInfoPage(
					tableNameConnectionIdMap, 1, 10,"", userDetail, "", nodeId, taskId
			);
			verify(dataSourceService, times(10)).findById(eq(MongoUtils.toObjectId(connectionId)), any(Field.class), eq(userDetail));
			assertEquals(10, shareCdcTableInfoPage.getItems().size());
			assertEquals(50, shareCdcTableInfoPage.getTotal());
			assertEquals("table_0", shareCdcTableInfoPage.getItems().iterator().next().getName());

			shareCdcTableInfoPage = logCollectorService.getShareCdcTableInfoPage(
					tableNameConnectionIdMap, 2, 10,"", userDetail, "", nodeId, taskId
			);
			verify(dataSourceService, times(20)).findById(eq(MongoUtils.toObjectId(connectionId)), any(Field.class), eq(userDetail));
			assertEquals(10, shareCdcTableInfoPage.getItems().size());
			assertEquals(50, shareCdcTableInfoPage.getTotal());
			assertEquals("table_18", shareCdcTableInfoPage.getItems().iterator().next().getName());
		}

		@Test
		@DisplayName("Test Different size")
		void testDifferentSize() {
			Page<ShareCdcTableInfo> shareCdcTableInfoPage = logCollectorService.getShareCdcTableInfoPage(
					tableNameConnectionIdMap, 1, 1,"", userDetail, "", nodeId, taskId
			);
			verify(dataSourceService, times(1)).findById(eq(MongoUtils.toObjectId(connectionId)), any(Field.class), eq(userDetail));
			assertEquals(1, shareCdcTableInfoPage.getItems().size());
			assertEquals(50, shareCdcTableInfoPage.getTotal());
			assertEquals("table_0", shareCdcTableInfoPage.getItems().iterator().next().getName());

			shareCdcTableInfoPage = logCollectorService.getShareCdcTableInfoPage(
					tableNameConnectionIdMap, 1, 5,"", userDetail, "", nodeId, taskId
			);
			verify(dataSourceService, times(6)).findById(eq(MongoUtils.toObjectId(connectionId)), any(Field.class), eq(userDetail));
			assertEquals(5, shareCdcTableInfoPage.getItems().size());
			assertEquals(50, shareCdcTableInfoPage.getTotal());
			assertEquals("table_0", shareCdcTableInfoPage.getItems().iterator().next().getName());
		}

		@Test
		@DisplayName("Test KeyWord")
		void testKeyWord() {
			Page<ShareCdcTableInfo> shareCdcTableInfoPage = logCollectorService.getShareCdcTableInfoPage(
					tableNameConnectionIdMap, 1, 1,"", userDetail, "_30", nodeId, taskId
			);
			verify(dataSourceService, times(1)).findById(eq(MongoUtils.toObjectId(connectionId)), any(Field.class), eq(userDetail));
			assertEquals(1, shareCdcTableInfoPage.getItems().size());
			assertEquals(1, shareCdcTableInfoPage.getTotal());
			assertEquals("table_30", shareCdcTableInfoPage.getItems().iterator().next().getName());
		}
		@Test
		@DisplayName("test today count asc order")
		void testTodayCountASC(){
			doCallRealMethod().when(logCollectorService).setShareTableInfo(any(ShareCdcTableInfo.class), eq(userDetail), eq(taskId), eq(nodeId));
			ShareCdcTableMetricsService shareCdcTableMetricsService = mock(ShareCdcTableMetricsService.class);
			ReflectionTestUtils.setField(logCollectorService,"shareCdcTableMetricsService",shareCdcTableMetricsService);
			ShareCdcTableMappingService shareCdcTableMappingService = mock(ShareCdcTableMappingService.class);
			ReflectionTestUtils.setField(logCollectorService,"shareCdcTableMappingService",shareCdcTableMappingService);
			when(shareCdcTableMappingService.findOne(any(Query.class))).thenReturn(mock(ShareCdcTableMappingDto.class));
			ShareCdcTableMetricsDto metricsDto1 = new ShareCdcTableMetricsDto();
			metricsDto1.setCount(1L);
			ShareCdcTableMetricsDto metricsDto2 = new ShareCdcTableMetricsDto();
			metricsDto2.setCount(2L);
			when(shareCdcTableMetricsService.findOne(any(Query.class),any(UserDetail.class))).thenReturn(metricsDto1).thenReturn(metricsDto2);
			Page<ShareCdcTableInfo> shareCdcTableInfoPage = logCollectorService.getShareCdcTableInfoPage(
					tableNameConnectionIdMap, 1, 2,"todayCount ASC", userDetail, "", nodeId, taskId
			);
			verify(dataSourceService, times(50)).findById(eq(MongoUtils.toObjectId(connectionId)), any(Field.class), eq(userDetail));
			assertEquals(2, shareCdcTableInfoPage.getItems().size());
			assertEquals(50, shareCdcTableInfoPage.getTotal());
			assertEquals("table_0", shareCdcTableInfoPage.getItems().iterator().next().getName());
		}
		@Test
		@DisplayName("test today count desc order")
		void testTodayCountDESC(){
			doCallRealMethod().when(logCollectorService).setShareTableInfo(any(ShareCdcTableInfo.class), eq(userDetail), eq(taskId), eq(nodeId));
			ShareCdcTableMetricsService shareCdcTableMetricsService = mock(ShareCdcTableMetricsService.class);
			ReflectionTestUtils.setField(logCollectorService,"shareCdcTableMetricsService",shareCdcTableMetricsService);
			ShareCdcTableMappingService shareCdcTableMappingService = mock(ShareCdcTableMappingService.class);
			ReflectionTestUtils.setField(logCollectorService,"shareCdcTableMappingService",shareCdcTableMappingService);
			when(shareCdcTableMappingService.findOne(any(Query.class))).thenReturn(mock(ShareCdcTableMappingDto.class));
			ShareCdcTableMetricsDto metricsDto1 = new ShareCdcTableMetricsDto();
			metricsDto1.setCount(1L);
			ShareCdcTableMetricsDto metricsDto2 = new ShareCdcTableMetricsDto();
			metricsDto2.setCount(2L);
			when(shareCdcTableMetricsService.findOne(any(Query.class),any(UserDetail.class))).thenReturn(metricsDto1).thenReturn(metricsDto2);
			Page<ShareCdcTableInfo> shareCdcTableInfoPage = logCollectorService.getShareCdcTableInfoPage(
					tableNameConnectionIdMap, 1, 2,"todayCount DESC", userDetail, "", nodeId, taskId
			);
			verify(dataSourceService, times(50)).findById(eq(MongoUtils.toObjectId(connectionId)), any(Field.class), eq(userDetail));
			assertEquals(2, shareCdcTableInfoPage.getItems().size());
			assertEquals(50, shareCdcTableInfoPage.getTotal());
			assertEquals("table_1", shareCdcTableInfoPage.getItems().iterator().next().getName());
		}
	}
	@Nested
	class ConvertTest{
		private DataSourceService dataSourceService;
		private TaskDto taskDto;
		private DAG dag;
		@BeforeEach
		void beforeEach(){
			dataSourceService = mock(DataSourceService.class);
			ReflectionTestUtils.setField(logCollectorService,"dataSourceService",dataSourceService);
			taskDto = mock(TaskDto.class);
			when(taskDto.getId()).thenReturn(mock(ObjectId.class));
			dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode source = mock(LogCollectorNode.class);
			when(source.getId()).thenReturn("111");
			sources.add(source);
			List<Node> targets = new ArrayList<>();
			TableNode target = mock(TableNode.class);
			when(target.getId()).thenReturn("222");
			targets.add(target);
			when(dag.getSources()).thenReturn(sources);
			when(dag.getTargets()).thenReturn(targets);
			when(taskDto.getDag()).thenReturn(dag);
		}
		@Test
		@DisplayName("test convert method normal")
		void test1(){
			try (MockedStatic<LogCollectorServiceImpl> mb = Mockito
					.mockStatic(LogCollectorServiceImpl.class)) {
				Date date = new Date();
				mb.when(()->LogCollectorServiceImpl.getAttrsValues(anyString(),anyString(),anyString(),anyMap())).thenReturn(date).thenReturn(date);
				long delayTime = 1717385911L;
				when(taskDto.getDelayTime()).thenReturn(delayTime);
				List<DataSourceConnectionDto> datasources = new ArrayList<>();
				DataSourceConnectionDto dataSourceConnectionDto = new DataSourceConnectionDto();
				dataSourceConnectionDto.setId(mock(ObjectId.class));
				datasources.add(dataSourceConnectionDto);
				when(dataSourceService.findAll(any(Query.class))).thenReturn(datasources);
				LogCollectorVo actual = logCollectorService.convert(taskDto);
				assertEquals(delayTime,actual.getDelayTime());
			}
		}
		@Test
		@DisplayName("test convert method when eventTime is null")
		void test2(){
			try (MockedStatic<LogCollectorServiceImpl> mb = Mockito
					.mockStatic(LogCollectorServiceImpl.class)) {
				Date date = new Date();
				mb.when(()->LogCollectorServiceImpl.getAttrsValues(anyString(),anyString(),anyString(),anyMap())).thenReturn(null).thenReturn(date);
				LogCollectorVo actual = logCollectorService.convert(taskDto);
				assertEquals(-1L,actual.getDelayTime());
			}
		}
		@Test
		@DisplayName("test convert method when sourceTime is null")
		void test3(){
			try (MockedStatic<LogCollectorServiceImpl> mb = Mockito
					.mockStatic(LogCollectorServiceImpl.class)) {
				Date date = new Date();
				mb.when(()->LogCollectorServiceImpl.getAttrsValues(anyString(),anyString(),anyString(),anyMap())).thenReturn(date).thenReturn(null);
				LogCollectorVo actual = logCollectorService.convert(taskDto);
				assertEquals(-1L,actual.getDelayTime());
			}
		}
		@Test
		@DisplayName("test convert method when source node is tableNode")
		void test4(){
			try (MockedStatic<LogCollectorServiceImpl> mb = Mockito
					.mockStatic(LogCollectorServiceImpl.class)) {
				Date date = new Date();
				mb.when(()->LogCollectorServiceImpl.getAttrsValues(anyString(),anyString(),anyString(),anyMap())).thenReturn(date).thenReturn(date);
				List<Node> sources = new ArrayList<>();
				TableNode source = mock(TableNode.class);
				when(source.getId()).thenReturn("111");
				sources.add(source);
				when(dag.getSources()).thenReturn(sources);
				LogCollectorVo actual = logCollectorService.convert(taskDto);
				assertEquals(0,actual.getDelayTime());
			}
		}
		@Test
		@DisplayName("test convert method when dag is null")
		void test5(){
			when(taskDto.getDag()).thenReturn(null);
			LogCollectorVo actual = logCollectorService.convert(taskDto);
			assertEquals(0,actual.getDelayTime());
		}
	}
	@Nested
	class LogCollector{
		@BeforeEach
		void setUp(){
			ReflectionTestUtils.setField(logCollectorService,"dataSourceService",mock(DataSourceService.class));
		}
		@Test
		void testCacheTask(){
			TaskDto taskDto = new TaskDto();
			taskDto.setShareCdcEnable(true);
			taskDto.setSyncType(TaskDto.SYNC_TYPE_MEM_CACHE);
			DAG dag = mock(DAG.class);
			taskDto.setDag(dag);
			when(dag.getNodes()).thenReturn(new ArrayList<>());
			when(dag.getTargets()).thenReturn(new ArrayList<>());
			doNothing().when(logCollectorService).updateLogCollectorMap(any(),any(),any());
			logCollectorService.logCollector(mock(UserDetail.class),taskDto);
			verify(logCollectorService,times(1)).updateLogCollectorMap(any(),any(),any());
		}

		@Test
		void testSyncTask(){
			TaskDto taskDto = new TaskDto();
			taskDto.setShareCdcEnable(true);
			taskDto.setSyncType(TaskDto.SYNC_TYPE_SYNC);
			DAG dag = mock(DAG.class);
			taskDto.setDag(dag);
			when(dag.getNodes()).thenReturn(new ArrayList<>());
			when(dag.getTargets()).thenReturn(new ArrayList<>());
			doNothing().when(logCollectorService).updateLogCollectorMap(any(),any(),any());
			logCollectorService.logCollector(mock(UserDetail.class),taskDto);
			verify(logCollectorService,times(1)).updateLogCollectorMap(any(),any(),any());
		}
		@Test
		void testMigrateTask(){
			TaskDto taskDto = new TaskDto();
			taskDto.setShareCdcEnable(true);
			taskDto.setSyncType(TaskDto.SYNC_TYPE_MIGRATE);
			DAG dag = mock(DAG.class);
			taskDto.setDag(dag);
			when(dag.getNodes()).thenReturn(new ArrayList<>());
			when(dag.getTargets()).thenReturn(new ArrayList<>());
			doNothing().when(logCollectorService).updateLogCollectorMap(any(),any(),any());
			logCollectorService.logCollector(mock(UserDetail.class),taskDto);
			verify(logCollectorService,times(1)).updateLogCollectorMap(any(),any(),any());
		}
		@DisplayName("test LogCollector add HeartbeatName")
		@Test
		void testLogCollector(){
			TaskDto taskDto = new TaskDto();
			taskDto.setId(MongoUtils.toObjectId("664184e4e7fea472f196681a"));
			taskDto.setShareCdcEnable(true);
			taskDto.setSyncType(TaskDto.SYNC_TYPE_MIGRATE);

			DAG dag = mock(DAG.class);
			List<Node> nodes=new ArrayList<>();
			TableNode tableNode=new TableNode();
			tableNode.setCatalog(Node.NodeCatalog.data);
			tableNode.setConnectionId("664184e4e7fea472f196681a");
			nodes.add(tableNode);
			when(dag.getNodes()).thenReturn(nodes);
			when(dag.getTargets()).thenReturn(new ArrayList<>());
			taskDto.setDag(dag);
			List<DataSourceConnectionDto> dataSourceConnections=new ArrayList<>();
			DataSourceConnectionDto dataSourceConnectionDto=new DataSourceConnectionDto();
			dataSourceConnectionDto.setId(MongoUtils.toObjectId("664184e4e7fea472f196681a"));
			dataSourceConnectionDto.setShareCdcEnable(true);
			dataSourceConnectionDto.setHeartbeatEnable(true);
			dataSourceConnections.add(dataSourceConnectionDto);

			DataSourceService dataSourceService = mock(DataSourceService.class);
			when(dataSourceService.findAll(any(Query.class))).thenReturn(dataSourceConnections);
			TaskService taskService = mock(TaskService.class);
			TaskDto logControllerTaskDto = new TaskDto();
			logControllerTaskDto.setName("CDC log cache task from Mysql");
			logControllerTaskDto.setType("cdc");
			logControllerTaskDto.setSyncType("logCollector");
			logControllerTaskDto.setId(MongoUtils.toObjectId("65ae40b097480b60d7d7f097"));
			when(taskService.confirmById(any(), any(UserDetail.class), anyBoolean())).thenReturn(logControllerTaskDto);

			ExternalStorageService externalStorageService = mock(ExternalStorageService.class);
			TaskSaveService taskSaveService = mock(TaskSaveService.class);
			ShareCdcTableMappingService shareCdcTableMappingService = mock(ShareCdcTableMappingService.class);
			MonitoringLogsService monitoringLogsService = mock(MonitoringLogsService.class);
			ReflectionTestUtils.setField(logCollectorService,"shareCdcTableMappingService",shareCdcTableMappingService);
			ReflectionTestUtils.setField(logCollectorService,"taskSaveService",taskSaveService);
			ReflectionTestUtils.setField(logCollectorService,"taskService",taskService);
			ReflectionTestUtils.setField(logCollectorService,"dataSourceService",dataSourceService);
			ReflectionTestUtils.setField(logCollectorService,"externalStorageService",externalStorageService);
			ReflectionTestUtils.setField(logCollectorService,"monitoringLogsService",monitoringLogsService);
			doNothing().when(logCollectorService).updateLogCollectorMap(any(),any(),any());
			logCollectorService.logCollector(mock(UserDetail.class),taskDto);
			verify(taskService,times(1)).start(any(),any());
		}
		@Test
		void testConnHeartbeatTask(){
			TaskDto taskDto = new TaskDto();
			taskDto.setShareCdcEnable(true);
			taskDto.setSyncType(TaskDto.SYNC_TYPE_CONN_HEARTBEAT);
			logCollectorService.logCollector(mock(UserDetail.class),taskDto);
			verify(logCollectorService,times(0)).updateLogCollectorMap(any(),any(),any());
		}

		@Test
		void testTestRunTask(){
			TaskDto taskDto = new TaskDto();
			taskDto.setShareCdcEnable(true);
			taskDto.setSyncType(TaskDto.SYNC_TYPE_TEST_RUN);
			logCollectorService.logCollector(mock(UserDetail.class),taskDto);
			verify(logCollectorService,times(0)).updateLogCollectorMap(any(),any(),any());
		}
	}
	@Nested
	class QueryTaskTest{
        TaskService taskService;
		@BeforeEach
		void setUp(){
			taskService = mock(TaskService.class);
			ReflectionTestUtils.setField(logCollectorService,"taskService",taskService);
		}
		@Test
		void test_main(){
			UserDetail user = mock(UserDetail.class);
			logCollectorService.queryTask(user,Arrays.asList("test1","test2"));
			verify(taskService,times(1)).findOne(any(Query.class),any(UserDetail.class));
		}
	}

	@Nested
	class GetDummyHeartConnectionTest {
		LogCollectorServiceImpl logCollectorService;
		UserDetail userDetail;

		DataSourceDefinitionDto dataSourceDefinitionDto;
		private String connectionId="662f841bf4a4396bfdd3b3ad";
		private String dataSourceId="testDataSourceId";
		private String qualifiedName="T__tapdata_heartbeat_table_662f841bf4a4396bfdd3b3ad";
		Query querySystemDummy;
		Query dataSourceDefinitionQuery;
		MetadataInstancesService metadataInstancesService;

		@BeforeEach
		void setUp(){
			logCollectorService = mock(LogCollectorServiceImpl.class);
			userDetail = mock(UserDetail.class);
			dataSourceDefinitionDto = new DataSourceDefinitionDto();
			dataSourceDefinitionDto.setPdkType("Dummy");
			dataSourceDefinitionDto.setPdkHash("testPdkHash");
			querySystemDummy = new Query(Criteria.where("database_type").is(ConnHeartbeatUtils.PDK_NAME)
					.and("createType").is(CreateTypeEnum.System));
			dataSourceDefinitionQuery = new Query(Criteria.where("pdkId").is(ConnHeartbeatUtils.PDK_ID));
			dataSourceDefinitionQuery.fields().include("pdkHash", "type");
			metadataInstancesService = mock(MetadataInstancesService.class);
			doCallRealMethod().when(logCollectorService).getSystemDummyConnection(null,userDetail,dataSourceId);
		}

		@DisplayName("test getDummyHeartConnection when system dummy is already exist")
		@Test
		void test1(){
			DataSourceService dataSourceService = mock(DataSourceService.class);
			DataSourceDefinitionDto dataSourceDefinitionDto=new DataSourceDefinitionDto();

			DataSourceConnectionDto dataSourceConnectionDto = ConnHeartbeatUtils.generateConnections(dataSourceId, dataSourceDefinitionDto);
			dataSourceConnectionDto.setId(MongoUtils.toObjectId(connectionId));
			ReflectionTestUtils.setField(logCollectorService,"dataSourceService",dataSourceService);

			when(dataSourceService.findOne(querySystemDummy,userDetail)).thenReturn(dataSourceConnectionDto);


			MetadataInstancesDto metadataInstancesDto=new MetadataInstancesDto();
			when(metadataInstancesService.findByQualifiedNameNotDelete(qualifiedName,userDetail,"_id")).thenReturn(metadataInstancesDto);
			ReflectionTestUtils.setField(logCollectorService,"metadataInstancesService",metadataInstancesService);

			DataSourceConnectionDto dummyConnection = logCollectorService.getSystemDummyConnection(null, userDetail, "testDataSourceId");
			assertEquals(dummyConnection,dataSourceConnectionDto);
			verify(metadataInstancesService,times(1)).findByQualifiedNameNotDelete(qualifiedName,userDetail,"_id");
		}
		@DisplayName("test getDummyHeartConnection when system dummy is already exist,but can not find metadataInstance")
		@Test
		void test2(){

			DataSourceService dataSourceService = mock(DataSourceService.class);

			DataSourceConnectionDto dataSourceConnectionDto = ConnHeartbeatUtils.generateConnections(dataSourceId, dataSourceDefinitionDto);
			dataSourceConnectionDto.setId(MongoUtils.toObjectId(connectionId));

			ReflectionTestUtils.setField(logCollectorService,"dataSourceService",dataSourceService);

			when(dataSourceService.findOne(querySystemDummy,userDetail)).thenReturn(dataSourceConnectionDto);


			when(metadataInstancesService.findByQualifiedNameNotDelete("T__tapdata_heartbeat_table_662f841bf4a4396bfdd3b3ad",userDetail,"_id")).thenReturn(null);
			ReflectionTestUtils.setField(logCollectorService,"metadataInstancesService",metadataInstancesService);

			DataSourceConnectionDto dummyConnection = logCollectorService.getSystemDummyConnection(null, userDetail, "testDataSourceId");
			assertEquals(dataSourceConnectionDto,dummyConnection);
			verify(metadataInstancesService,times(9)).findByQualifiedNameNotDelete(qualifiedName,userDetail,"_id");
		}

		@DisplayName("test getDummyHeartConnection when systemDummy is not exist")
		@Test
		void test3() {
			DataSourceConnectionDto dataSourceConnectionDto = ConnHeartbeatUtils.generateConnections(dataSourceId, dataSourceDefinitionDto);
			dataSourceConnectionDto.setConfig(null);
			dataSourceConnectionDto.setId(MongoUtils.toObjectId(connectionId));

			DataSourceService dataSourceService = mock(DataSourceService.class);
			ReflectionTestUtils.setField(logCollectorService, "dataSourceService", dataSourceService);

			when(dataSourceService.findOne(querySystemDummy, userDetail)).thenReturn(null);

			DataSourceDefinitionService dataSourceDefinitionService = mock(DataSourceDefinitionService.class);
			ReflectionTestUtils.setField(logCollectorService, "dataSourceDefinitionService", dataSourceDefinitionService);
			when(dataSourceDefinitionService.findOne(dataSourceDefinitionQuery)).thenReturn(dataSourceDefinitionDto);
			when(dataSourceService.add(any(DataSourceConnectionDto.class), any(UserDetail.class))).thenReturn(dataSourceConnectionDto);

			MetadataInstancesDto metadataInstancesDto=new MetadataInstancesDto();
			when(metadataInstancesService.findByQualifiedNameNotDelete(qualifiedName,userDetail,"_id")).thenReturn(metadataInstancesDto);
			ReflectionTestUtils.setField(logCollectorService,"metadataInstancesService",metadataInstancesService);

			DataSourceConnectionDto dummyConnection = logCollectorService.getSystemDummyConnection(null, userDetail, "testDataSourceId");
			assertEquals(dataSourceConnectionDto,dummyConnection);
			verify(metadataInstancesService,times(1)).findByQualifiedNameNotDelete(qualifiedName,userDetail,"_id");

		}
		@DisplayName("test getDummyHeartConnection when dummy connector not registered")
		@Test
		void test4() {
			DataSourceConnectionDto dataSourceConnectionDto = ConnHeartbeatUtils.generateConnections(dataSourceId, dataSourceDefinitionDto);
			dataSourceConnectionDto.setConfig(null);
			dataSourceConnectionDto.setId(MongoUtils.toObjectId(connectionId));

			DataSourceService dataSourceService = mock(DataSourceService.class);
			ReflectionTestUtils.setField(logCollectorService, "dataSourceService", dataSourceService);

			when(dataSourceService.findOne(querySystemDummy, userDetail)).thenReturn(null);



			DataSourceDefinitionService dataSourceDefinitionService = mock(DataSourceDefinitionService.class);
			ReflectionTestUtils.setField(logCollectorService, "dataSourceDefinitionService", dataSourceDefinitionService);
			when(dataSourceDefinitionService.findOne(dataSourceDefinitionQuery)).thenReturn(null);

			DataSourceConnectionDto connectionDto = logCollectorService.getSystemDummyConnection(null,userDetail, "testDataSourceId");
			assertEquals(null,connectionDto);
		}

	}

	@Nested
	class StartConnHeartbeatTest {
		UserDetail userDetail;
		LogCollectorServiceImpl logCollectorService;
		MonitoringLogsService monitoringLogsService;
		private String taskId = "6656dfd6362b11461e4ad403";
		private String connectionId = "65fd52aa67def503a78ea020";

		private String heartBeatTaskId="6656dfd6362b11461e4ad403";
		private String heartBeatConnId="6600b629928fc21057b480fd";
		DataSourceDefinitionDto dataSourceDefinitionDto;
		TaskService taskService;
		List<Capability> capabilities = new ArrayList<>();
		DataSourceConnectionDto dataSourceConnectionDto;
		TaskDto taskDto;

		@BeforeEach
		void setUp() {
			userDetail = mock(UserDetail.class);
			logCollectorService = mock(LogCollectorServiceImpl.class);
			dataSourceDefinitionDto = new DataSourceDefinitionDto();
			dataSourceDefinitionDto.setPdkType("Dummy");
			dataSourceDefinitionDto.setPdkHash("testPdkHash");
			taskService=mock(TaskService.class);
			ReflectionTestUtils.setField(logCollectorService,"taskService",taskService);
			monitoringLogsService = mock(MonitoringLogsService.class);
			ReflectionTestUtils.setField(logCollectorService,"monitoringLogsService",monitoringLogsService);
			Capability streamReadCapability = new Capability();
			streamReadCapability.setId(CapabilityEnum.STREAM_READ_FUNCTION.getId());
			Capability createTableCapability =new Capability();
			createTableCapability.setId(CapabilityEnum.CREATE_TABLE_V2_FUNCTION.getId());
			Capability writeRecordCapability =new Capability();
			writeRecordCapability.setId(CapabilityEnum.WRITE_RECORD_FUNCTION.getId());
			capabilities.add(streamReadCapability);
			capabilities.add(createTableCapability);
			capabilities.add(writeRecordCapability);

			dataSourceConnectionDto = new DataSourceConnectionDto();;
			dataSourceConnectionDto.setId(MongoUtils.toObjectId(connectionId));
			dataSourceConnectionDto.setConnection_type("source_and_target");
			dataSourceConnectionDto.setHeartbeatEnable(true);
			dataSourceConnectionDto.setConnection_type("mysql");
			dataSourceConnectionDto.setCapabilities(capabilities);

			taskDto = new TaskDto();
			taskDto.setSyncType(TaskDto.SYNC_TYPE_MIGRATE);
			taskDto.setType(ParentTaskDto.TYPE_INITIAL_SYNC_CDC);
			taskDto.setId(MongoUtils.toObjectId(taskId));
		}
		@DisplayName("test startConnHeartbeat when heartbeat Task not exist")
		@Test
		void test1() {

			List<DataSourceConnectionDto> dataSourceDtos =new ArrayList<>();
			dataSourceDtos.add(dataSourceConnectionDto);

			when(logCollectorService.getConnectionByDag(any(UserDetail.class),eq(null))).thenReturn(dataSourceDtos);
			doCallRealMethod().when(logCollectorService).startConnHeartbeat(userDetail,taskDto);

			List<String> connections=new ArrayList<>();
			connections.add(connectionId);
			when(logCollectorService.getConnectionIds(any(UserDetail.class),anyMap(),any(DataSourceConnectionDto.class))).thenReturn(connections);

			DataSourceConnectionDto heartBeatConnection = ConnHeartbeatUtils.generateConnections(connectionId, dataSourceDefinitionDto);
			heartBeatConnection.setId(MongoUtils.toObjectId(heartBeatConnId));
			when(logCollectorService.getSystemDummyConnection(eq(null),eq(userDetail),eq(connectionId))).thenReturn(heartBeatConnection);

			TaskDto heartBeatTask =new TaskDto();
			heartBeatTask.setId(MongoUtils.toObjectId(heartBeatTaskId));
			when(taskService.confirmById(eq(null),eq(userDetail),eq(true))).thenReturn(heartBeatTask);
			logCollectorService.startConnHeartbeat(userDetail,taskDto);
			verify(taskService,times(1)).start(eq(heartBeatTask.getId()),eq(userDetail));
		}
		@DisplayName("test startConnHeartbeat when heartbeat Task already exist")
		@Test
		void test2(){
			List<DataSourceConnectionDto> dataSourceDtos =new ArrayList<>();
			dataSourceDtos.add(dataSourceConnectionDto);

			when(logCollectorService.getConnectionByDag(any(UserDetail.class),eq(null))).thenReturn(dataSourceDtos);
			doCallRealMethod().when(logCollectorService).startConnHeartbeat(userDetail,taskDto);

			List<String> connections=new ArrayList<>();
			connections.add(connectionId);
			when(logCollectorService.getConnectionIds(any(UserDetail.class),anyMap(),any(DataSourceConnectionDto.class))).thenReturn(connections);

			TaskDto heartBeatTask=new TaskDto();
			heartBeatTask.setId(MongoUtils.toObjectId(heartBeatTaskId));
			heartBeatTask.setStatus(TaskDto.STATUS_STOP);
			when(logCollectorService.queryTask(eq(userDetail),eq(connections))).thenReturn(heartBeatTask);
			logCollectorService.startConnHeartbeat(userDetail,taskDto);
			verify(taskService,times(1)).start(eq(heartBeatTask.getId()),eq(userDetail));
		}

        @DisplayName("test startConnHeartbeat when heartbeat Task start failed")
        @Test
        void test3() {
            List<DataSourceConnectionDto> dataSourceDtos = new ArrayList<>();
            dataSourceDtos.add(dataSourceConnectionDto);

            when(logCollectorService.getConnectionByDag(any(UserDetail.class), eq(null))).thenReturn(dataSourceDtos);
            doCallRealMethod().when(logCollectorService).startConnHeartbeat(userDetail, taskDto);

            List<String> connections = new ArrayList<>();
            connections.add(connectionId);
            when(logCollectorService.getConnectionIds(any(UserDetail.class), anyMap(), any(DataSourceConnectionDto.class))).thenReturn(connections);
            doThrow(new RuntimeException("xxx")).when(taskService).start(any(ObjectId.class), any(UserDetail.class));

            TaskDto heartBeatTask = new TaskDto();
            heartBeatTask.setId(MongoUtils.toObjectId(heartBeatTaskId));
            heartBeatTask.setStatus(TaskDto.STATUS_STOP);
            when(logCollectorService.queryTask(eq(userDetail), eq(connections))).thenReturn(heartBeatTask);
			logCollectorService.startConnHeartbeat(userDetail, taskDto);
			verify(monitoringLogsService, times(1)).startTaskErrorLog(any(TaskDto.class), any(UserDetail.class), anyString(), any(Level.class));
			verify(taskService, times(1)).start(eq(heartBeatTask.getId()), eq(userDetail));
        }
	}
	@Nested
	class tableInfosTest{
		private String taskId;
		private String connectionId;
		private String keyword;
		private DataSourceService dataSourceService;
		private ShareCdcTableMetricsService shareCdcTableMetricsService;
		@BeforeEach
		void beforeEach(){
			taskId = new ObjectId().toHexString();
			connectionId = new ObjectId().toHexString();
			keyword = "";
			dataSourceService = mock(DataSourceService.class);
			ReflectionTestUtils.setField(logCollectorService, "dataSourceService", dataSourceService);
			shareCdcTableMetricsService = mock(ShareCdcTableMetricsService.class);
			ReflectionTestUtils.setField(logCollectorService, "shareCdcTableMetricsService", shareCdcTableMetricsService);
		}
		@Test
		@DisplayName("test tableInfos method when logCollectorConnConfigs is null")
		void test1(){
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(null);
			List<String> connIds = new ArrayList<>();
			connIds.add(connectionId);
			when(logCollectorNode.getConnectionIds()).thenReturn(connIds);
			List<String> tableNames = new ArrayList<>();
			tableNames.add("table_1");
			tableNames.add("table_2");
			when(logCollectorNode.getTableNames()).thenReturn(tableNames);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(mock(DataSourceConnectionDto.class));
			Page<ShareCdcTableInfo> actual = logCollectorService.tableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals("table_1", actual.getItems().get(0).getName());
		}
		@Test
		@DisplayName("test tableInfos method when logCollectorConnConfigs and tableNames are empty")
		void test4(){
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(new HashMap<>());
			List<String> connIds = new ArrayList<>();
			connIds.add(connectionId);
			when(logCollectorNode.getConnectionIds()).thenReturn(connIds);
			List<String> tableNames = new ArrayList<>();
			when(logCollectorNode.getTableNames()).thenReturn(tableNames);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(mock(DataSourceConnectionDto.class));
			Page<ShareCdcTableInfo> actual = logCollectorService.tableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals(0, actual.getItems().size());
		}
		@Test
		@DisplayName("test tableInfos method for new version")
		void test2(){
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			List<String> tableNames = new ArrayList<>();
			tableNames.add("table_1");
			tableNames.add("table_2");
			Map<String, LogCollecotrConnConfig> logCollectorConnConfigs = new HashMap<>();
			LogCollecotrConnConfig logCollecotrConnConfig = mock(LogCollecotrConnConfig.class);
			logCollectorConnConfigs.put(connectionId, logCollecotrConnConfig);
			when(logCollecotrConnConfig.getTableNames()).thenReturn(tableNames);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(logCollectorConnConfigs);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(mock(DataSourceConnectionDto.class));
			Page<ShareCdcTableInfo> actual = logCollectorService.tableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals("table_1", actual.getItems().get(0).getName());
		}
		@Test
		@DisplayName("test tableInfos method for new version and tableNames is empty")
		void test5(){
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			List<String> tableNames = new ArrayList<>();
			Map<String, LogCollecotrConnConfig> logCollectorConnConfigs = new HashMap<>();
			LogCollecotrConnConfig logCollecotrConnConfig = mock(LogCollecotrConnConfig.class);
			logCollectorConnConfigs.put(connectionId, logCollecotrConnConfig);
			when(logCollecotrConnConfig.getTableNames()).thenReturn(tableNames);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(logCollectorConnConfigs);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(mock(DataSourceConnectionDto.class));
			Page<ShareCdcTableInfo> actual = logCollectorService.tableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals(0, actual.getItems().size());
		}
		@Test
		@DisplayName("test tableInfos method when two conn has same table name")
		void test3(){
			connectionId = "";
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			List<String> tableNames = new ArrayList<>();
			tableNames.add("table_1");
			tableNames.add("table_2");
			Map<String, LogCollecotrConnConfig> logCollectorConnConfigs = new HashMap<>();
			LogCollecotrConnConfig logCollecotrConnConfig = mock(LogCollecotrConnConfig.class);
			String connId1 = new ObjectId().toHexString();
			String connId2 = new ObjectId().toHexString();
			logCollectorConnConfigs.put(connId1, logCollecotrConnConfig);
			logCollectorConnConfigs.put(connId2, logCollecotrConnConfig);
			when(logCollecotrConnConfig.getTableNames()).thenReturn(tableNames);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(logCollectorConnConfigs);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			DataSourceConnectionDto connectionDto = mock(DataSourceConnectionDto.class);
			when(connectionDto.getName()).thenReturn("conn_name");
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(connectionDto);
			Page<ShareCdcTableInfo> actual = logCollectorService.tableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals("table_1", actual.getItems().get(0).getName());
			assertEquals(4, actual.getItems().size());
		}
		@Test
		@DisplayName("test tableInfos method when tableNames1 is empty")
		void test6(){
			connectionId = "";
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			List<String> tableNames = new ArrayList<>();
			Map<String, LogCollecotrConnConfig> logCollectorConnConfigs = new HashMap<>();
			LogCollecotrConnConfig logCollecotrConnConfig = mock(LogCollecotrConnConfig.class);
			logCollectorConnConfigs.put(connectionId, logCollecotrConnConfig);
			when(logCollecotrConnConfig.getTableNames()).thenReturn(tableNames);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(logCollectorConnConfigs);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			DataSourceConnectionDto connectionDto = mock(DataSourceConnectionDto.class);
			when(connectionDto.getName()).thenReturn("conn_name");
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(connectionDto);
			Page<ShareCdcTableInfo> actual = logCollectorService.tableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals(0, actual.getItems().size());
		}
	}

	@Nested
	class excludeTableInfosTest{
		private String taskId;
		private String connectionId;
		private String keyword;
		private DataSourceService dataSourceService;
		private ShareCdcTableMetricsService shareCdcTableMetricsService;
		@BeforeEach
		void beforeEach(){
			taskId = new ObjectId().toHexString();
			connectionId = new ObjectId().toHexString();
			keyword = "";
			dataSourceService = mock(DataSourceService.class);
			ReflectionTestUtils.setField(logCollectorService, "dataSourceService", dataSourceService);
			shareCdcTableMetricsService = mock(ShareCdcTableMetricsService.class);
			ReflectionTestUtils.setField(logCollectorService, "shareCdcTableMetricsService", shareCdcTableMetricsService);
		}
		@Test
		@DisplayName("test excludeTableInfos method when logCollectorConnConfigs is null")
		void test1(){
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(null);
			List<String> connIds = new ArrayList<>();
			connIds.add(connectionId);
			when(logCollectorNode.getConnectionIds()).thenReturn(connIds);
			List<String> tableNames = new ArrayList<>();
			tableNames.add("table_1");
			tableNames.add("table_2");
			when(logCollectorNode.getExclusionTables()).thenReturn(tableNames);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(mock(DataSourceConnectionDto.class));
			Page<ShareCdcTableInfo> actual = logCollectorService.excludeTableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals("table_1", actual.getItems().get(0).getName());
		}
		@Test
		@DisplayName("test excludeTableInfos method when logCollectorConnConfigs and tableNames are empty")
		void test4(){
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(new HashMap<>());
			List<String> connIds = new ArrayList<>();
			connIds.add(connectionId);
			when(logCollectorNode.getConnectionIds()).thenReturn(connIds);
			List<String> tableNames = new ArrayList<>();
			when(logCollectorNode.getExclusionTables()).thenReturn(tableNames);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(mock(DataSourceConnectionDto.class));
			Page<ShareCdcTableInfo> actual = logCollectorService.excludeTableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals(0, actual.getItems().size());
		}
		@Test
		@DisplayName("test excludeTableInfos method for new version")
		void test2(){
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			List<String> tableNames = new ArrayList<>();
			tableNames.add("table_1");
			tableNames.add("table_2");
			Map<String, LogCollecotrConnConfig> logCollectorConnConfigs = new HashMap<>();
			LogCollecotrConnConfig logCollecotrConnConfig = mock(LogCollecotrConnConfig.class);
			logCollectorConnConfigs.put(connectionId, logCollecotrConnConfig);
			when(logCollecotrConnConfig.getExclusionTables()).thenReturn(tableNames);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(logCollectorConnConfigs);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(mock(DataSourceConnectionDto.class));
			Page<ShareCdcTableInfo> actual = logCollectorService.excludeTableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals("table_1", actual.getItems().get(0).getName());
		}
		@Test
		@DisplayName("test excludeTableInfos method for new version and tableNames is empty")
		void test5(){
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			List<String> tableNames = new ArrayList<>();
			Map<String, LogCollecotrConnConfig> logCollectorConnConfigs = new HashMap<>();
			LogCollecotrConnConfig logCollecotrConnConfig = mock(LogCollecotrConnConfig.class);
			logCollectorConnConfigs.put(connectionId, logCollecotrConnConfig);
			when(logCollecotrConnConfig.getExclusionTables()).thenReturn(tableNames);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(logCollectorConnConfigs);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(mock(DataSourceConnectionDto.class));
			Page<ShareCdcTableInfo> actual = logCollectorService.excludeTableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals(0, actual.getItems().size());
		}
		@Test
		@DisplayName("test excludeTableInfos method when two conn has same table name")
		void test3(){
			connectionId = "";
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			List<String> tableNames = new ArrayList<>();
			tableNames.add("table_1");
			tableNames.add("table_2");
			Map<String, LogCollecotrConnConfig> logCollectorConnConfigs = new HashMap<>();
			LogCollecotrConnConfig logCollecotrConnConfig = mock(LogCollecotrConnConfig.class);
			String connId1 = new ObjectId().toHexString();
			String connId2 = new ObjectId().toHexString();
			logCollectorConnConfigs.put(connId1, logCollecotrConnConfig);
			logCollectorConnConfigs.put(connId2, logCollecotrConnConfig);
			when(logCollecotrConnConfig.getExclusionTables()).thenReturn(tableNames);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(logCollectorConnConfigs);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			DataSourceConnectionDto connectionDto = mock(DataSourceConnectionDto.class);
			when(connectionDto.getName()).thenReturn("conn_name");
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(connectionDto);
			Page<ShareCdcTableInfo> actual = logCollectorService.excludeTableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals("table_1", actual.getItems().get(0).getName());
			assertEquals(4, actual.getItems().size());
		}
		@Test
		@DisplayName("test excludeTableInfos method when tableNames1 is empty")
		void test6(){
			connectionId = "";
			TaskDto shareCdcTask = new TaskDto();
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			LogCollectorNode logCollectorNode = mock(LogCollectorNode.class);
			List<String> tableNames = new ArrayList<>();
			Map<String, LogCollecotrConnConfig> logCollectorConnConfigs = new HashMap<>();
			LogCollecotrConnConfig logCollecotrConnConfig = mock(LogCollecotrConnConfig.class);
			logCollectorConnConfigs.put(connectionId, logCollecotrConnConfig);
			when(logCollecotrConnConfig.getExclusionTables()).thenReturn(tableNames);
			when(logCollectorNode.getLogCollectorConnConfigs()).thenReturn(logCollectorConnConfigs);
			sources.add(logCollectorNode);
			when(dag.getSources()).thenReturn(sources);
			shareCdcTask.setDag(dag);
			when(taskService.findById(MongoUtils.toObjectId(taskId), userDetail)).thenReturn(shareCdcTask);
			DataSourceConnectionDto connectionDto = mock(DataSourceConnectionDto.class);
			when(connectionDto.getName()).thenReturn("conn_name");
			when(dataSourceService.findById(any(ObjectId.class), any(Field.class), any(UserDetail.class))).thenReturn(connectionDto);
			Page<ShareCdcTableInfo> actual = logCollectorService.excludeTableInfos(taskId, connectionId, keyword, 1, 10, "", userDetail);
			assertEquals(0, actual.getItems().size());
		}
	}

	@Test
	void testFindNormal() {
		Filter filter = new Filter();
		Field field = new Field();
		filter.setFields(field);
		UserDetail user = mock(UserDetail.class);
		Page<TaskDto> taskDtoPage = new Page<>();
		taskDtoPage.setTotal(1);
		when(taskService.find(filter, user)).thenReturn(taskDtoPage);
		Page<LogCollectorVo> actual = logCollectorService.find(filter, user);
		assertEquals(1, actual.getTotal());
	}

	@Test
	void testFindByConnectionNameNormal() {
		String name = "test";
		String connectionName = "mysql";
		UserDetail user = mock(UserDetail.class);
		int skip = 0;
		int limit = 1;
		List<String> sort = new ArrayList<>();
		DataSourceService dataSourceService = mock(DataSourceService.class);
		ReflectionTestUtils.setField(logCollectorService, "dataSourceService", dataSourceService);
		List<DataSourceConnectionDto> connectionDtos = new ArrayList<>();
		when(dataSourceService.findAll(any(Query.class))).thenReturn(connectionDtos);
		when(taskService.count(any(Query.class))).thenReturn(1L);
		Page<LogCollectorVo> actual = logCollectorService.findByConnectionName(name, connectionName, user, skip, limit, sort);
		assertEquals(1, actual.getTotal());
	}
}
