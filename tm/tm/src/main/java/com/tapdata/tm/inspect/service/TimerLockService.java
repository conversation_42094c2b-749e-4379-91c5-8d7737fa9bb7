package com.tapdata.tm.inspect.service;

import cn.hutool.core.date.DatePattern;
import com.tapdata.tm.base.service.BaseService;
import com.tapdata.tm.commons.base.dto.BaseDto;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.inspect.dto.InspectDto;
import com.tapdata.tm.inspect.entity.InspectEntity;
import com.tapdata.tm.inspect.entity.TimerLockEntity;
import com.tapdata.tm.inspect.repository.InspectRepository;
import com.tapdata.tm.utils.TimeUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class TimerLockService extends BaseService {
    @Autowired
    MongoOperations mongoOperations;

    public TimerLockService(@NonNull InspectRepository repository) {
        super(repository, InspectDto.class, InspectEntity.class);
    }


    @Override
    protected void beforeSave(BaseDto dto, UserDetail userDetail) {

    }


    public TimerLockEntity tryGetLock(String prcessName) {
        Date now = new Date();
        Date hour = TimeUtil.format(now,DatePattern.NORM_DATETIME_MINUTE_PATTERN);
        Query query = Query.query(Criteria.where("hour").is(hour));//查询的字段必须是唯一索引才行，原子性才能生效

        Update update = new Update();
        update.setOnInsert("tmProcessName", prcessName);
        update.set("createTime", new Date());
        TimerLockEntity measurementLockEntity = mongoOperations.findAndModify(query, update, FindAndModifyOptions.options().upsert(true).returnNew(true), TimerLockEntity.class);
        log.info(measurementLockEntity.getId().toString());
        return measurementLockEntity;
    }
}
