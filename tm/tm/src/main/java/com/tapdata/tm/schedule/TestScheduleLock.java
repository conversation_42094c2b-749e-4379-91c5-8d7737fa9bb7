package com.tapdata.tm.schedule;

import net.javacrumbs.shedlock.core.LockAssert;
import org.apache.commons.lang3.RandomUtils;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/11/11 6:13 下午
 * @description
 */
public class TestScheduleLock {

	/*@Scheduled(fixedDelay = 10000L)
	@SchedulerLock(name = "testScheduleLock", lockAtLeastFor = "10s", lockAtMostFor = "10s")*/
	public void testScheduleLock(){

		LockAssert.assertLocked();
		System.out.println("在执行");

		try {
			Thread.sleep(RandomUtils.nextInt(20, 50) * 1000);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}

		System.out.println("执行完成");
	}

}
