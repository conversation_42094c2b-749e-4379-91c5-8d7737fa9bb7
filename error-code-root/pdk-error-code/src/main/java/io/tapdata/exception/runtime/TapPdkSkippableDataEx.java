package io.tapdata.exception.runtime;

import io.tapdata.PDKExCode_10;
import io.tapdata.exception.TapPdkBaseException;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2023/4/26 11:39 Create
 */
public class TapPdkSkippableDataEx extends TapPdkBaseException {
    public TapPdkSkippableDataEx(String message, String pdkId) {
        super(PDKExCode_10.SKIPPABLE_DATA, message, pdkId);
    }

    public TapPdkSkippableDataEx(String message, String pdkId, Throwable cause) {
        super(PDKExCode_10.SKIPPABLE_DATA, message, pdkId, cause);
    }

    @Override
    public String getMessage() {
        return String.format("Data failed in %s, %s", pdkId, super.getMessage());
    }
}
