{"connectionConfig": {"client_id": "1000.33EVKI7TCHY661GPOPMCXDEF4N2T6H", "client_secret": "441ed39ecd48ed5608e1143ffe5453a47585824b55", "refreshToken": "**********************************************************************", "accessToken": "**********************************************************************", "code": "**********************************************************************"}, "nodeConfig": {}, "functionParams": {"discoverSchema": {}, "connectionTest": {}, "insertRecord": {"eventDataList": [{"eventType": "i", "afterData": {}, "beforeData": {}, "referenceTime": 0, "tableName": "example_table_lark"}]}, "deleteRecord": {"eventDataList": [{"eventType": "i", "afterData": {}, "beforeData": {}, "referenceTime": 0, "tableName": "example_table_lark"}]}, "updateRecord": {"eventDataList": [{"eventType": "i", "afterData": {}, "beforeData": {}, "referenceTime": 0, "tableName": "example_table_lark"}]}, "writeRecord": {"eventDataList": [{"eventType": "i", "afterData": {}, "beforeData": {}, "referenceTime": 0, "tableName": "example_table_lark"}]}, "batchRead": {"tableName": "Leads", "pageSize": 500}, "batchCount": {"tableName": "Leads"}, "streamRead": {"offset": {}, "tableNameList": ["Leads"], "pageSize": 500}, "commandCallback": {"commandInfo": {"command": "DescribeUserProjects", "action": "", "argMap": {}, "time": 0}}, "webhookEvent": {"tableNameList": ["Leads"], "eventDataMap": {}}}}