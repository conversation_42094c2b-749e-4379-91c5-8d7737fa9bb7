/**
 * <AUTHOR>
 * @date 2021/5/27 下午9:21
 * @description
 */
let m = require('./index');

console.log(m.encryptPassword('admin'))

console.log(m.comparePassword('admin', '$2a$10$VmrRIazvMqgNc.S8kSx5Ju0GOZnY3lh.vS7VLgNBJ52t4TVrqb77K'))

/*
db.user.find().toArray().map(u => {
    return {
        uid: u._id.valueOf(),
        nickname: u.username || u.email,
        account_id: u._id.valueOf(),
        mail: u.email,
        emailVerified: u.emailVerified,
        password: u.password,
        loginsCount: 0
    }
})*/
