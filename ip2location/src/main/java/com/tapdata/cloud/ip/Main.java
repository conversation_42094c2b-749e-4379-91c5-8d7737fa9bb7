package com.tapdata.cloud.ip;

import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.model.UpdateOneModel;
import net.renfei.ip2location.IP2Location;
import net.renfei.ip2location.IPResult;
import net.renfei.ip2location.IPTools;
import org.apache.commons.cli.*;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2024/1/16 11:38
 */
public class Main {

    private Logger logger = Logger.getLogger(Main.class.getName());

    private final Mongo mongo;
    private final String ipv4BinFile;
    private final String ipv6BinFile;

    public Main(String mongoUrl, String ipv4BinFile, String ipv6BinFile) {
        this.mongo = new Mongo(mongoUrl);
        this.ipv4BinFile = ipv4BinFile;
        this.ipv6BinFile = ipv6BinFile;
    }

    private IP2Location getIP2Location(String binFile) throws IOException {
        if ( binFile == null) {
            return null;
        }
        File file = new File(binFile);
        if (file.exists()) {
            IP2Location ip2Location = new IP2Location();
            ip2Location.Open(binFile, false);
            return ip2Location;
        }
        return null;
    }

    private void run() {

        mongo.completeUserLastIp();

        queryGeolocationByUserIp();

        this.mongo.disconnect();
    }

    private void queryGeolocationByUserIp() {
        IPTools ipTools = new IPTools();
        IP2Location ipv4IP2Location;
        IP2Location ipv6IP2Location;
        try {
            ipv4IP2Location = getIP2Location(this.ipv4BinFile);
            ipv6IP2Location = getIP2Location(this.ipv6BinFile);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        List<Document> users = this.mongo.findNeedUpdateLocationUsers();
        if (users != null && !users.isEmpty()) {
            logger.log(Level.INFO, String.format("Found %s users to update location", users.size()));
            Map<ObjectId, Document> locations = new HashMap<>();

            Function<Document, Document> updateFunction = data -> {
                Document update = new Document();
                Document set = new Document();
                set.put("location", data);
                update.put("$set", set);
                return update;
            };

            for (Document user : users) {
                ObjectId userId = user.getObjectId("_id");
                String lastIp = null;
                Object userInfo = user.get("userInfo");
                if (userInfo instanceof Document) {
                    lastIp = ((Document) userInfo).getString("lastIp");
                }

                IPResult ipResult = null;
                if (lastIp != null) {
                    try {
                        if (ipTools.IsIPv4(lastIp) && ipv4IP2Location != null) {
                            ipResult = ipv4IP2Location.IPQuery(lastIp);
                        } else if (ipTools.IsIPv6(lastIp) && ipv6IP2Location != null) {
                            ipResult = ipv6IP2Location.IPQuery(lastIp);
                        }
                    } catch (IOException e) {
                        logger.log(Level.WARNING,String.format("Query location failed by ip %s", lastIp));
                        continue;
                    }
                }

                if (ipResult != null) {
                    String json = Utils.toJson(ipResult);
                    Document location = Document.parse(json);
                    List<String> clearKeys = location.keySet().stream()
                            .filter(key -> {
                                if (location.get(key) == null) {
                                    return true;
                                }
                                Object value = location.get(key);
                                if (value instanceof String &&
                                        value.toString().toLowerCase().contains("not_supported")) {
                                    return true;
                                }
                                return false;
                            }).collect(Collectors.toList());
                    for (String key : clearKeys) {
                        location.remove(key);
                    }

                    locations.put(userId, location);
                }

                if (locations.size() >= 100) {
                    BulkWriteResult result = mongo.batchUpdate(locations, updateFunction);
                    logger.log(Level.INFO, String.format("Update success %s", result.getModifiedCount()));
                    locations.clear();
                }
            }

            if (locations.size() > 0) {
                BulkWriteResult result = mongo.batchUpdate(locations, updateFunction);
                logger.log(Level.INFO, String.format("Update success %s", result.getModifiedCount()));
                locations.clear();
            }
        }

        if (ipv4IP2Location != null) {
            ipv4IP2Location.Close();
        }
        if (ipv6IP2Location != null) {
            ipv6IP2Location.Close();
        }
    }

    public static void main(String[] args) throws ParseException {
        Options options = new Options();
        options.addOption("u", "url", true, "MongoDB connection string for tcm database.");
        options.addOption("4", "ipv4", true, "IPv4 IP2Location LITE data");
        options.addOption("6", "ipv6", true, "IPv6 IP2Location LITE data");
        options.addOption("h", "help", false, "Print help usage.");

        CommandLineParser parser = new DefaultParser();
        CommandLine cmd = parser.parse(options, args);

        String mongoUrl = cmd.hasOption("url") ? cmd.getOptionValue("url") : null;
        String ipv4BinFile = cmd.hasOption("ipv4") ? cmd.getOptionValue("ipv4") : null;
        String ipv6BinFile = cmd.hasOption("ipv6") ? cmd.getOptionValue("ipv6") : null;

        if (cmd.hasOption("help") ||
                StringUtils.isEmpty(mongoUrl) ||
                (StringUtils.isEmpty(ipv4BinFile) &&
                StringUtils.isEmpty(ipv6BinFile))) {
            HelpFormatter formatter = new HelpFormatter();
            formatter.printHelp("IP2Location", options);
            System.exit(1);
        }

        new Main(mongoUrl, ipv4BinFile, ipv6BinFile).run();
    }

}
