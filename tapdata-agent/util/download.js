const path = require('path');
const fs = require('fs');
const readline = require('readline');
const basic = require('../basic');
const logger = require('../log/log');
const {sendBehavior} = require("./sendBehavior");
exports.download = async function(conf, downloadUrl, fileName, dir, ws, process_id, num, onlyDownload) {
    let http = require('http');
    if (downloadUrl.includes("https"))
        http = require('https');
    try{
        if(!dir || dir==='')dir = 'update'
        fs.mkdirSync(path.join(process.cwd(),dir));//conf.TAPDATA_HOME
    }catch(e){}
    logger.info('Downloading components...');
    let filename = path.join(process.cwd(), dir, fileName);//conf.TAPDATA_HOME  tapdata-agent.jar
    try {
        fs.unlinkSync(filename);
    } catch (e) {
    }
    let nowTime = new Date().getTime();
    const promise = new Promise(function (resolve, reject) {
        const url = downloadUrl + fileName;//cloud.tapdata.net
        const file = fs.createWriteStream(filename);
        http.get(url, (res, err) => {
            try {
                if (err) {
                    logger.error(err)
                    file.close();
                    fs.unlinkSync(filename);
                    reject(err);
                }
                if (res.statusCode !== 200) {
                    logger.error(res.statusCode,err);
                    console.info('Download file failed');
                    reject('statusCode:' + res.statusCode);
                }
                res.on('end', async () => {
                    //console.info('finish download');
                });
                let cur = 0;
                let len = parseInt(res.headers['content-length'], 10);
                let unit = 'B';
                let total = len;// / 1048576;
                if(total >= 1048576){
                    total = total/1048576;
                    unit = 'MB'
                }else if(total > 1024){
                    total = total/1024;
                    unit = 'KB'
                }
                let remaining = 0;
                let start = Date.now();
                let remainingMinutes = 0;
                let remainingSeconds = 0;
                let speed = 0;
                let speedStr = "";
                //logger.info("Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " MB" + ", Total size: " + total.toFixed(2) + " " + unit);
                res.on('data', (m) => {
                    readline.clearLine(process.stdout, 0);
                    readline.moveCursor(process.stdout, 0, -1);
                    cur += m.length;
                    speed = Math.floor(cur / (Date.now() - start + 1) / 1024 * 1000);
                    speedStr = speed + " KB/s";
                    if (speed > 1024) {
                        speedStr = (speed / 1024).toFixed(1) + " MB/s";
                    }
                    remaining = Math.floor((len - cur) / (cur / (Date.now() - start + 1)) / 1000);
                    remainingMinutes = Math.floor(remaining / 60);
                    remainingSeconds = remaining - 60 * remainingMinutes;
                    if(new Date().getTime() - nowTime > 3000) {
                        let progres;
                        if(num){
                            progres = (100.0 * cur / len).toFixed(2) * 0.5;
                            if(num === 2){
                                progres = progres + 50
                            }
                            //progres = progres.toString();
                        }
                        if(!onlyDownload){
                            basic.handleSendMsg(ws, 'updateMsg', {
                                process_id: process_id,
                                status: 'downloading',
                                progres: progres.toFixed(2),
                                msg: "Downloading " + fileName + ' ' + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " MB" + ", Total size: " + total.toFixed(2) + " " + unit + ", avg speed: " + speedStr + ", remaining time: " + remainingMinutes + "m " + remainingSeconds + "s     "
                            });
                        }
                        nowTime = new Date().getTime()
                    }
                    console.info("Downloading "+ fileName+' ' + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " MB" + ", Total size: " + total.toFixed(2) + " " + unit + ", avg speed: " + speedStr + ", remaining time: " + remainingMinutes + "m " + remainingSeconds + "s     ");
                });
                file.on('finish', async () => {
                    //console.info('finished')
                    let md5filename = path.join(process.cwd(), dir, '.md5sum');
                    let md5Value;
                    let checkOk = true;
                    try{
                        md5Value = fs.readFileSync(md5filename).toString();
                    }catch (e) {}
                    if(md5Value){
                        const fileMd5 = await basic.readBigFileMD5(filename);
                        md5Value = md5Value.split('\n');
                        for(let z in md5Value){
                            let md5Item = md5Value[z];
                            md5Item = md5Item.split('  ');
                            if(md5Item.length > 1){
                                if(fileName === md5Item[1]){
                                    checkOk = fileMd5 === md5Item[0];
                                    break;
                                }
                            }
                        }
                    }
                    console.info('checkOk:',checkOk)
                    if(!onlyDownload) {
                        if(checkOk) {
                            if (num === 2) {
                                basic.handleSendMsg(ws, 'updateMsg', {
                                    process_id: process_id,
                                    status: 'downloading',
                                    progres: '100',
                                    msg: "Downloading " + fileName + ' ' + "100% " + total.toFixed(2) + " " + unit + ", Total size: " + total.toFixed(2) + " " + unit + ", avg speed: " + speedStr + ", remaining time: " + remainingMinutes + "m " + remainingSeconds + "s     "
                                });
                            } else {
                                basic.handleSendMsg(ws, 'updateMsg', {
                                    process_id: process_id,
                                    status: 'downloading',
                                    msg: "Downloading " + fileName + ' ' + "100% " + total.toFixed(2) + " " + unit + ", Total size: " + total.toFixed(2) + " " + unit + ", avg speed: " + speedStr + ", remaining time: " + remainingMinutes + "m " + remainingSeconds + "s     "
                                });
                            }
                        } else {

                            logger.error( fileName + ' down load fail. check MD5 fail.');
                            reject();
                        }
                    }
                    file.close();
                    resolve();
                }).on('error', (err) => {
                    logger.error(err);
                    reject(err);
                    try {
                        fs.unlinkSync(filename);
                    } catch (e) {
                    }
                });
                res.pipe(file);
            }catch(e){
                logger.error(e);
                reject(e);
            }
        }).on('error',(err)=>{
            logger.error(err);
            reject(err);
        });
    });
    try {
        await promise;
    }catch (e) {
        logger.error(e)
        sendBehavior(conf || {},{code:'downloadAgent',result:'failed',msg:e});
        return false
    }
    return true
}
