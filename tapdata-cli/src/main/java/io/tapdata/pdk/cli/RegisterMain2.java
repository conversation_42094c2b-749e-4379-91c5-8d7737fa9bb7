package io.tapdata.pdk.cli;

import org.apache.commons.io.FilenameUtils;

import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * <PERSON><PERSON><PERSON>li aims to be the easiest way to create rich command line applications that can run on and off the JVM. Considering picocli? Check what happy users say about picocli.
 * https://picocli.info/
 *
 * <AUTHOR>
 */
public class RegisterMain2 {
    private static final String BASE_PATH = basePath();

    private enum ConnectorEnums {
 //       Mysql(BASE_PATH + "/dist/mysql-connector-v1.0-SNAPSHOT.jar", "all", "mysql", "basic", "jdbc"),
        //Postgres(BASE_PATH + "dist/postgres-connector-v1.0-SNAPSHOT.jar", "all", "postgres", "basic", "jdbc"),
 //       ORACLE(BASE_PATH + "/dist/oracle-connector-v1.0-SNAPSHOT.jar", "all", "oracle", "basic", "jdbc"),
        //Custom(BASE_PATH + "/dist/custom-connector-v1.0-SNAPSHOT.jar", "all", "custom"),
        //Elasticsearch(BASE_PATH + "/dist/elasticsearch-connector-v1.0-SNAPSHOT.jar", "all", "elasticsearch"),
        //Mongodb_Atlas(BASE_PATH + "/dist/mongodb-atlas-connector-v1.0-SNAPSHOT.jar", "all", "mongodb-atlas"),
//        Mongodb(BASE_PATH + "/dist/mongodb-connector-v1.0-SNAPSHOT.jar", "all", "mongodb", "basic", "jdbc"),
        MSSQL(BASE_PATH + "/dist/mssql-connector-v1.0-SNAPSHOT.jar", "all", "mssql", "basic", "jdbc"),
//        DB2(BASE_PATH + "/dist/db2-connector-v1.0-SNAPSHOT.jar", "all", "db2", "basic", "jdbc"),
//       SYBASE(BASE_PATH + "/dist/sybase-connector-v1.0-SNAPSHOT.jar", "all", "sybase")
        ;

        private final String path;
        private final Set<String> tags = new HashSet<>();

        ConnectorEnums(String path, String... tags) {
            this.path = path;
            if (null != tags) {
                this.tags.addAll(Arrays.asList(tags));
            }
        }

        public boolean contains(String... tags) {
            for (String s : tags) {
                if (this.tags.contains(s)) return true;
            }
            return false;
        }

        public static void addByTags(List<String> postList, String... tags) {
            for (ConnectorEnums c : ConnectorEnums.values()) {
                if (c.contains(tags)) {
                    postList.add(c.path);
                }
            }
        }
    }

    public static void main(String... args) {
        // VM options samples:
        // -Dtags=all -Dserver=http://localhost:3000
        // -Dtags=dummy,mysql
        // -Dserver=http://*************:31966
        // -Dserver=http://*************:31787
        // -Dserver=http://*************:31321
        // -Dbeta=true

        List<String> postList = new ArrayList<>();

        //1.local
        String server = System.getProperty("server", "http://localhost:5173");
        Collections.addAll(postList, "register", "-a", "3324cfdf-7d3e-4792-bd32-571638d4562f", "-ak", "", "-sk", "", "-t", server);

        //2.cloud-test
//        String server = System.getProperty("server", "https://dev.cloud.tapdata.net:1443/console/tm");
//        Collections.addAll(postList, "register", "-a", "da949282212f4fd7b11796cf2599f9a822a7826ee3e04ca9ac7e75e897fa0b9c", "-ak", "eLpLinDtsDgCzrC39URv8K5ATvNRZQoB", "-sk", "S5X0HZRMmWpkcmVXcnzqCvMlBEyA03pL", "-t", server);

        //3.cloud-net
//       String server = System.getProperty("server", "https://cloud.tapdata.net/console/v3/tm");
//        Collections.addAll(postList, "register", "-a", "da949282212f4fd7b11796cf2599f9a822a7826ee3e04ca9ac7e75e897fa0b9c", "-ak", "tCnHIpwY1KdPHFhFgiIMtkdXzU95dCdY", "-sk", "nfM7BI8y30n42IkNrZWrO4IU4tHBxRAi", "-t", server);

        //4.international
//        String server = System.getProperty("server", "http://cloud.tapdata.io/console/tm");
//        Collections.addAll(postList, "register", "-a", "da949282212f4fd7b11796cf2599f9a822a7826ee3e04ca9ac7e75e897fa0b9c", "-ak", "tCnHIpwY1KdPHFhFgiIMtkdXzU95dCdY", "-sk", "nfM7BI8y30n42IkNrZWrO4IU4tHBxRAi", "-t", server);

        String[] tags = System.getProperty("tags", "all").split(",");
        ConnectorEnums.addByTags(postList, tags);
        Main.registerCommands().execute(postList.toArray(new String[0]));
        System.exit(0);
    }

    private static String basePath() {
        URL resource = RegisterMain.class.getClassLoader().getResource("");
        if (null == resource) {
            return "/";
        }

        try {
            Path path = Paths.get(resource.getPath() + "../../../");
            String basePath = path.toFile().getCanonicalPath() + "/";
            System.out.println("basePath:" + basePath);
            return basePath;
        } catch (Throwable throwable) {
            return FilenameUtils.concat(resource.getPath(), "../../../");
        }

    }
}
