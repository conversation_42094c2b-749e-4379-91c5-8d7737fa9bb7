spring:
    profiles:
        active: 'prod'
        include: 'dfs,default'

    data:
        mongodb:
            uri: 'mongodb+srv://root:<EMAIL>/dfs_tm'
            cursorBatchSize: 1000
    elasticsearch:
        rest:
            uris: http://192.168.100.252:9201

task:
    log:
        expireDay: 7
        cron: "0 0 * * * ?"
        indexName: logs3
springdoc:
    enabled: true
tcm:
    url: http://dfs-tcm-svc.gcp-test:30103

# logWareHouse: elasticsearch

aliyun:
    accessKey: LTAI5tRs7Bi4t4ngvH94uX17
    accessSecret: ******************************

gateway:
    secret: Q3HraAbDkmKoPzaBEYzPXB1zJXmWlQ169

# Enable weChat notifications will compete accessToken with the production env.
#weChat:
#    mp:
#        appId: wx592f9ef879b35634
#        appSecret: a73b89a5ddb2f61531c0dae8d6c0e94b
alarm:
    channel:
        sms: false
