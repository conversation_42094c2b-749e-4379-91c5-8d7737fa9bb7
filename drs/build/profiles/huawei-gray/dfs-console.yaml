kind: ConfigMap
apiVersion: v1
metadata:
  name: console-config
  namespace: gray
  labels:
    app: dfs-console-config
data:
  config.json: |-
    {
      "ENV": "hw-prod",
      "USER_CENTER": "https://console.us.authing.co/u",
      "topBarLinks": [{
        "text": "header_manual",
        "link": "https://docs.tapdata.io/",
        "icon": "send",
        "type": "handbook"
      }],
      "officialWebsiteAddress": "https://tapdata.io",
      "disabledOnlineChat": true,
      "onlyEnglishLanguage": true,
      "disabledAlibabaCloudComputingNest": true,
      "disabledBindingPhone": true,
      "disabledAliyunCode": true,
      "disabledDataService": true,
      "currencyType": "usd",
      "station": "international",
      "disabledDataVerify": true
    }
  default.conf: |-
    server {

            listen       80;
            listen       [::]:80;

            location / {
                root /apps/console/public;
                add_header  version v3.0;
            }

            error_page  405 =200 $uri;
        }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: console-prod3
  namespace: gray
  labels:
    version: -version-
    app: console-prod3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: console-prod3
  template:
    metadata:
      annotations:
        monitoring.cci.io/enable-pod-metrics: "true"
        monitoring.cci.io/metrics-port: "19100"
      labels:
        app: console-prod3
    spec:
      hostAliases:
      imagePullSecrets:
        - name: imagepull-secret
      containers:
      - name: console-prod3
        image: swr.ap-southeast-1.myhuaweicloud.com/tapdata/dfs-console:-version-
        ports:
          - containerPort: 80
            name: http
            protocol: TCP
        resources:
          limits:
            cpu: "2"
            memory: 4Gi
          requests:
            cpu: "2"
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /
            port: 80
            scheme: HTTP
          initialDelaySeconds: 10
          timeoutSeconds: 1
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 80
            scheme: HTTP
          initialDelaySeconds: 10
          timeoutSeconds: 1
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        volumeMounts:
        - mountPath: /apps/console/public/config/config.json
          name: vol-168060851769991931
          subPath: config.json
        - mountPath: /etc/nginx/conf.d/default.conf
          name: vol-168060851769991932
          subPath: default.conf
      volumes:
      - configMap:
          defaultMode: 420
          items:
            - key: config.json
              path: config.json
          name: console-config
        name: vol-168060851769991931
      - configMap:
          defaultMode: 420
          items:
            - key: default.conf
              path: default.conf
          name: console-config
        name: vol-168060851769991932