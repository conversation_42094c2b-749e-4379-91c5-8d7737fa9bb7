spring:
  profiles:
    active: 'cloud-dev'
    include: 'dfs,default'

  data:
    mongodb:
      uri: 'mongodb://root:Gotapd8!@192.168.1.184:37017/dev_dfs_tcm?authSource=admin'
      cursorBatchSize: 1000
springdoc:
  enabled: true
tm:
  domain: 10.1.5.1:3000
  appKey: 60125b2c24a4973ee4420646
  appSecret: 60125b2c24a4973ee44206471
  backendUrl: http://10.1.5.1:3000/tm/api/

# DFS Authing config

authing:
  userPoolId: 60b061312a380ae777c3aafb
  userPoolSecret: 517736de5054630ea3082fa0d1ec8286
  weChatEventForwardUri: https://core.authing.cn/connections/social/weixin-mp/60b061312a380ae777c3aafb/events

weChat:
  mp:
    appId: wx1a74daefc8c2364c
    appSecret: 91ee69676fa377aee0d664dfe8f7e2cc
    token: L6AEzZBHk3DfTfiztfNL7e80OuQ
    encodingAesKey: qE7lFdlkZPq0xI1ER7j4AUNrkLnn4crJYKwnkrQ68n8
orderlimit: 1


agentCleanUpTime: 7

agentAlarmTime: 6

tasks:
  agentCleanUp:
    cron: 0 0 20 * * ?
    lockDuration: PT1H
  agentAlarm:
    cron: 0 0 10 * * ?
    lockDuration: PT1H
aliyun:
  sms:
    regionId: cn-hangzhou
    accessKey: LTAI5tRs7Bi4t4ngvH94uX17
    accessSecret: ******************************
    domain: dysmsapi.aliyuncs.com
    version: 2017-05-25
    signName: Tapdata
  oss:
    roleArn: acs:ram::1809821306098986:role/stsrole
    bucket: agent-logs-1
    regionId: oss-cn-beijing
    stsRegionId: cn-beijing
    expireDate: 86400 #一天的过期时间
  market:
    enable: true

mail:
  config:
    path: 'classpath:/mail.properties'

server:
  keepAliveTimeout: 70000


Stripe:
   apikey: sk_test_51Mk30VEWLTk9KOyLUyL9lLi2DQE6oK50joPFO6I7B1RXWSOMVKvPweHg1jX8i8G8RsMohyb7jdmXMR2Sz5he09oB00NFFB7LN9
   endpointSecret: whsec_VWT1fFZpealsLzQRqXuMKMzq9cF7SccL

site:
  #type: domesticStation
  type: internationalStation

notifyUri: https://open.feishu.cn/open-apis/bot/v2/hook/c13462c2-bb50-4ce7-b59f-12a8f5b2f079

gcp:
  market:
    enable: false
    # Project ID
    projectId: tapdata-public
    # Service Account Keys
    serviceAccountKeyPath: conf/tapdata-public-************.json
    # Pub/Sub Topic
    topic: gcp_marketplace_message