kind: ConfigMap
apiVersion: v1
metadata:
  name: drs-order-proxy-config
  namespace: drs
  labels:
    app: drs-order-proxy-config
data:
  proxy.json: |
    {
        "useSsl": false,
        "useCas": false,
        "userSafe": false,
        "port": 30101,
        "casServerPath": "https://ecloud.10086.cn:31015",
        "casServicePreFix": "https://order.cmecloud.cn:30101",
        "casIgnore": ["/health", "/version"],
        "logPath": "logs",
        "sessionConfig": {
            "cookie": {
                "secure": true,
                "sameSite": "none"
            },
            "proxy": "force"
        },
        "proxyConfig": [
            {
                "name": "drs-tcm-api-health",
                "context": "/health",
                "needToken": false,
                "needUserName": false,
                "options": {
                    "target": "http://drs-tcm-svc.drs:3000",
                    "secure": false
                }
            }
        ]
    }
---

kind: ConfigMap
apiVersion: v1
metadata:
  name: drs-order-config
  namespace: drs
  labels:
    app: drs-order-config
data:
  cfger.json: |
    {
        "OPURL":"https://ecloud.10086.cn:31015",
        "PURCHASEURL":"https://drs-order-wuxi-1.cmecloud.cn:30100/purchase.html#/",
        "CONSOLEURL":"https://drs-order-wuxi-1.cmecloud.cn:30100/#/",
        "NOCAPTCHA": true
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: drs-order
  namespace: drs
  labels:
    version: -version-
    app: drs-order
spec:
  replicas: 1
  selector:
    matchLabels:
      app: drs-order
  template:
    metadata:
      labels:
        app: drs-order
    spec:
      hostAliases:
        - ip: *************
          hostnames:
            - "ecloud.10086.cn"
      containers:
        - name: drs-order
          image: tapdata/drs-order:-version-
          imagePullPolicy: IfNotPresent
          env:
            - name: CONFIG_FILE_PATH
              value: "public/dist/config/proxy.json"
          ports:
            - containerPort: 30100
              name: https
          resources:
            limits:
              cpu: "1"
              memory: "1Gi"
            requests:
              cpu: "1"
              memory: "1Gi"
          volumeMounts:
            - mountPath: /apps/public/dist/config
              name: drs-order-proxy-config
            - mountPath: /apps/public/dist/static
              name: drs-order-config
      nodeSelector:
        drs-svc: 'true'
      volumes:
        - name: drs-order-proxy-config
          configMap:
            name: drs-order-proxy-config
        - name: drs-order-config
          configMap:
            name: drs-order-config
---
apiVersion: v1
kind: Service
metadata:
  name: drs-order-svc
  labels:
    app: drs-order
  namespace: drs
spec:
  ports:
    - port: 30101
      targetPort: 30101
      nodePort: 30101
      name: webhttps
  selector:
    app: drs-order
#  sessionAffinity: ClientIP
  type: NodePort
