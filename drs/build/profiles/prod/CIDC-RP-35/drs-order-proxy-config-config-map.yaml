kind: ConfigMap
apiVersion: v1
metadata:
  name: drs-order-proxy-config
  namespace: drs
  labels:
    app: drs-order-proxy-config
data:
  proxy.json: |
    {
        "useSsl": false,
        "useCas": false,
        "userSafe": false,
        "port": 31056,
        "casServerPath": "https://ecloud.10086.cn",
        "casServicePreFix": "https://order.cmecloud.cn:31056",
        "casIgnore": ["/health", "/version"],
        "logPath": "logs",
        "sessionConfig": {
            "cookie": {
                "secure": true,
                "sameSite": "none"
            },
            "proxy": "force"
        },
        "proxyConfig": [
             {
                "name": "drs-tcm-api-version",
                "context": "/version",
                "needToken": false,
                "needUserName": false,
                "options": {
                    "target": "http://10.213.174.210:30103",
                    "secure": false
                }
             },
             {
                "name": "drs-tm-api-health",
                "context": "/health",
                "needToken": false,
                "needUserName": false,
                "options": {
                    "target": "http://10.213.174.210:30103",
                    "secure": false
                }
             }
        ]
    }
