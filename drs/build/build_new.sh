#!/usr/bin/env bash

WORK_DIR=$(pwd)

DRS_HOME=$( cd "$( dirname "$0"  )/.." && pwd  )
BUILD_PROFILE=
VERSION=
BUILD_COMPONENTS="tcm,tm,agent,console,tapdata_agent"
PRODUCT="drs"

usage() {
    echo "Usage:"
    echo "  build.sh [-p profile] [-v version] [-P product] [-c components]"
    echo ""
    echo "Description:"
    echo "    profile: the runtime environment. Can be one of the following: dev, prod, test, uat; default dev."
    echo "    version: build version, default is the tag name of current git branch."
    echo "    product: build the product, drs or dfs, default drs."
    echo "    components: build on demand: tcm, tm, agent, console; use commas to separate multiple component name, example -m tcm,tm; default for build all."
    echo ""
    echo "Example:"
    echo "    Build DFS for dev environment:          ./build.sh -P dfs"
    echo "    Specify version:                        ./build.sh -v 0.0.20 -P drs"
    echo "    Specify environment:                    ./build.sh -v 0.0.20 -P drs -p test"
    echo "    Only build TM:                          ./build.sh -c tm"
    echo "    Build TM and Console:                   ./build.sh -c tm,console -p test -P dfs -v 0.0.20"
    echo ""
    exit 0
}
while getopts 'p:v:P:c:h' OPT; do
    case $OPT in
        p) BUILD_PROFILE=$(echo "$OPTARG" | tr "[A-Z]" "[a-z]");;
        v) VERSION="$OPTARG";;
        P) PRODUCT=$(echo "$OPTARG" | tr "[A-Z]" "[a-z]");;
        c) BUILD_COMPONENTS=$(echo "$OPTARG" | tr "[A-Z]" "[a-z]");;
        h) usage;;
        ?) usage;;
    esac
done

TCM_HOME="$DRS_HOME/tcm"
DRS_TCM_WEB_HOME=$( cd "$DRS_HOME/../../drs-web/" && pwd || echo "$DRS_HOME/../../drs-web/")
DFS_TCM_WEB_HOME=$( cd "$DRS_HOME/../../dfs-web/" && pwd || echo "$DRS_HOME/../../dfs-web/")
DAAS_HOME=$( cd "$DRS_HOME/../../daas/" && pwd || echo "$DRS_HOME/../../daas/")
TM_HOME="$DAAS_HOME/backend"
TM_WEB_HOME="$DAAS_HOME/frontend-el"
FLOW_ENGINE_HOME="$DAAS_HOME/connector"
TAPDATA_AGENT_HOME=$( cd "$DRS_HOME/../../tapdata_agent/" && pwd || echo "$DRS_HOME/../../tapdata_agent/")

DIST_DIR="$DRS_HOME/dist"
BUILD_PROFILE="${BUILD_PROFILE:=dev}"
BUILD_HOME="$DRS_HOME/build"
PROFILE_HOME="$BUILD_HOME/profiles/$BUILD_PROFILE"

GIT_MAIN_VERSION=$(cd "$DRS_HOME" && git describe)
GIT_COMMIT_VERSION=$(cd "$DRS_HOME" && git describe --long HEAD)
VERSION="${VERSION:=${GIT_MAIN_VERSION}}"
PRODUCT="${PRODUCT:=drs}"

RELEASE_NAME="$PRODUCT-$VERSION"
RELEASE_DIR="$DIST_DIR/$RELEASE_NAME"
RELEASE_BIN_DIR="$RELEASE_DIR/bin"
RELEASE_CONF_DIR="$RELEASE_DIR/conf"
RELEASE_LIB_DIR="$RELEASE_DIR/lib"
RELEASE_WEB_DIR="$RELEASE_DIR/static"
RELEASE_IMAGE_DIR="$RELEASE_DIR/image"

cat <<_END_
Worker directory:              $WORK_DIR
DRS home directory:            $DRS_HOME
TCM home directory:            $TCM_HOME
DRS TCM web home directory:    $DRS_TCM_WEB_HOME
DFS TCM web home directory:    $DFS_TCM_WEB_HOME
DAAS home directory:           $DAAS_HOME
TM home directory:             $TM_HOME
TM web home directory:         $TM_WEB_HOME
DRS Agent home directory:      $FLOW_ENGINE_HOME
Tapdata Agent home directory:  $TAPDATA_AGENT_HOME

Build home directory:          $BUILD_HOME
Profile home directory:        $PROFILE_HOME

Build profile:                 $BUILD_PROFILE
Build version:                 $VERSION
Git commit version:            $GIT_COMMIT_VERSION
Build product:                 $PRODUCT
Build components:              $BUILD_COMPONENTS

Output directory:              $DIST_DIR
Package name:                  $RELEASE_NAME
Package directory:             $RELEASE_DIR

_END_

export VERSION
export GIT_COMMIT_VERSION="$GIT_COMMIT_VERSION"
export PRODUCT="$PRODUCT"
#COMPONENTS=($(echo "$BUILD_COMPONENTS" | tr -d ' ' | tr ',' ' '))
IFS=" " read -r -a COMPONENTS <<< "$(echo "$BUILD_COMPONENTS" | tr -d ' ' | tr ',' ' ')"

if [[ ! -d "$DAAS_HOME" ]]; then
  echo "DAAS directory not exists: $DAAS_HOME"
  exit 1
fi

function clean() {
  echo "Clean original compile files"
  cd "$DRS_HOME" || exit 1
  mvn clean
}

function replaceAll() {
  if [ "$(uname)" == "Darwin" ];then
    sed -i '' "s/$1/$2/g" "$3"
  elif [[ "$(uname)" == "Linux" ]] || [[ "$(uname)" =~ "MINGW64_NT" ]]; then
    sed -i "s/$1/$2/g" "$3"
    sed -i "s/$1/$2/g" "$3"
  else
    echo "WARNING!!: The replaceAll method is not implemented on the $(uname) platform."
  fi
}

if [[ "$1" == "clean" ]]; then
	clean
	exit 0
fi

if [[ "$BUILD_PROFILE" != "prod" && "$BUILD_PROFILE" != "dev" && "$BUILD_PROFILE" != "test"  && "$BUILD_PROFILE" != "preprod" && "$BUILD_PROFILE" != "uat" ]]; then
	echo "profile can be dev、test、uat or prod or preprod."
	exit 1
fi

if [[ "$PRODUCT" != "drs" && "$PRODUCT" != "dfs" ]]; then
  echo "Product can be drs, dfs."
  exit 1
fi

if [ ! -d "$DIST_DIR" ]; then
  mkdir "$DIST_DIR"
fi

#if [ -d "$RELEASE_DIR" ]; then
#  echo "Remove exists directory $RELEASE_DIR"
#  rm -rf "$RELEASE_DIR"
#  echo ""
#fi
if [ ! -d "$RELEASE_BIN_DIR" ]; then
  mkdir -p "$RELEASE_BIN_DIR"
fi
if [ ! -d "$RELEASE_CONF_DIR" ]; then
  mkdir -p "$RELEASE_CONF_DIR"
fi
if [ ! -d "$RELEASE_LIB_DIR" ]; then
  mkdir -p "$RELEASE_LIB_DIR"
fi
if [ ! -d "$RELEASE_WEB_DIR" ]; then
  mkdir -p "$RELEASE_WEB_DIR"
fi

function existsCmd() {
  CMD_PATH=$(command -v "$1")
  return $?
}

function checkEnv() {
  if [ -d $JAVA_HOME ]; then
    echo "JAVA_HOME: $JAVA_HOME"
  else
    echo "Please set the correct JAVA_HOME."
    exit 1
  fi
}

function updateSourceCode() {
  echo "Update source code $DAAS_HOME"
  cd "$DAAS_HOME" || exit 1
  git pull
  echo ""

  echo "Update source code $DRS_HOME"
  cd "$DRS_HOME" || exit 1
  git pull
  echo ""
}

function build_tcm() {
  # Build tcm

  cd "$DRS_HOME/" || exit 1

  echo "Update Tapdata cloud manager Versions..."
  mvn versions:set -DnewVersion="$VERSION" # -DgenerateBackupPoms=false

  MODULES=("tcm" "tm-client")
  for MODULE in "${MODULES[@]}"
  do
    cd "$DRS_HOME/$MODULE" || exit 1
    mvn versions:set -DnewVersion="$VERSION"
  done

  cd "$DRS_HOME/" || exit 1
  mvn -Dmaven.test.skip=true package "-P$BUILD_PROFILE" || exit 1

  mvn versions:revert

  mv "$TCM_HOME/target/logback.xml" \
    "$RELEASE_CONF_DIR/"
  mv "$TCM_HOME/target/application-$PRODUCT.yml" \
    "$RELEASE_CONF_DIR/application.yml"

  cp "$TCM_HOME/target/tcm-$VERSION.jar" "$RELEASE_LIB_DIR/"
}

function build_tcm_web() {

  if [[ $PRODUCT == "drs" ]]; then
      echo "Build drs tcm web"
      if [[ ! -d "$DRS_TCM_WEB_HOME" ]]; then
        echo "DFS web directory not exists: $DRS_TCM_WEB_HOME"
        exit 1
      fi
      cd "$DRS_TCM_WEB_HOME" || exit 1

#      if [[ $test_profile == "test" ]]; then
#        test_profile="testing"
#      fi
      echo "npm run build -- --dest $RELEASE_WEB_DIR/ || exit 1"
      npm i
      npm run build -- --dest "$RELEASE_WEB_DIR/" || exit 1

      replaceAll "-version-" "$GIT_COMMIT_VERSION" "$RELEASE_WEB_DIR/index.html"
      replaceAll "-version-" "$GIT_COMMIT_VERSION" "$RELEASE_WEB_DIR/purchase.html"
  else
      echo "Build dfs tcm web"
      if [[ ! -d "$DFS_TCM_WEB_HOME" ]]; then
        echo "DFS web directory not exists: $DFS_TCM_WEB_HOME"
        exit 1
      fi
      cd "$DFS_TCM_WEB_HOME" || exit 1

      DFS_WEB_GIT_VERSION=$(cd "$DFS_TCM_WEB_HOME" && git describe)

#      test_profile=$BUILD_PROFILE

#      if [[ $test_profile == "test" ]]; then
#        test_profile="testing"
#      fi
      echo "npm run build -- --dest $RELEASE_WEB_DIR/ || exit 1"
      npm i 
      npm run submodule
      npm run build -- --dest "$RELEASE_WEB_DIR/" || exit 1

      replaceAll "<\/head>" "<!--Build version:$VERSION    DFS web git version: $DFS_WEB_GIT_VERSION--><\/head>" "$RELEASE_WEB_DIR/index.html"

  fi



}

function build_tm() {
    echo "Build tm"
    cd "$TM_HOME" || exit 1

    INIT_SCRIPT="$RELEASE_LIB_DIR/tm/init"
    mkdir -p "$RELEASE_LIB_DIR/tm/client"
    mkdir -p "$INIT_SCRIPT"
    npm i 
    cp -r "README.md" "common" "package.json" "scripts" "server" "node_modules" "$RELEASE_LIB_DIR/tm"
    cp -r "$TM_HOME/run_init.js" "$DAAS_HOME/init" "$DAAS_HOME/build/profile" "$INIT_SCRIPT/"
    replaceAll "..\/init\/" ".\/init\/" "$INIT_SCRIPT/run_init.js"
    replaceAll "..\/build\/profile\/" "profile\/" "$INIT_SCRIPT/run_init.js"
    replaceAll "..\/build_profile.js" ".\/build_profile.js" "$INIT_SCRIPT/run_init.js"
}

function build_tm_web() {

    echo "Build tm web"
    cd "$TM_WEB_HOME" || exit 1
    INDEX_PAGE="$TM_WEB_HOME/public/index.html"

    replaceAll "-version-" "$GIT_COMMIT_VERSION" "$INDEX_PAGE"
    if [[ $PRODUCT == "drs" ]]; then
      replaceAll "favicon-32x32.png" "favicon-drs.ico" "$INDEX_PAGE"
      replaceAll "favicon-16x16.png" "favicon-drs.ico" "$INDEX_PAGE"
      replaceAll "favicon.ico" "favicon-drs.ico" "$INDEX_PAGE"
      replaceAll "loading.svg" "loading-drs.gif" "$TM_WEB_HOME/src/main.js"
    fi
    npm cache clean -f
    rm -rf package-lock.json
    npm i 
    npm run submodule
    npm run build -- --dest "$RELEASE_WEB_DIR/tm" || exit 1
    #cp -r "$TM_WEB_HOME/dist" "$RELEASE_WEB_DIR/tm"

    git checkout "$INDEX_PAGE" "$TM_WEB_HOME/src/main.js"

}

function build_agent() {

    echo "Build agent"
    cd "$FLOW_ENGINE_HOME" || exit 1

    export DAAS_GIT_VERSION=$(cd "$FLOW_ENGINE_HOME" && git describe)

    mvn clean
    mvn -Dmaven.test.skip=true package -P not_encrypt || exit 1

    cp "$FLOW_ENGINE_HOME/connector-manager.jar" "$RELEASE_LIB_DIR/"
    cp "$FLOW_ENGINE_HOME/connector-manager/src/main/resources/log4j2.yml" "$RELEASE_CONF_DIR"
}

function build_tapdata_agent() {
  echo "Build Tapdata Agent"
  cd "$TAPDATA_AGENT_HOME" || exit 1
  npm i 
  "$TAPDATA_AGENT_HOME/node_modules/.bin/pkg" -t node12-linux-x64,node12-win-x64 -o "$RELEASE_BIN_DIR/tapdata-agent" "./tapdata.js"
}

function build_component() {
  for COMPONENT in "${COMPONENTS[@]}" ; do
    echo "Build component $COMPONENT"
    case $COMPONENT in
      "tcm")
        build_tcm &
      ;;
      "tm")
        build_tm &
      ;;
      "agent")
        build_agent &
      ;;
      "console")
        build_tcm_web &
        # build_tm_web &
      ;;
      "tapdata_agent")
        build_tapdata_agent &
      ;;
    esac
  done
  wait
}

function add_config() {
    echo "Add config"
    cd "$RELEASE_CONF_DIR" || exit 1

    for COMPONENT in "${COMPONENTS[@]}"
    do
      # k8s yaml
      if [[ -e "$PROFILE_HOME/$PRODUCT-$COMPONENT.yaml" ]]; then
        CONFIG_FILE="$PROFILE_HOME/$PRODUCT-$COMPONENT.yaml"
        if [ -f "$CONFIG_FILE" ]; then
          cp "$CONFIG_FILE" "$RELEASE_CONF_DIR"
          replaceAll "-version-" "$VERSION" "$RELEASE_CONF_DIR/$PRODUCT-$COMPONENT.yaml"
        fi
        if [ "$PRODUCT" == "drs" ] && [ "$COMPONENT" == "console" ]; then
          CONFIG_FILE="$PROFILE_HOME/$PRODUCT-order.yaml"
          if [ -f "$CONFIG_FILE" ]; then
            cp "$CONFIG_FILE" "$RELEASE_CONF_DIR"
            replaceAll "-version-" "$VERSION" "$RELEASE_CONF_DIR/$PRODUCT-order.yaml"
          fi
        fi
      fi

      if [[ "$PRODUCT" == "drs" ]]; then
        for POOL_ID in $(ls -l "$PROFILE_HOME" | awk '/^d.*CIDC.*/ {print $NF}')
        do
          if [[ -d "$PROFILE_HOME/$POOL_ID" ]]; then
            if [[ ! -d "$RELEASE_CONF_DIR/$POOL_ID" ]]; then
              mkdir "$RELEASE_CONF_DIR/$POOL_ID"
            fi
            CONFIG_FILE="$PROFILE_HOME/$POOL_ID/$PRODUCT-$COMPONENT.yaml"
            if [ -f "$CONFIG_FILE" ]; then
              cp "$CONFIG_FILE" "$RELEASE_CONF_DIR/$POOL_ID"
              replaceAll "-version-" "$VERSION" "$RELEASE_CONF_DIR/$POOL_ID/$PRODUCT-$COMPONENT.yaml"
            fi
            if [[ "$COMPONENT" == "console" ]]; then
              CONFIG_FILE="$PROFILE_HOME/$POOL_ID/$PRODUCT-order.yaml"
              cp "$CONFIG_FILE" "$RELEASE_CONF_DIR/$POOL_ID"
              replaceAll "-version-" "$VERSION" "$RELEASE_CONF_DIR/$POOL_ID/$PRODUCT-order.yaml"
            fi
            if [[ "$COMPONENT" == "tcm" ]]; then
              if [[ -f "$PROFILE_HOME/$POOL_ID/application.yml" ]]; then
                cp "$PROFILE_HOME/$POOL_ID/application.yml" "$RELEASE_CONF_DIR/$POOL_ID/"
              fi
              if [[ -f "$PROFILE_HOME/$POOL_ID/logback.xml" ]]; then
                cp "$PROFILE_HOME/$POOL_ID/logback.xml" "$RELEASE_CONF_DIR/$POOL_ID/"
              fi
            fi
            cp "$PROFILE_HOME/$POOL_ID/*-config-map.yaml" "$RELEASE_CONF_DIR/$POOL_ID/"
          fi
        done
      fi

      # Dockerfile
      #DOCKER_FILE="$BUILD_HOME/Dockerfile.$PRODUCT.$COMPONENT"
      #if [[ -e "$DOCKER_FILE" ]]; then
      #  cp "$DOCKER_FILE" "$RELEASE_CONF_DIR/Dockerfile.$PRODUCT.$COMPONENT"
      #  replaceAll "-version-" "$VERSION" "$RELEASE_CONF_DIR/Dockerfile.$PRODUCT.$COMPONENT"
      #fi
    done

    # self-agent Dockerfile
    #if [[ "$PRODUCT" == "dfs" ]]; then
    #  if echo "${COMPONENTS[@]}" | grep -w "agent" &>/dev/null; then
    #    if echo "${COMPONENTS[@]}" | grep -w "tapdata_agent" &>/dev/null; then
    #      cp "$BUILD_HOME/Dockerfile.$PRODUCT.agent.self-build" "$RELEASE_CONF_DIR"
    #      replaceAll "-version-" "$VERSION" "$RELEASE_CONF_DIR/Dockerfile.$PRODUCT.agent.self-build"
    #    fi
    #  fi
    #fi

    # upgrade shell
    cp "$BUILD_HOME/upgrade.sh" "$RELEASE_BIN_DIR/upgrade.sh"
}

function build_image() {

  cd "$RELEASE_DIR" || exit 1
  if [[ ! -d "$RELEASE_IMAGE_DIR" ]]; then
    mkdir "$RELEASE_IMAGE_DIR"
  fi

  IMAGE_VERSION="$VERSION"
  for COMPONENT in "${COMPONENTS[@]}"
  do

    DOCKER_FILE="$BUILD_HOME/Dockerfile.$PRODUCT.$COMPONENT"
    if [[ -e "$DOCKER_FILE" ]]; then

      IMAGE="tapdata/$PRODUCT-$COMPONENT:$IMAGE_VERSION"
      IMAGE_FILENAME="$PRODUCT-$COMPONENT-image-$IMAGE_VERSION.tar"

      echo ""
      echo "Build $IMAGE docker image"
      echo ""

      cp "$DOCKER_FILE" "./Dockerfile"
      replaceAll "-version-" "$VERSION" "./Dockerfile"

      docker build -t "$IMAGE" . || exit 1
      docker save -o "$IMAGE_FILENAME" "$IMAGE"
      gzip "$IMAGE_FILENAME"
      mv "$IMAGE_FILENAME.gz" "$RELEASE_IMAGE_DIR/"

      if [[ "$PRODUCT" == "drs" ]]; then
        if [[ $COMPONENT = "console" ]]; then
          echo ""
          echo "Build order docker image"
          echo ""

          ORDER_IMAGE="tapdata/$PRODUCT-order:$IMAGE_VERSION"

          echo "Tagged image $ORDER_IMAGE"

          docker tag "$IMAGE" "$ORDER_IMAGE"
          docker save -o "$PRODUCT-order-image-$IMAGE_VERSION.tar" "$ORDER_IMAGE"
          gzip "$PRODUCT-order-image-$IMAGE_VERSION.tar"
          mv "$PRODUCT-order-image-$IMAGE_VERSION.tar.gz" "$RELEASE_IMAGE_DIR/"
          docker rmi "$ORDER_IMAGE"
        fi
      fi

      docker rmi "$IMAGE"
      rm "Dockerfile"
    else
      echo "Skip build $COMPONENT image, not found Dockerfile: $DOCKER_FILE"
    fi
  done

  if [[ "$PRODUCT" == "dfs" ]]; then
    echo "${COMPONENTS[@]}" | grep -w "agent" > /dev/null;
    if echo "${COMPONENTS[@]}" | grep -w "agent" &>/dev/null; then
      if [[ -f "$RELEASE_BIN_DIR/tapdata-agent-linux" ]]; then
        # build docker image for self-build
        echo ""
        echo "Build docker image for DFS self-build flow-engine"
        echo ""

        cp "$BUILD_HOME/Dockerfile.$PRODUCT.agent.self-build" "./Dockerfile"
        replaceAll "-version-" "$VERSION" "./Dockerfile"

        IMAGE="tapdata/$PRODUCT-flow-engine:$IMAGE_VERSION"
        docker build -t "$IMAGE" . || exit 1
        docker save -o "$PRODUCT-flow-engine-$IMAGE_VERSION.tar" "$IMAGE"
        gzip "$PRODUCT-flow-engine-$IMAGE_VERSION.tar"
        mv "$PRODUCT-flow-engine-$IMAGE_VERSION.tar.gz" "$RELEASE_IMAGE_DIR/"
        docker rmi "$IMAGE"
        rm "Dockerfile"

        echo ""
        echo "Build release package for self-build flow engine."
        echo ""

        UPLOAD_DIR="$RELEASE_IMAGE_DIR/$RELEASE_NAME"
        mkdir "$UPLOAD_DIR"
        mv "$RELEASE_BIN_DIR/tapdata-agent-win.exe" "$UPLOAD_DIR/tapdata.exe" || exit 1
        mv "$RELEASE_BIN_DIR/tapdata-agent-linux" "$UPLOAD_DIR/tapdata"  || exit 1
        cp "$RELEASE_LIB_DIR/connector-manager.jar" "$UPLOAD_DIR/tapdata-agent"  || exit 1
        cp "$FLOW_ENGINE_HOME/connector-manager/src/main/resources/log4j2.yml" "$UPLOAD_DIR/" || exit 1
        chmod +x "$UPLOAD_DIR/tapdata"
        chmod +x "$UPLOAD_DIR/tapdata.exe"
        echo "U2FsdGVkX1/MEMW+D0y6GRykxjVgkUfjDMi7tDFXan5e4eU4Iu4dDA/iKZZWFLWgrj3u0yTBYEMCrRaz8h58SNGOwHIAypaoU03KpZhwMsKomtzUPqw6msOL3U7dXkAqjryAOqY2QSv5M3A=" > "$UPLOAD_DIR/.version"

      fi
    fi
  fi

}

function build_package() {
  cd "$DIST_DIR" || exit

  chmod 644 "$BUILD_HOME/package_server.pem"

  # build docker image
  if [[ -d "$RELEASE_IMAGE_DIR" ]]; then

    rm -rf "$RELEASE_LIB_DIR" "$RELEASE_WEB_DIR"

    FLOW_ENGINE_PACKAGE="$RELEASE_IMAGE_DIR/$RELEASE_NAME"
    if [[ -d "$FLOW_ENGINE_PACKAGE" ]]; then
      echo "Upload DFS flow-engine package to server."
      scp -i "$BUILD_HOME/package_server.pem" -r "$FLOW_ENGINE_PACKAGE" root@192.168.1.189:/home/<USER>/flow-engine/
      rm -rf "$FLOW_ENGINE_PACKAGE"
    fi

  fi

  tar -zcf "$RELEASE_NAME.tar.gz" "$RELEASE_NAME"
  scp -i "$BUILD_HOME/package_server.pem" "$RELEASE_NAME.tar.gz" root@192.168.1.189:/home/<USER>/
  rm -rf "$RELEASE_NAME"
}

function upgrade_drs_dev() {
  #DRS_SERVER="10.254.9.8"
  DRS_SERVER="10.144.91.30"
  scp -r "$RELEASE_DIR" "root@$DRS_SERVER:/apps/svr/drs/"

  ssh "root@$DRS_SERVER" -C "cd /apps/svr/drs && /apps/svr/drs/upgrade.sh $RELEASE_NAME $BUILD_COMPONENTS"
}
function upgrade_drs_test() {
  scp -P 10122 -r "$RELEASE_DIR" apps@127.0.0.1:/apps/svr/drs/

  ssh  -p 10122 apps@127.0.0.1 -C "cd /apps/svr/drs && /apps/svr/drs/upgrade.sh $RELEASE_NAME $BUILD_COMPONENTS"
}

function upgrade_dfs_dev(){
  chmod 600 "$BUILD_HOME/profiles/dev/id_rsa"
  scp -i "$BUILD_HOME/profiles/dev/id_rsa" -r "$DIST_DIR/$RELEASE_NAME.tar.gz" root@192.168.1.182:/data/dfs/
  ssh root@192.168.1.182 -i "$BUILD_HOME/profiles/dev/id_rsa"  -C "cd /data/dfs && /data/dfs/upgrade.sh -f $RELEASE_NAME.tar.gz -c $BUILD_COMPONENTS"
}

function upgrade_dfs_test(){
  chmod 600 "$BUILD_HOME/profiles/test/id_rsa"
  scp -i "$BUILD_HOME/profiles/test/id_rsa" -r "$DIST_DIR/$RELEASE_NAME.tar.gz" root@192.168.1.189:/home/<USER>/
  ssh root@192.168.1.189  -i "$BUILD_HOME/profiles/test/id_rsa" -C "cd /home/<USER>/home/<USER>/upgrade.sh -f $RELEASE_NAME.tar.gz -c $BUILD_COMPONENTS"
}

function upgrade_dfs_prod(){
  chmod 600 "$BUILD_HOME/profiles/prod/id_rsa"
  scp -i "$BUILD_HOME/profiles/prod/id_rsa" -r "$RELEASE_DIR" <EMAIL>:/data/svr/dfs/
  ssh <EMAIL> -i "$BUILD_HOME/profiles/prod/id_rsa" -C "cd /data/svr/dfs && /data/svr/dfs/upgrade.sh $RELEASE_NAME $BUILD_COMPONENTS"
}

function upload_release_package_to_cdn() {

  UPLOAD_DIR="$RELEASE_DIR/$RELEASE_NAME"
  mkdir "$UPLOAD_DIR"
  mv "$RELEASE_BIN_DIR/tapdata-agent-win.exe" "$UPLOAD_DIR/tapdata.exe" || exit 1
  mv "$RELEASE_BIN_DIR/tapdata-agent-linux" "$UPLOAD_DIR/tapdata"  || exit 1
  cp "$RELEASE_LIB_DIR/connector-manager.jar" "$UPLOAD_DIR/tapdata-agent"  || exit 1
  cp "$FLOW_ENGINE_HOME/connector-manager/src/main/resources/log4j2.yml" "$UPLOAD_DIR/" || exit 1
  chmod +x "$UPLOAD_DIR/tapdata"
  chmod +x "$UPLOAD_DIR/tapdata.exe"
  echo "U2FsdGVkX1/MEMW+D0y6GRykxjVgkUfjDMi7tDFXan5e4eU4Iu4dDA/iKZZWFLWgrj3u0yTBYEMCrRaz8h58SNGOwHIAypaoU03KpZhwMsKomtzUPqw6msOL3U7dXkAqjryAOqY2QSv5M3A=" > "$UPLOAD_DIR/.version"
  echo "Upload files"
  ls -l "$UPLOAD_DIR"
  chmod 600 "$PROFILE_HOME/public.pem"
  scp -i "$PROFILE_HOME/public.pem" -r "$UPLOAD_DIR"  root@129.204.11.227:/cdn.tapdata.net/package/feagent
  rm -rf "$UPLOAD_DIR"
}

function notice_msg() {

  type say
  if [[ $? -eq 0 ]]; then
    say "$1"
  fi

}

function upgrade() {
  if [[ $PRODUCT = "drs" ]]; then

    #rm -rf "$RELEASE_WEB_DIR" "$RELEASE_LIB_DIR"
    case "$BUILD_PROFILE" in
      "dev")
        upgrade_drs_dev
        #notice_msg "更新研发环境完成"
        ;;
      "test")
        upgrade_drs_test
        #notice_msg "更新测试环境完成"
        ;;
    esac
  elif [[ $PRODUCT = "dfs" ]]; then

#    if [[ "$BUILD_COMPONENTS" =~ "tapdata_agent" ]];  then
#      echo "Upload agent"
#      upload_release_package_to_cdn
#    else
#      echo "Not upload agent $BUILD_COMPONENTS"
#    fi

    #rm -rf "$RELEASE_WEB_DIR" "$RELEASE_LIB_DIR"
    case "$BUILD_PROFILE" in
      "dev")
        upgrade_dfs_dev
        notice_msg "更新研发环境完成"
        ;;
      "test")
        upgrade_dfs_test
        notice_msg "更新测试环境完成"
        ;;
      "prod")
        #upgrade_dfs_prod
        #notice_msg "更新生产环境完成"
        ;;
    esac
  fi
}

# clean
# updateSourceCode

build_component

#notice_msg "编译 $PRODUCT 完成"

add_config

build_image

#notice_msg "构建 $PRODUCT 完成"

build_package

upgrade

notice_msg "更新 $PRODUCT 完成"
