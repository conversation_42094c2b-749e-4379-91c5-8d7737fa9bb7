/**
 * <AUTHOR>
 * @date 2020/12/20 下午1:01
 * @description
 */

var userDataBulk = db.userData.initializeUnorderedBulkOp();
var current = 0;
var total = db.usertable.count();
db.usertable.find().batchSize(5000).forEach(function(rec){

	current++;
	userDataBulk.insert(rec);

	if (current % 5000 === 0) {
		userDataBulk.execute();
		userDataBulk = db.userData.initializeUnorderedBulkOp();
		print(current);
	} else if (total-1 === current) {
		userDataBulk.execute();
		print(current);
	}

});



res = benchRun( {
	host:'*************:30843',
	username:'test',
	password:'lG1208123$',
	db:'admin',
	ops : [ {
		ns : "test3.test" ,
		op : "insert" ,
		doc : {
			sym:{"#RAND_STRING" : [500] },
			ts : { "#RAND_INT" : [ 1474558306, 1484558306,1 ] },
			price: { "#RAND_INT" : [ 10, 500, 1] }
		}
	} ] ,
	parallel : 1 ,
	seconds : 60} );


db.drs_agent.aggregate([
	{ $match: { status: {$nin: ['Deleted', 'Deleting', 'Approving', 'WaitingDelete']} } },
	{ $group: {_id: {region: '$region', zone: '$zone', status: '$status'}, agentCount: { $sum: 1 }}},
	{
		$lookup: {
			from: 'drs_k8s_config',
			let: { region: '$_id.region', zone: '$_id.zone' },
			pipeline: [{
					$match: { $expr: { $and: [
						{ $eq: [ "$region", "$$region" ] },
						{ $eq: [ "$zone", "$$zone" ] },
					] } }
				}, {
					$project: { _id: 0, regionName: 1, zoneName: 1, region: 1, zone: 1 }
				}
			],
			as: 'regionMeta'
		}
	},
	{ $unwind: '$regionMeta' }
])
