SystemError=System error: {0}
IllegalState=Illegal State: {0}
IllegalArgument=Illegal Argument: {0}
javax.validation.constraints.ValueOfEnum.message=Must be any of enum {enums}
javax.validation.constraints.CustomerTypeSubset.message=Must be any {anyOf}
javax.validation.constraints.In.message=Must be in {values}
NotLogin=User is not login.

CreateAgentFailed.AccessFailed=Cannot access TM server.
SubscribeFailed.OrderLimit=A maximum of {0} instances can be ordered in the same resource pool
QueryUserInfoFromAuthingFailed=Failed to call Authing query user information reason: {0}
Resources.NotEnough=Resources not enough
NotFoundAgent=Not found Agent:{0}
AgentUploading=Agent:{0} Uploading
UploadLogError=Agent:{0} upload error
NotDownLoadLog=Not Download Agent:{0} Log
NotFoundLog=Not found Agent:{0} log
DownloadLogFail=Download Log fail:{0}
DeleteLogFail=Delete Log fail:{0}
QueryUserIdentitiesFromAuthingFailed=Failed to call Authing query user identities reason: {0}

CreateAgentFailed.LicenseRequired=License code ID(licenseId) cannot be empty.
CreateAgentFailed.SubscriptionRequired=Subscription ID(subscriptionId) cannot be empty.
CreateAgentFailed.OrderRequired=Missing order information.
CreateAgentFailed.ChargeProviderRequired=Charge provider cannot be empty.

LicenseAssign.NotActivated=License({0}) status({1}) mast be activated.
LicenseAssign.Invalid=Invalid license({0}) to assign
LicenseAssign.NotFound=Not found license({0}) to assign
LicenseAssign.Existed=License {0} has been assigned to agent {1}

NotFoundProduct=Product:{0} Not found 
NotFoundPrice=Price:{0} Not found 
GetPaymentError=Get payment error:{0}
NotFoundSubscribe = Order:{0} Not found 
RefundPaymentError=Refund payment error:{0}
SubscriptionCancelFail = Subscription cancel Fail :{0}
AgentAassigned = Agent Aassigned 
CreateManyUnpaid= Create too many unpaid orders
AgentHasRunningTask = Agent:Has {0} running task
RejectRefund.Status=Subscribe status is {0}, cannot execute this operation.
RejectRefund.UsageType=Refunds are not available for usage-based subscriptions

User.BindPhone.Exists=The mobile phone has been bound and cannot be bound repeatedly
User.BindPhone.Failed=Captcha is wrong, please re-enter
User.ResetPassword.Failed=The verification code is incorrect. Please re-enter it.
User.ResetPassword.Authing.Failed=Password reset failed. Please try again later.
User.ResetPassword.Invalid=The password must be at least 6 characters long.
User.UpdateEmail.Failed=The verification code for the original email is incorrect. Please re-enter it.
User.UpdateEmail.Failed.NewEmailCode=The verification code for the new email is incorrect. Please re-enter it.
User.UpdateEmail.Authing.Failed=Failed to update the email address. Please try again later.
User.UpdateEmail.TM.Failed=Failed to update the email address. Please try again later.
Captcha.NotFound=Captcha is wrong, please re-enter
Captcha.Used=The captcha has been used, please get it again
Captcha.Expired=The captcha has expired, please get it again
Captcha.Mismatch=Captcha is wrong, please re-enter
User.BindPhone.Authing.Failed=Failed to bind mobile phone number, please try again later
User.BindPhone.TM.Failed=Failed to bind mobile phone number, please try again later

mdb.instance.assigned.underquota=Can't acquire DB instance, please retry or contact the administrator
mdb.instance.assigned.failed=DB instance assign failed: {0}
mdb.instance.assigned.expired=DB instance expired, assign time is {0}
mdb.instance.assigned.status.unsupported=Unsupported status: {0}