[{"providerId": "tapdata-public", "productId": "tapdata-real-time-data-pipelines.endpoints.tapdata-public.cloud.goog", "plans": [{"planName": "large", "periods": [{"planId": "large", "period": "month"}, {"planId": "large-P1Y", "period": "year"}], "metrics": [{"id": "tapdata-real-time-data-pipelines.endpoints.tapdata-public.cloud.goog/large_egress_traffic", "type": "egress_traffic", "description": "Large 实例流量指标"}]}, {"planName": "xlarge", "periods": [{"planId": "xlarge", "period": "month"}, {"planId": "xlarge-P1Y", "period": "year"}], "metrics": [{"id": "tapdata-real-time-data-pipelines.endpoints.tapdata-public.cloud.goog/xlarge_egress_traffic", "type": "egress_traffic", "description": "XLarge 实例流量指标"}]}, {"planName": "2xlarge", "periods": [{"planId": "xlarge2", "period": "month"}, {"planId": "xlarge2-P1Y", "period": "year"}], "metrics": [{"id": "tapdata-real-time-data-pipelines.endpoints.tapdata-public.cloud.goog/xlarge2_egress_traffic", "type": "egress_traffic", "description": "2XLarge 实例流量指标"}]}, {"planName": "3xlarge", "periods": [{"planId": "xlarge3", "period": "month"}, {"planId": "xlarge3-P1Y", "period": "year"}], "metrics": [{"id": "tapdata-real-time-data-pipelines.endpoints.tapdata-public.cloud.goog/xlarge3_egress_traffic", "type": "egress_traffic", "description": "3XLarge 实例流量指标"}]}, {"planName": "4xlarge", "periods": [{"planId": "xlarge4", "period": "month"}, {"planId": "xlarge4-P1Y", "period": "year"}], "metrics": [{"id": "tapdata-real-time-data-pipelines.endpoints.tapdata-public.cloud.goog/xlarge4_egress_traffic", "type": "egress_traffic", "description": "4XLarge 实例流量指标"}]}, {"planName": "8xlarge", "periods": [{"planId": "xlarge8", "period": "month"}, {"planId": "xlarge8-P1Y", "period": "year"}], "metrics": [{"id": "tapdata-real-time-data-pipelines.endpoints.tapdata-public.cloud.goog/xlarge8_egress_traffic", "type": "egress_traffic", "description": "8XLarge 实例流量指标"}]}]}]