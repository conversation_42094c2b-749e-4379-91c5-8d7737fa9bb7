package com.tapdata.manager.mdb.dto;

import com.tapdata.atlas.client.cluster.AutoScaling;
import com.tapdata.atlas.client.cluster.BiConnector;
import com.tapdata.atlas.client.cluster.ProviderSetting;
import com.tapdata.atlas.client.cluster.ReplicationSpec;
import com.tapdata.atlas.client.user.Label;
import lombok.Data;

import java.util.List;

@Data
public class Cluster {
    private String id;
    private AutoScaling autoScaling;
    private Boolean backupEnabled;
    private BiConnector biConnector;

    /**
     * Enum: "REPLICASET" "SHARDED" "GEOSHARDED"
     */
    private String clusterType;
    private Double diskSizeGB;
    /**
     * Cloud service provider
     * Enum: "NONE" "AWS" "AZURE" "GCP"
     */
    private String encryptionAtRestProvider;
    private ProviderSetting providerSettings;

    private List<Label> labels;
    private String mongoDBMajorVersion;
    private String mongoDBVersion;
    private String name;
    private Integer numShards;
    private Boolean paused;
    private Boolean pitEnabled;
    private Boolean providerBackupEnabled;

    private List<ReplicationSpec> replicationSpecs;

    private String rootCertType;
    private Boolean terminationProtectionEnabled;
    /**
     * Enum: "LTS" "CONTINUOUS"
     */
    private String versionReleaseSystem;

    private com.tapdata.atlas.client.cluster.ConnectionString connectionStringsForAtlas;
    private String createDate;
    private String srvAddress;
    /**
     * Enum: "IDLE" "CREATING" "UPDATING" "DELETING" "DELETED" "REPAIRING"
     */
    private String stateName;

    private String  uri;
    private String dbName;
    private ConnectionString connectionStrings;

}
