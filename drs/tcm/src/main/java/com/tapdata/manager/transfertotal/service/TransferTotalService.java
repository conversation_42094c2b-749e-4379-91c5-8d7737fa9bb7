package com.tapdata.manager.transfertotal.service;

import com.tapdata.manager.base.exception.BizException;
import com.tapdata.manager.base.service.BaseService;
import com.tapdata.manager.config.security.UserDetail;
import com.tapdata.manager.transfertotal.constant.TransferType;
import com.tapdata.manager.transfertotal.dto.TransferTotalDto;
import com.tapdata.manager.transfertotal.entity.TransferTotal;
import com.tapdata.manager.transfertotal.repository.TransferTotalRepository;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.NumberFormat;

@Service
@Slf4j
public class TransferTotalService extends BaseService<TransferTotalDto, TransferTotal, ObjectId, TransferTotalRepository> {

   private static  final long INCREASE_PER_SECOND = 10000;

    public TransferTotalService(@NonNull TransferTotalRepository repository) {
        super(repository, TransferTotalDto.class, TransferTotal.class);
    }

    @Override
    protected void beforeSave(TransferTotalDto dto, UserDetail userDetail) {

    }


    public void updateTransferTotal() {
        Query query = Query.query(Criteria.where("type").is(TransferType.ALL.getValue()));
        repository.upsetData(query, Update.update("updateDate", System.currentTimeMillis()).inc("total", 5 * INCREASE_PER_SECOND));
    }


    public TransferTotalDto findData(String type) {
        if (StringUtils.isEmpty(type)) {
            throw new BizException("IllegalArgument", "type");

        }
        if (!StringUtils.equalsAny(type, TransferType.ALL.getValue(), TransferType.COPY.getValue(), TransferType.DEVELOP.getValue())) {
            throw new BizException("IllegalArgument", type);

        }
        TransferTotalDto transferTotalDto = new TransferTotalDto();
        TransferTotal transferTotal = repository.findOne(Query.query(Criteria.where("type").is(type))).orElse(null);
        transferTotalDto.setTotal(transferTotal.getTotal());
        transferTotalDto.setType(type);
        NumberFormat formatter = new DecimalFormat("###,###");
        transferTotalDto.setFormatTotal(formatter.format(transferTotal.getTotal() / 10000) + "");
        return transferTotalDto;
    }



}