package com.tapdata.manager.subscribe.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * create at 2025/1/6 12:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceSummaryResult {
    private String month;
    private String subscribeId;
    private TotalSummary total;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TotalSummary {
        private Long totalFreeNetworkTraffic;
        private Long freeNetworkTrafficAvailable;
        private Long totalTransmit;
        private Long totalReceived;
        private Double networkCost;
        private Long engine;
        private Long engineCost;
    }
}
