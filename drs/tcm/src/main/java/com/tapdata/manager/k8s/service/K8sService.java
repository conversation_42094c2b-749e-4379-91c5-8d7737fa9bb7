package com.tapdata.manager.k8s.service;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.gridfs.model.GridFSFile;
import com.tapdata.manager.agent.SpecType;
import com.tapdata.manager.agent.dto.AgentAlterDto;
import com.tapdata.manager.agent.dto.AgentDto;
import com.tapdata.manager.agent.dto.Spec;
import com.tapdata.manager.agent.dto.TmInfo;
import com.tapdata.manager.base.exception.BizException;
import com.tapdata.manager.common.utils.JsonUtil;
import com.tapdata.manager.file.service.FileService;
import com.tapdata.manager.k8s.authentication.CCIKubeconfigAuthentication;
import com.tapdata.manager.k8s.ReplicasPatch;
import com.tapdata.manager.k8s_config.entity.K8sConfig;
import com.tapdata.manager.k8s_config.service.K8sConfigService;
import com.tapdata.manager.product_release.service.ProductReleaseService;
import com.tapdata.manager.utils.MapWrapper;
import io.kubernetes.client.Exec;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.custom.V1Patch;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.Pair;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.*;
import io.kubernetes.client.util.ClientBuilder;
import io.kubernetes.client.util.KubeConfig;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2021/1/27 下午9:28
 * @description
 */
@Component
@Slf4j
public class K8sService{

    @Autowired
    private K8sConfigService k8sConfigService;

    @Value("${spring.profiles.include}")
    private String profile;

    private static final String INGRESS_BANDWIDTH = "kubernetes.io/ingress-bandwidth";
    private static final String EGRESS_BANDWIDTH = "kubernetes.io/egress-bandwidth";
    @Lazy
    @Autowired
    private ProductReleaseService productReleaseService;

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier("memoryCache")
    private CacheManager cacheManager;

    /**
     * 创建 k8s api client
     * @param k8sConfig
     * @return
     * @throws IOException
     */
    //@Cacheable(cacheNames = "k8sApiClient", cacheManager = "memoryCache", key = "#k8sConfig.provider + '-' + #k8sConfig.region + '-' + #k8sConfig.zone", condition = "#result != null")
    public ApiClient getApiClient(K8sConfig k8sConfig) throws IOException {
        if (k8sConfig != null) {

            String cacheKey = String.format("%s-%s-%s", k8sConfig.getProvider(), k8sConfig.getRegion(), k8sConfig.getZone());
            Cache cache = cacheManager.getCache("k8sApiClient");
            ApiClient cachedApiClient = cache != null ? cache.get(cacheKey, ApiClient.class) : null;
            if (cachedApiClient != null) {
                log.debug("Return ApiClient from memory cache");
                return cachedApiClient;
            }

            KubeConfig config = KubeConfig.loadKubeConfig(new StringReader(k8sConfig.getYaml()));
            Map<String, Object> execMap = getExecMapFromKubeConfig(config);
            String command = execMap != null && execMap.get("command") != null ?
                    execMap.get("command").toString() : null;
            ApiClient apiClient = null;
            if (!StringUtils.isEmpty(command)) {
                log.info("command:{}",command);
                // 先检查文件
                File commandFile = queryAndUpload(execMap);
                config.setFile(commandFile);

                log.debug("Using plugin to auth {}", execMap);

                config.setFile(commandFile);

                if (command.contains("cci-iam-authenticator")) {
                    apiClient = CCIKubeconfigAuthentication.buildClient(config);
                } else {
                    apiClient = ClientBuilder.kubeconfig(config)
                            .setAuthentication(new CCIKubeconfigAuthentication(config))
                            .build();
                }
            } else {
                apiClient = ClientBuilder.kubeconfig(config).build();
            }

            if (apiClient != null) {
                if (cache != null) {
                    cache.put(cacheKey, apiClient);
                }
                return apiClient;
            } else {
                throw new BizException("Create k8s ApiClient failed, ApiClient is null.");
            }
        } else {
            throw new BizException("Can't create k8s ApiClient, the k8sConfig cannot be null.");
        }
    }

    public Map<String, Object> getExecMapFromKubeConfig(KubeConfig config) {
        try {
            Map<String, Object> maUser = (Map<String, Object>) config.getUsers().get(0);
            Map<String, Object> map = findObject(config.getUsers(), maUser.get("name").toString());
            Map<String, Object> currentUser = (Map<String, Object>) map.get("user");
            Map<String, Object>exec =(Map<String, Object>) currentUser.get("exec");
            if (exec != null) {
                return exec;
            }
            return null;
        } catch (Exception e) {
            return null;
        }

    }

    public synchronized File queryAndUpload(Map<String, Object> execMap) {
        try {
            File workDir = new File(System.getProperty("user.dir") + File.separator + "tmpl");
            if (!workDir.exists()) {
                workDir.mkdir();
            }
            File commandFile = new File(workDir, execMap.get("command").toString());
            if (!commandFile.exists()) {
                storeFileToDisk(commandFile);
                if (!commandFile.setExecutable(true)) {
                    log.error("Set Executable fail");
                }
            }

            if (execMap.containsKey("env") && execMap.get("env") instanceof List) {
                List<Map<String, String>> envs = (List<Map<String, String>>) execMap.get("env");
                envs.forEach(env -> {
                    if ("gridFs".equals(env.get("type")) && StringUtils.hasText(env.get("filename") )) {
                        File file = new File(workDir, env.get("filename"));
                        if (!file.exists()) {
                            storeFileToDisk(file);
                        }
                        String value = env.get("value");
                        if (value != null) {
                            value = value.replaceAll("\\{FILE_STORE_PATH\\}", file.getAbsolutePath());
                        } else {
                            value = file.getAbsolutePath();
                        }
                        env.put("value", value);
                    }
                });
            }

            return commandFile;
        } catch (Exception e) {
            throw new BizException("Query and Upload is error", e);
        }
    }

    /**
     * 根据文件名查找GridFS中的文件，并保存到指定目录
     * @param outputFile
     */
    private void storeFileToDisk(File outputFile) {
        if (outputFile.exists()) {
            return;
        }
        String filename = outputFile.toPath().getFileName().toString();
        Query query = Query.query(Criteria.where("filename").is(filename));
        GridFSFile gridFSFile = fileService.findOne(query);
        if (gridFSFile == null) {
            log.error("Not found K8s authenticator plugin file {} in mongo grid fs", filename);
            return;
        }
        try (FileOutputStream outputStream = new FileOutputStream(outputFile)) {
            fileService.readFileById(gridFSFile, outputStream);

            log.info("Store file to {} from gridFs", outputFile.getAbsolutePath());
        } catch (Exception e) {
            log.error("Store file {} to disk {} failed", filename, outputFile.getAbsolutePath(), e);
        }
    }

    public static Map<String, Object> findObject(ArrayList<Object> list, String name) {
        if (list == null) {
            return null;
        }
        for (Object obj : list) {
            Map<String, Object> map = (Map<String, Object>) obj;
            if (name.equals(map.get("name"))) {
                return map;
            }
        }
        return null;
    }

    /**
     * 移除 k8s api client 缓存
     * @param region
     * @param zone
     */
    @CacheEvict(cacheNames = "k8sApiClient", cacheManager = "memoryCache", key = "#provider + '-' + #region + '-' + #zone")
    public void removeApiClient(String provider, String region, String zone) {
        log.info("{} - {} - {} k8s ApiClient is removed.", provider, region, zone);
    }

    /**
     * 判断K8s集群上是否存在某个资源
     * @param apiClient
     * @param resourcePath
     * @return
     * @throws IOException
     * @throws ApiException
     */
    public boolean exists(ApiClient apiClient, String resourcePath) throws IOException, ApiException {
        Call call = buildCall(apiClient, resourcePath, "GET");
        Response response = call.execute();
        return response.isSuccessful();
    }

    /**
     * 删除指定path的资源
     * @param apiClient
     * @return
     */
    private boolean deleteResource(ApiClient apiClient, String resourcePath) throws ApiException, IOException {
        Call call = buildCall(apiClient, resourcePath, "DELETE");
        Response response = call.execute();
        return response.isSuccessful();
    }

    /**
     * 构建 k8s 请求
     * @param apiClient
     * @param url
     * @param method
     * @return
     * @throws ApiException
     */
    private Call buildCall(ApiClient apiClient, String url, String method) throws ApiException {
        List<Pair> localVarQueryParams = new ArrayList<>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<>();
        Map<String, String> localVarHeaderParams = new HashMap<>();
        Map<String, String> localVarCookieParams = new HashMap<>();
        Map<String, Object> localVarFormParams = new HashMap<>();
        String[] localVarAccepts = new String[]{"application/json", "application/yaml", "application/vnd.kubernetes.protobuf"};
        String localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }
        String[] localVarContentTypes = new String[0];
        String localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);
        localVarHeaderParams.put("Content-Type", localVarContentType);
        String[] localVarAuthNames = new String[]{"BearerToken"};
        return apiClient.buildCall(url, method, localVarQueryParams, localVarCollectionQueryParams, null, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, null);
    }

    /**
     * 在指定资源池创建命名空间
     * @param ns
     * @param coreV1Api
     * @param k8sConfig
     */
    public boolean createNamespace(String ns, CoreV1Api coreV1Api, K8sConfig k8sConfig) throws IOException, ApiException {
        V1ObjectMetaBuilder metadataBuilder = new V1ObjectMetaBuilder()
                .withName(ns)
                //.addToLabels("kubesphere.io/workspace", profile)
                .addToLabels("kubesphere.io/namespace", ns)
                .addToAnnotations("kubesphere.io/creator", "TCM(Leon)");

        V1NamespaceBuilder nsBuilder = new V1NamespaceBuilder();
        nsBuilder.withMetadata(metadataBuilder.build())
                .withKind("Namespace")
                .withApiVersion("v1");

        V1Namespace namespace = nsBuilder.build();
        if (log.isDebugEnabled()){
            log.debug("\n" + Yaml.dump(namespace));
        }

        V1Namespace result;
        /*boolean existsNs = existsResource(coreV1Api, "Namespaces", ns);
        if (existsNs) {
            result = coreV1Api.replaceNamespace(ns, namespace, null, null, null);*/
        //} else {
            result = coreV1Api.createNamespace(namespace, null, null, null);
        //}

        if (result != null && result.getStatus() != null) {
            return "Active".equalsIgnoreCase(result.getStatus().getPhase());
        }
        return false;
    }

    /**
     * 创建 Agent 实例
     * @param agentDto
     * @param tmInfo
     * @return
     */
    public void createAgent(AgentDto agentDto, TmInfo tmInfo) throws BizException {

        if (agentDto == null || tmInfo == null) {
            throw new BizException("Create Agent failed, parameters cannot be empty.");
        }

        K8sConfig k8sConfig = k8sConfigService.getK8sConfig(agentDto.getProvider(), agentDto.getRegion(), agentDto.getZone());

        ApiClient apiClient;
        try {
            apiClient = getApiClient(k8sConfig);
        } catch (IOException e) {
            log.error("Create Agent failed, Because k8s ApiClient cannot be created.");
            throw new BizException("Create Agent failed, Because k8s ApiClient cannot be created.", e);
        }

        CoreV1Api coreV1Api = new CoreV1Api(apiClient);
        String ns = getNamespace(agentDto, k8sConfig);

        try {
            boolean existsNamespaces = exists(apiClient, String.format("/api/v1/namespaces/%s", ns));
            if (!existsNamespaces) {
                createNamespace(ns, coreV1Api, k8sConfig);
            }
        } catch (Exception e) {
            String msg = String.format("Create Agent failed, because namespace %s cannot created", ns);
            log.error(msg, e);
            throw new BizException(msg, e);
        }

        AppsV1Api appsV1Api = new AppsV1Api(apiClient);
        try {
            V1Deployment deployment = buildDeployment(agentDto, tmInfo, ns, k8sConfig);

            if (deployment.getMetadata() == null) {
                throw new BizException("Build deployment failed, metadata must be not empty.");
            }

            String deploymentName = deployment.getMetadata().getName();
            String deploymentPath = String.format("/apis/apps/v1/namespaces/%s/deployments/%s", ns, deploymentName);
            boolean existsDeployment = exists(apiClient, deploymentPath);
            V1Deployment result;
            if (existsDeployment) {
                result = appsV1Api.replaceNamespacedDeployment(deploymentName, ns, deployment, null, null, null);
            } else {
                result = appsV1Api.createNamespacedDeployment(ns, deployment, null, null, null);
            }
            //result.getStatus()
            if (log.isDebugEnabled()) {
                log.debug("<\n" + Yaml.dump(result));
            }
        } catch (IOException | ApiException e) {
            log.error("Create Agent failed, because create deployment failed", e);
        }

    }

    public boolean existsAgent(AgentDto agentDto) {

        K8sConfig k8sConfig = k8sConfigService.getK8sConfig(agentDto.getProvider(), agentDto.getRegion(), agentDto.getZone());

        ApiClient apiClient;
        try {
            apiClient = getApiClient(k8sConfig);
        } catch (IOException e) {
            log.error("Check Agent exists failed, Because k8s ApiClient cannot be created.");
            throw new BizException("Check Agent exists failed, Because k8s ApiClient cannot be created.", e);
        }

        String deploymentName = buildDeploymentName(agentDto);
        String ns = getNamespace(agentDto, k8sConfig);
        String deploymentPath = String.format("/apis/apps/v1/namespaces/%s/deployments/%s", ns, deploymentName);

        try {
            return exists(apiClient, deploymentPath);
        } catch (Exception e) {
            log.error("Check agent exists failed", e);
            throw new BizException("Check agent exists failed", e);
        }
    }

    private String buildDeploymentName(AgentDto agentDto) {
        return  "agent-" + agentDto.getId().toHexString();
    }

    private String getNamespace(AgentDto agentDto, K8sConfig k8sConfig) {
        return k8sConfig != null && k8sConfig.getDefaultAgentNamespace() != null ? k8sConfig.getDefaultAgentNamespace() : "drs-agent";
    }

    /**
     * 创建 deployment
     * @param agentDto
     * @param tmInfo
     * @param ns
     */
    private V1Deployment buildDeployment(AgentDto agentDto, TmInfo tmInfo, String ns, K8sConfig k8sConfig) throws IOException {

        Spec agentSpec = agentDto.getSpec();
        if (agentSpec.getVersion() == null)
            agentSpec.setVersion(k8sConfig.getDefaultAgentVersion());

        String name = buildDeploymentName(agentDto);
        String packageName = StringUtils.isEmpty(k8sConfig.getDefaultAgentPackage()) ? "tapdata/drs-agent" : k8sConfig.getDefaultAgentPackage();
        String tmServerUrl = tmInfo.getTmServerUrl() + "/api/";
        SpecType specType = SpecType.get(agentSpec.getSpecType());
        int speedLimit = 0; // 0 表示默认不限速
        if (k8sConfig.getTmServerUrl() != null) {
            tmServerUrl = k8sConfig.getTmServerUrl();
        }
        log.info("tmServerUrl------:{}",tmServerUrl);


        if (!StringUtils.isEmpty(k8sConfig.getRegistry())) {
            packageName = k8sConfig.getRegistry() + "/" + packageName;
        }

        double limitCpu = agentSpec.getCpu() * 1000 * k8sConfig.getScale();
        double limitMem = agentSpec.getMemory() * k8sConfig.getScaleMem();
        Map<String, Quantity> limits = new LinkedHashMap<>();
        limits.put("cpu", Quantity.fromString(limitCpu + "m"));
        limits.put("memory", Quantity.fromString(limitMem + "Gi"));
        limits.put("ephemeral-storage", Quantity.fromString("10Gi"));
        Map<String, Quantity> requests = /*"dfs".equalsIgnoreCase(profile) ? new LinkedHashMap<String, Quantity>(){{
            put("cpu", Quantity.fromString("100m"));
            put("memory", Quantity.fromString("100Mi"));
        }} : */limits;

        Map<String, String> nodeSelector = new HashMap<>();
        if (agentDto.getNodeSelector() != null && agentDto.getNodeSelector().size() > 0) {
            nodeSelector.putAll(agentDto.getNodeSelector());
        }
        if (k8sConfig.getDefaultNodeSelector() != null && k8sConfig.getDefaultNodeSelector().size() > 0) {
            nodeSelector.putAll(k8sConfig.getDefaultNodeSelector());
        }

        Map<String, String> podAnnotationsMap = new HashMap<>();
        if (k8sConfig.getEnableBandwidth() != null && k8sConfig.getEnableBandwidth()) {
            /*String bandwidth = specType.getBandwidth();
            if (com.tapdata.manager.common.utils.StringUtils.isNotBlank(bandwidth)){
                podAnnotationsMap.put(INGRESS_BANDWIDTH,bandwidth);
                podAnnotationsMap.put(EGRESS_BANDWIDTH,bandwidth);
            }*/
            speedLimit = specType.getSpeedLimit();
        }
        JSONObject jsonObject =  productReleaseService.parserToken(tmInfo.getToken());
        // String appType =StringUtils.isEmpty(profile)||profile.contains("dfs")?"dfs":"";
        V1DeploymentBuilder deploymentBuilder = new V1DeploymentBuilder()
                .withNewMetadata()
                .addToAnnotations("kubesphere.io/creator", "TCM(Leon)")
                .addToAnnotations("kubesphere.io/mail", "<EMAIL>")
                .addToAnnotations("kubesphere.io/team", "TCM Team")
                .addToAnnotations("kubesphere.io/owner", agentDto.getCreateUser())
                .addToAnnotations("kubesphere.io/ownerId", agentDto.getCreateBy())
                .addToLabels("app", name)
                .addToLabels("version", agentSpec.getVersion())
                .withName(name)
                .withNamespace(ns)
                .endMetadata()
                .withNewSpec()
                .withNewSelector().addToMatchLabels("app", name).endSelector()
                .withReplicas(1)

                .withNewTemplate()
                .withNewMetadata()
                .addToAnnotations(podAnnotationsMap)
                .addToLabels("app", name)
                .addToLabels("version", agentSpec.getVersion())
                .endMetadata()
                .withNewSpec()
                .withImagePullSecrets().addAllToImagePullSecrets(Optional.ofNullable(
                                k8sConfig.getImagePullSecrets()).orElse(Collections.emptyList()).stream()
                        .map(k -> new V1LocalObjectReference().name(k)).collect(Collectors.toList()))
                .addNewContainer()

                .withName(name)
                .withImage(String.format("%s:%s", packageName, agentSpec.getVersion()))
                .withCommand("/bin/bash")
                .withArgs("/usr/local/bin/docker-entrypoint.sh","/opt/agent/tapdata","start","backend", "--token",tmInfo.getToken())
                .addToEnv(
                        new V1EnvVar().name("direction").value(agentSpec.getDirection()),
                        new V1EnvVar().name("OUTPUT_LOG_PATH").value("/opt/agent/logs"),
                        new V1EnvVar().name("isCloud").value("true"),
                        new V1EnvVar().name("jobTags").value(agentDto.getRegion() + "," + agentDto.getZone()),
                        new V1EnvVar().name("cloud_accessCode").value(tmInfo.getAccessCode()),
                        new V1EnvVar().name("backend_url").value(tmServerUrl),
                        new V1EnvVar().name("version").value(agentSpec.getVersion()),
                        new V1EnvVar().name("app_type").value("DFS"),
                        new V1EnvVar().name("process_id").value(tmInfo.getAgentId()),
                        new V1EnvVar().name("CONSUMER_SPEED_TOTAL").value(String.valueOf(speedLimit)),
                        new V1EnvVar().name("accessKey").value(jsonObject.getString("accessKey")),
                        new V1EnvVar().name("secretKey").value(jsonObject.getString("secretKey")),
                        new V1EnvVar().name("ignoreIpForNTC").value(String.join(",", k8sConfig.getIgnoreIpForNTC())),
                        new V1EnvVar().name("singletonLock").value(agentDto.getId().toHexString()))
                .withNewResources().addToLimits(limits).addToRequests(requests)
                .endResources()
                .editOrNewSecurityContext()
                .editOrNewCapabilities()
                .addToAdd("NET_RAW", "SETPCAP")
                .addToDrop()
                .endCapabilities()
                .endSecurityContext()

                .endContainer()
                .addToNodeSelector(nodeSelector)
                .endSpec()
                .endTemplate()

                .endSpec();

        V1Deployment deployment = deploymentBuilder.build();
        if (log.isDebugEnabled()) {
            log.debug(">\n" + Yaml.dump(deployment));
        }
        return deployment;
    }

    /**
     * 释放实例
     * @param agentDto
     * @param tmInfo
     */
    public void releaseAgent(AgentDto agentDto, TmInfo tmInfo) {
        if (agentDto == null || tmInfo == null) {
            throw new BizException("Release Agent failed, parameters cannot be empty.");
        }

        K8sConfig k8sConfig = k8sConfigService.getK8sConfig(agentDto.getProvider(), agentDto.getRegion(), agentDto.getZone());

        ApiClient apiClient;
        try {
            apiClient = getApiClient(k8sConfig);
        } catch (IOException e) {
            log.error("Release Agent failed, Because k8s ApiClient cannot be created.");
            throw new BizException("Create Agent failed, Because k8s ApiClient cannot be created.", e);
        }
        String name = buildDeploymentName(agentDto);
        String ns = getNamespace(agentDto, k8sConfig);
        try {
            AppsV1Api appsV1Api = new AppsV1Api(apiClient);
            V1Status result = appsV1Api.deleteNamespacedDeployment(name, ns, null, null, null, null, null, null);
            if (log.isDebugEnabled()) {
                log.debug("<\n" + Yaml.dump(result));
            }
        } catch (Exception e) {
            if ("Not Found".equalsIgnoreCase(e.getMessage())) {
                log.warn("Agent no longer exists.");
            } else {
                log.error("Release Agent failed", e);
                throw new BizException("Release Agent failed", e);
            }
        }
    }

    /**
     * 检查 Agent 规格是否和变更规格一致
     * @param agentDto
     * @param agentAlterDto
     * @param tmInfo
     * @return
     */
    public boolean checkAgentSpec(AgentDto agentDto, AgentAlterDto agentAlterDto, TmInfo tmInfo) {

        if (agentDto == null || agentAlterDto == null || tmInfo == null) {
            throw new BizException("Check Agent alter status failed, parameters cannot be empty.");
        }

        K8sConfig k8sConfig = k8sConfigService.getK8sConfig(agentDto.getProvider(), agentDto.getRegion(), agentDto.getZone());

        ApiClient apiClient;
        try {
            apiClient = getApiClient(k8sConfig);
        } catch (IOException e) {
            log.error("Check Agent alter status failed, Because k8s ApiClient cannot be created.");
            throw new BizException("Check Agent alter status faile, Because k8s ApiClient cannot be created.", e);
        }
        String ns = getNamespace(agentDto, k8sConfig);
        try {
            String deploymentName = buildDeploymentName(agentDto);
            String deploymentPath = String.format("/apis/apps/v1/namespaces/%s/deployments/%s", ns, deploymentName);

            Call call = buildCall(apiClient, deploymentPath, "GET");
            Response response = call.execute();

            if (response.isSuccessful() && response.body() != null) {

                Object deployment = JsonUtil.parseJson(response.body().string(), Map.class);

                MapWrapper deploymentWrapper = new MapWrapper(deployment);
                MapWrapper status = deploymentWrapper.get("status");
                String replicas = status.get("replicas").getAsString();
                String readyReplicas = status.get("readyReplicas").getAsString();
                log.info("Ready replicas/Replicas: {}/{}", readyReplicas, replicas);
                // spec.template.spec.containers[0].resources.limits.[cpu/memory]
                MapWrapper limits = deploymentWrapper.get("spec").get("template").get("spec")
                        .get("containers").getAsList().get(0).get("resources").get("limits");

                String cpu = limits.get("cpu").getAsString();
                String memory = limits.get("memory").getAsString();

                if (cpu == null) {
                    log.error("Cannot get cpu spec from k8s deployment");
                    return false;
                }
                if (memory == null) {
                    log.error("Cannot get memory spec from k8s deployment");
                    return false;
                }

                Quantity currentCpu = Quantity.fromString(cpu);
                Quantity currentMemory = Quantity.fromString(memory);

                Spec agentSpec = agentAlterDto.getSpec();
                Quantity alterCpu = Quantity.fromString((agentSpec.getCpu() * 1000 * k8sConfig.getScale()) + "m");
                Quantity alterMemory = Quantity.fromString(agentSpec.getMemory() * k8sConfig.getScaleMem() + "Gi");

                if (ObjectUtils.compare(currentCpu.getNumber(), alterCpu.getNumber()) != 0 ||
                    ObjectUtils.compare(currentMemory.getNumber(), alterMemory.getNumber()) != 0) {
                    log.info("Agent current spec is {} {}, and alter spec is {} {}", currentCpu, currentMemory, alterCpu, alterMemory);
                    return false;
                }

                String version = deploymentWrapper.get("metadata").get("labels").get("version").getAsString();
                if (!agentSpec.getVersion().equals(version)) {
                    log.warn("Agent current spec is {}, and alter spec is {}", version, agentSpec.getVersion());
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            if ("Not Found".equalsIgnoreCase(e.getMessage())) {
                log.warn("Agent no longer exists.");
            } else {
                log.error("Release Agent failed", e);
                throw new BizException("Release Agent failed", e);
            }
        }

        return false;
    }

    /**
     * 停止 agent
     * @param agentDto
     */
    public void stopAgent(AgentDto agentDto) throws IOException, ApiException {
        K8sConfig k8sConfig = k8sConfigService.getK8sConfig(agentDto.getProvider(), agentDto.getRegion(), agentDto.getZone());
        String deploymentName = this.buildDeploymentName(agentDto);
        String ns = getNamespace(agentDto, k8sConfig);
        AppsV1Api appsV1Api = new AppsV1Api(this.getApiClient(k8sConfig));
        log.info("Stop Agent [{}]", agentDto.getId().toString());
        appsV1Api.patchNamespacedDeployment(deploymentName, ns, new V1Patch(ReplicasPatch.ZERO), null, null, null, null);
    }

    /**
     * 启动 agent
     * @param agentDto
     */
    public void startAgent(AgentDto agentDto) throws IOException, ApiException {
        K8sConfig k8sConfig = k8sConfigService.getK8sConfig(agentDto.getProvider(), agentDto.getRegion(), agentDto.getZone());
        String deploymentName = this.buildDeploymentName(agentDto);
        String ns = getNamespace(agentDto, k8sConfig);
        AppsV1Api appsV1Api = new AppsV1Api(this.getApiClient(k8sConfig));
        log.info("Start Agent [{}]", agentDto.getId().toString());
        appsV1Api.patchNamespacedDeployment(deploymentName, ns, new V1Patch(ReplicasPatch.ONE), null, null, null, null);
    }

    public void restartAgent(AgentDto agentDto) throws Throwable {
        final K8sConfig k8sConfig = k8sConfigService.getK8sConfig(agentDto.getProvider(), agentDto.getRegion(), agentDto.getZone());
        final String deploymentName = this.buildDeploymentName(agentDto);
        final String ns = getNamespace(agentDto, k8sConfig);
        AppsV1Api appsV1Api = new AppsV1Api(this.getApiClient(k8sConfig));
        log.info("prepare to restart Agent [{}]", agentDto.getId().toString());
        appsV1Api.patchNamespacedDeployment(deploymentName, ns, new V1Patch(ReplicasPatch.ZERO), null, null, null, null);
        Thread.sleep(1000L);
        appsV1Api.patchNamespacedDeployment(deploymentName, ns, new V1Patch(ReplicasPatch.ONE), null, null, null, null);
    }

    /**
     * 判断 agent 是否运行中
     * @param agentDto
     * @return
     */
    public boolean isRunning(AgentDto agentDto) {
        if (agentDto == null) {
            throw new BizException("Check Agent run status failed, parameters cannot be empty.");
        }

        K8sConfig k8sConfig = k8sConfigService.getK8sConfig(agentDto.getProvider(), agentDto.getRegion(), agentDto.getZone());

        ApiClient apiClient;
        try {
            apiClient = getApiClient(k8sConfig);
        } catch (IOException e) {
            log.error("Check Agent run status failed, Because k8s ApiClient cannot be created.");
            throw new BizException("Check Agent alter status faile, Because k8s ApiClient cannot be created.", e);
        }
        String ns = getNamespace(agentDto, k8sConfig);
        try {

            String deploymentName = buildDeploymentName(agentDto);
            String deploymentPath = String.format("/apis/apps/v1/namespaces/%s/deployments/%s", ns, deploymentName);

            Call call = buildCall(apiClient, deploymentPath, "GET");
            Response response = call.execute();

            if (response.isSuccessful() && response.body() != null) {

                Object deployment = JsonUtil.parseJson(response.body().string(), Map.class);

                MapWrapper deploymentWrapper = new MapWrapper(deployment);
                MapWrapper status = deploymentWrapper.get("status");
                String replicas = Optional.ofNullable(status.get("replicas").getAsString()).orElse("0");
                String readyReplicas = Optional.ofNullable(status.get("readyReplicas").getAsString()).orElse("0");
                log.info("Ready replicas/Replicas: {}/{}", readyReplicas, replicas);

                if (!"0".equals(readyReplicas) && readyReplicas.equals(replicas)) {
                    return true;
                }
            }
        } catch (Exception e) {
            if ("Not Found".equalsIgnoreCase(e.getMessage())) {
                log.warn("Agent no longer exists.");
            } else {
                log.error("Query Agent status failed", e);
                throw new BizException("Query Agent status failed", e);
            }
        }

        return false;
    }

    public Map<String,Float> getCapacityTotal(K8sConfig k8sConfig){
        Map<String,Float> result = new HashMap<>();
        Map<String, String> defaultNodeSelector = k8sConfig.getDefaultNodeSelector();
        String labelSelector = null;
        if (defaultNodeSelector != null){
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : defaultNodeSelector.entrySet()) {
                sb.append(",").append(entry.getKey()).append("=").append(entry.getValue());
            }
            if (sb.length() > 0){
                labelSelector = sb.substring(1);
            }
        }
        try {
            ApiClient apiClient = getApiClient(k8sConfig);
            CoreV1Api coreV1Api = new CoreV1Api(apiClient);
            V1NodeList v1NodeList = coreV1Api.listNode(null, null, null, null, labelSelector, null, null, null, null);
            float cpuTotal = 0f;
            float ramTotal = 0f;
            if (v1NodeList != null && CollectionUtils.isNotEmpty(v1NodeList.getItems())){
                for (V1Node item : v1NodeList.getItems()) {
                    if (item == null || item.getStatus() == null || item.getStatus().getCapacity() == null){
                        continue;
                    }
                    if (item.getSpec() != null && CollectionUtils.isNotEmpty(item.getSpec().getTaints())){
                        continue;
                    }
                    boolean isReady = false;
                    for (V1NodeCondition condition : Objects.requireNonNull(item.getStatus().getConditions())) {
                        if ("Ready".equalsIgnoreCase(condition.getType()) && "true".equalsIgnoreCase(condition.getStatus())){
                            isReady = true;
                            break;
                        }
                    }
                    if (!isReady){
                        continue;
                    }

                    Map<String, Quantity> capacity = item.getStatus().getCapacity();
                    Quantity cpu = capacity.get("cpu");
                    Quantity memory = capacity.get("memory");
                    cpuTotal += cpu.getNumber().longValue();
                    ramTotal += memory.getNumber().divide(new BigDecimal(1 << 10 << 10 << 10), 0, BigDecimal.ROUND_HALF_UP).floatValue();
                }
            }
            result.put("cpu", cpuTotal);
            result.put("ram", ramTotal);
        } catch (Exception e) {
            log.error("Get CapacityList failed, k8sConfig.id: {}, message: {}", k8sConfig.getId().toHexString(), e.getMessage());
        }

        return result;
    }

    public List<String> getAllNodeInternalIP(String provider, String region, String zone) {
        K8sConfig k8sConfig = k8sConfigService.getK8sConfig(provider, region, zone);

        ApiClient apiClient;
        try {
            apiClient = getApiClient(k8sConfig);
        } catch (IOException e) {
            log.error("Get k8s node internal ip failed, Because k8s ApiClient cannot be created.");
            throw new BizException("Get k8s node internal ip failed, Because k8s ApiClient cannot be created.", e);
        }
        CoreV1Api coreV1Api = new CoreV1Api(apiClient);
        try {
            V1NodeList nodes = coreV1Api.listNode(null, false, null, null, null, null, null, 50, false);
            return nodes.getItems().stream().map(node -> {
                if(node.getStatus() != null && node.getStatus().getAddresses() != null)
                    return node.getStatus().getAddresses()
                            .stream().filter( v1NodeAddress -> "InternalIP".equals(v1NodeAddress.getType())).map(V1NodeAddress::getAddress)
                            .findFirst().orElse(null);
                else
                    return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());

        } catch (ApiException e) {
            log.error("Get k8s node internal ip failed, Because call k8s api failed.");
            throw new BizException("Get k8s node internal ip failed, Because call k8s api failed.", e);
        }
    }

    public boolean deployNTC(AgentDto agentDto) {

        K8sConfig k8sConfig = k8sConfigService.getK8sConfig(agentDto.getProvider(), agentDto.getRegion(), agentDto.getZone());
        ApiClient apiClient;
        try {
            apiClient = getApiClient(k8sConfig);
        } catch (IOException e) {
            log.error("Deployment ntc failed, {}", e.getMessage(), e);
            throw new BizException("Upload file to pod failed, Because k8s ApiClient cannot be created.", e);
        }

        String deploymentName = this.buildDeploymentName(agentDto);
        String namespace = getNamespace(agentDto, k8sConfig);
        String podName = getPodNameByDeployment(namespace, deploymentName, apiClient);

        Exec exec = new Exec(apiClient);
        uploadFileToPodFromGridFS("ntc", namespace, podName, "/opt/ntc", exec);
        uploadFileToPodFromGridFS("ntc_start.sh", namespace, podName, "/opt/ntc_start.sh", exec);

        ByteArrayOutputStream output = new ByteArrayOutputStream();
        String[] command;
        if (k8sConfig.getIgnoreIpForNTC() != null && !k8sConfig.getIgnoreIpForNTC().isEmpty()) {
            command = new String[]{"/bin/sh", "/opt/ntc_start.sh", "-f", String.join(",", k8sConfig.getIgnoreIpForNTC())};
        } else {
            command = new String[]{"/bin/sh", "/opt/ntc_start.sh"};
        }
        int exitCode = execCommandInPod(namespace, podName, command, exec, output);
        if (exitCode == 0) {
            try {
                log.info("Startup ntc output: {}", output.toString("UTF-8"));
            } catch (UnsupportedEncodingException e) {
                log.error("Unsupported encoding for utf-8");
            }
        } else {
            log.error("Startup ntc failed, exit code is {}", exitCode);
            return false;
        }

        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {}

        output.reset();
        exitCode = execCommandInPod(namespace, podName, new String[]{"curl", "http://localhost:8010"}, exec, output);

        if (exitCode == 0 && output.size() > 0) {
            try {
                String _output = output.toString("UTF-8");
                Map<String, Object> data = JsonUtil.parseJson(_output, Map.class);
                if (data != null && data.containsKey("code")) {
                    return data.get("code").equals("ok");
                } else {
                    log.error("Ntc health response not ok: {}", _output);
                }
            } catch (Exception e) {
                log.error("Parse health response for ntc failed: {}", output, e);
            }
        } else {
            log.error("Ntc health check not response: {}", output);
        }

        return false;
    }

    public boolean checkNtcHealth(AgentDto agentDto) {
        K8sConfig k8sConfig = k8sConfigService.getK8sConfig(agentDto.getProvider(), agentDto.getRegion(), agentDto.getZone());
        ApiClient apiClient;
        try {
            apiClient = getApiClient(k8sConfig);
        } catch (IOException e) {
            log.error("Deployment ntc failed, {}", e.getMessage(), e);
            throw new BizException("Upload file to pod failed, Because k8s ApiClient cannot be created.", e);
        }

        String deploymentName = this.buildDeploymentName(agentDto);
        String namespace = getNamespace(agentDto, k8sConfig);
        String podName = getPodNameByDeployment(namespace, deploymentName, apiClient);
        Exec exec = new Exec(apiClient);

        ByteArrayOutputStream output = new ByteArrayOutputStream();
        int exitCode = execCommandInPod(namespace, podName, new String[]{"curl", "http://localhost:8010"}, exec, output);

        if (exitCode == 0 && output.size() > 0) {
            try {
                String _output = output.toString("UTF-8");
                agentDto.setMessage(_output);
                Map<String, Object> data = JsonUtil.parseJson(_output, Map.class);
                if (data != null && data.containsKey("code")) {
                    return data.get("code").equals("ok");
                } else {
                    log.error("Ntc health response not ok: {}", _output);
                }
            } catch (Exception e) {
                log.error("Parse health response for ntc failed: {}", output, e);
            }
        } else {
            log.error("Ntc health check not response: {}", output);
        }
        return false;
    }

    private int execCommandInPod(String namespace, String podName, String[] command, Exec exec, ByteArrayOutputStream byteArrayOutput) {

        try {
            Process process = exec.exec(namespace, podName, command, false, false);

            if (byteArrayOutput != null) {
                IOUtils.copy(process.getInputStream(), byteArrayOutput);
            }
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                log.info("Exec command in pod({}.{}) success: {}", namespace, podName, String.join(" ", command));
            } else {
                log.error("Exec command failed in pod({}.{}): {}", namespace, podName, String.join(" ", command));
            }

            process.destroy();
            return exitCode;
        } catch (ApiException | IOException | InterruptedException e) {
            log.error("Exec command in pod({}.{}) failed", namespace, podName, e);
            throw new BizException("ExecCommandFailed", e);
        }
    }

    private String getPodNameByDeployment(String namespace, String deploymentName, ApiClient apiClient) {

        CoreV1Api coreV1Api = new CoreV1Api(apiClient);
        String labelSelector = "app=" + deploymentName;
        try {
            V1PodList pods = coreV1Api.listNamespacedPod(namespace, null, null, null, null,
                    labelSelector, null, null, null, false);
            if (pods != null && pods.getItems() != null && !pods.getItems().isEmpty()) {
                return Objects.requireNonNull(pods.getItems().get(0).getMetadata()).getName();
            }
            return null;
        } catch (ApiException e) {
            log.error("Find pod name failed for deployments {}.{}", namespace, deploymentName, e);
            throw new BizException("FindPodNameFailed", e, "Find pod name failed");
        }
    }

    private boolean uploadFileToPodFromGridFS(String sourceName, String namespace, String podName, String targetPath, Exec exec) {
        InputStream inputStream;
        try {
            inputStream = fileService.getInputStreamByQuery(Query.query(Criteria.where("filename").is(sourceName)));
        } catch (IOException e) {
            log.error("Deploy ntc failed, read {} file failed in mongodb, {}", sourceName, e.getMessage(), e);
            throw new BizException("DeployNtcFailed", e, "Read " + sourceName + " file failed in mongodb.");
        }

        if (inputStream == null) {
            log.error("Not found {} in mongodb", sourceName);
            throw new BizException("NotFoundNtc", String.format("Not found %s in mongodb.", sourceName));
        }
        return uploadFileToPod(namespace, podName, inputStream, targetPath, exec);
    }

    /**
     * Upload file to pod
     * @param namespace namespace of pod
     * @param podName name of pod
     * @param exec k8s exec object.
     * @param fileInput upload file input stream
     * @param targetPath save target path in pod
     * @return exit code
     */
    private boolean uploadFileToPod(String namespace, String podName, InputStream fileInput, String targetPath, Exec exec) {
        String[] command = new String[]{"/bin/sh", "-c", "cat > " + targetPath};

        try {
            Process process = exec.exec(namespace, podName, command, true, false);
            OutputStream output = process.getOutputStream();

            int length = IOUtils.copy(fileInput, output);
            if (length > 0) {
                log.info("Upload file({}) to pod({}) success", length, targetPath);
            }
            output.flush();
            output.close();

            process.destroy();
            return true;
        } catch (IOException | ApiException e) {
            log.error("Upload file to pod failed: {}", e.getMessage(), e);
            throw new BizException("UploadFileToPodFailed", e, String.format("Upload file to pod failed, %s", e.getMessage()) );
        }
    }
}
