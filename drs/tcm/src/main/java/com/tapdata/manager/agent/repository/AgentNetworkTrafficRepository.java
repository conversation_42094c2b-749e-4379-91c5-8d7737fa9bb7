package com.tapdata.manager.agent.repository;

import com.tapdata.manager.agent.entity.AgentNetworkTraffic;
import com.tapdata.manager.base.reporitory.BaseRepository;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * create at 2024/5/28 15:39
 */
@Repository
public class AgentNetworkTrafficRepository extends BaseRepository<AgentNetworkTraffic, ObjectId> {
    public AgentNetworkTrafficRepository(MongoTemplate mongoOperations) {
        super(AgentNetworkTraffic.class, mongoOperations);
    }
}
