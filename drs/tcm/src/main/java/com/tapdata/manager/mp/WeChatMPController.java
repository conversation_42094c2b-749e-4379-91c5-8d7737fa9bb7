package com.tapdata.manager.mp;

import com.tapdata.manager.base.controller.BaseController;
import com.tapdata.manager.common.utils.StringUtils;
import com.tapdata.manager.mp.util.AesException;
import com.tapdata.manager.mp.util.WXBizMsgCrypt;
import com.tapdata.manager.mp.util.XMLParse;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * create at 2022/6/17 上午11:14
 */
@Controller
@Slf4j
@Profile("dfs")
public class WeChatMPController extends BaseController {

    @Value("${weChat.mp.token:}")
    private String token;
    @Value("${weChat.mp.appId:}")
    private String appId;
    @Value("${weChat.mp.appSecret:}")
    private String appSecret;
    @Value("${weChat.mp.encodingAesKey:}")
    private String encodingAesKey;

    @Value("${authing.weChatEventForwardUri:}")
    private String authingUri;
    private WXBizMsgCrypt wxcpt = null;

    private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(5, 20,
            60L, TimeUnit.SECONDS, new SynchronousQueue<Runnable>());

    private CloseableHttpClient httpClient = HttpClients.custom()
            .setConnectionTimeToLive(30L, TimeUnit.SECONDS)
            .useSystemProperties()
            .build();

    private WXBizMsgCrypt getCrypt() throws AesException {
        if (wxcpt != null)
            return wxcpt;

        if (StringUtils.isBlank(appId) || StringUtils.isBlank(encodingAesKey) || StringUtils.isBlank(token)) {
            throw new AesException(AesException.ValidateAppidError);
        }

        wxcpt = new WXBizMsgCrypt(token, encodingAesKey, appId);
        return wxcpt;

    }

    @RequestMapping(value = "/api/tcm/mp/event", method = {RequestMethod.GET, RequestMethod.HEAD})
    @ResponseBody
    public String mpVerify(
            @RequestParam(required = false) String signature,
            @RequestParam(required = false, name="echostr") String echoStr,
            @RequestParam(required = false) String timestamp,
            @RequestParam(required = false) String nonce, HttpServletRequest request) {
        log.info("WeChat server identity, query string is {}", request.getQueryString());

        try {

            getCrypt().verifyUrl(signature, timestamp, nonce, echoStr);

            return echoStr;
        } catch (AesException e) {
            log.error("Verify signature fail", e);
            return "verify signature fail";
        }
    }

    @RequestMapping(value = "/api/tcm/mp/event", method = {RequestMethod.POST})
    @ResponseBody
    public String eventHandler(@RequestBody(required = false) String originalBody,
                               @RequestParam(required = false) String signature,
                               @RequestParam(required = false, name="echostr") String echoStr,
                               @RequestParam(required = false) String timestamp,
                               @RequestParam(required = false) String nonce,
                               @RequestParam(required = false, name="encrypt_type") String encryptType,
                               @RequestParam(required = false, name="msg_signature") String msgSignature,
                               @RequestParam(required = false, name="openid") String openId, HttpServletRequest request) {

        try {
            getCrypt().verifyUrl(signature, timestamp, nonce, echoStr);
        } catch (AesException e) {
            log.error("Verify signature fail", e);
            return "verify signature fail";
        }

        String body = null;
        if (StringUtils.isNotBlank(encryptType) && StringUtils.isNotBlank(msgSignature)) {

            if ("aes".equalsIgnoreCase(encryptType)) {
                try {
                    body = getCrypt().decryptMsg(msgSignature, timestamp, nonce, originalBody);
                } catch (AesException e) {
                    log.error("Decrypt WeChat message fail, sign: {}, ts: {}, nonce: {}, body: {}",
                            msgSignature, timestamp, nonce, originalBody, e);
                    return "decrypt message fail";
                }
            } else {
                log.error("Decrypt WeChat message fail, not support encrypt for {}", encryptType);
                return "not support encrypt for " + encryptType;
            }
        } else {
            body = originalBody;
        }

        try {
            Document document = XMLParse.parseXML(body);

            Element rootEl = document.getDocumentElement();
            Function<String, String> getTextContent = (tagName) -> {
                NodeList el = rootEl.getElementsByTagName(tagName);
                if (el.getLength() > 0) {
                    return el.item(0).getTextContent();
                }
                return null;
            };

            /*String toUserName = getTextContent.apply("ToUserName");
            String fromUserName = getTextContent.apply("FromUserName");*/
            String msgType = getTextContent.apply("MsgType");
            String event = getTextContent.apply("Event");

            // MsgType=event and Event=subscribe,unsubscribe,SCAN
            if ("event".equals(msgType) && (
                    "subscribe".equalsIgnoreCase(event) ||
                    "unsubscribe".equalsIgnoreCase(event) ||
                    "SCAN".equalsIgnoreCase(event)
            )) {
                forwardAuthing(request.getQueryString(), originalBody);
            }
        } catch (AesException e) {
            log.error("Parse WeChat message fail", e);
        }

        return "success";
    }

    private void forwardAuthing(String queryString, String body) {

        threadPool.execute(() -> {
            try {

                String uri = authingUri + "?" + queryString;
                log.debug("Forward WeChat event to authing");
                log.debug("> POST {}", uri);

                HttpPost httpPost = new HttpPost(uri);
                httpPost.setEntity(new StringEntity(body, ContentType.create("application/xml", "UTF-8")));
                Stream.of(httpPost.getAllHeaders()).map(h -> "< " + h.getName() + ": " + h.getValue()).forEach(log::debug);
                log.debug("> {}", body);

                CloseableHttpResponse response = httpClient.execute(httpPost);
                String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.debug(response.getStatusLine().toString());
                Stream.of(response.getAllHeaders()).map(h -> "< " + h.getName() + ": " + h.getValue()).forEach(log::debug);
                log.debug(responseBody);
            } catch (IOException e) {
                log.error("Forward WeChat event to authing fail", e);
            }

        });

    }

}
