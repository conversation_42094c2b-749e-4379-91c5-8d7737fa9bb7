package com.tapdata.manager.interactive.service.dfs;

import cn.authing.sdk.java.client.ManagementClient;
import cn.authing.sdk.java.dto.*;
import cn.authing.sdk.java.model.ManagementClientOptions;
import com.google.common.reflect.TypeToken;
import com.tapdata.manager.base.exception.BizException;
import com.tapdata.manager.common.utils.JsonUtil;
import com.tapdata.manager.common.utils.StringUtils;
import com.tapdata.manager.user.dto.UpdateUserInfoReq;
import com.tapdata.manager.user.dto.UserInfoDto;
import com.tapdata.manager.user.entity.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2021/4/10 下午7:11
 * @description
 */
@Service
@Slf4j
@Profile("dfs")
public class AuthingService {

    @Value("${authing.userPoolId}")
    private String userPoolId;
    @Value("${authing.userPoolSecret}")
    private String userPoolSecret;
    @Value("${authing.userRoleNamespaceId:6062b5216946f73e1f93ebde}")
    private String userRoleNamespaceId;
    @Value("${authing.host:}")
    private String host;

    @Autowired
    private MongoTemplate mongoTemplate;

    private Pattern pattern = Pattern.compile("/^([0-9]|[a-z]){24}$/");


    private ManagementClient getInstance() {

        ManagementClientOptions clientOptions = new ManagementClientOptions();
        clientOptions.setAccessKeyId(userPoolId);
        clientOptions.setAccessKeySecret(userPoolSecret);

        if (StringUtils.isNotBlank(host)) {
            clientOptions.setHost(host);
        }
        return new ManagementClient(clientOptions);
    }


    public UserInfoDto getUserInfo(String userId){
        Query query = Query.query(Criteria.where("userInfo.id").is(userId));
        UserInfo userInfo = mongoTemplate.findOne(query, UserInfo.class);
        if (userInfo == null){
            userInfo = new UserInfo();
        }
        UserInfoDto userInfoDto = userInfo.getUserInfo();
        if (userInfoDto == null){
            userInfoDto = getUserInfoByAuthing(userId);
            userInfo.setUserInfo(userInfoDto);
            userInfo.setCreateAt(new Date());
            userInfo.setLastUpdAt(new Date());
            userInfo.setLastOpTime(new Date());
            //mongoTemplate.save(userInfo);
            mongoTemplate.upsert(query, Update.update("userInfo", userInfoDto)
                            .set("lastUpdAt", userInfo.getLastUpdAt())
                            .set("lastOpTime", userInfo.getLastOpTime())
                            .setOnInsert("createAt", userInfo.getCreateAt()), UserInfo.class);
        }else {
            if (System.currentTimeMillis() - userInfo.getLastUpdAt().getTime() > 1000 * 60 * 60){
                try {
                    UserInfoDto newUserInfoDto = getUserInfoByAuthing(userId);
                    if (userInfoDto.getCustomData() != null && newUserInfoDto.getCustomData() != null) {
                        Map<String, String> customData = userInfoDto.getCustomData();
                        newUserInfoDto.getCustomData().forEach((key, val) -> {
                            if (!org.apache.commons.lang3.StringUtils.isEmpty(key)) {
                                if (customData.containsKey(key)) {
                                    if (org.apache.commons.lang3.StringUtils.isBlank(val)) {
                                        // remove custom data for key
                                        customData.remove(key);
                                    } else {
                                        // update custom data for key
                                        customData.put(key, val);
                                    }
                                } else {
                                    // add new custom data
                                    customData.put(key, val);
                                }
                            }
                        });
                        newUserInfoDto.setCustomData(customData);
                    }
                    userInfo.setUserInfo(newUserInfoDto);
                    userInfo.setLastUpdAt(new Date());
                    mongoTemplate.updateMulti(query, Update.update("userInfo", newUserInfoDto)
                                    .set("lastOpTime", new Date())
                                    .set("lastUpdAt", new Date()), UserInfo.class);
                }catch (Exception e){
                    log.error("Get user info failed,userId: {} message: {}", userId, e.getMessage(), e);
                }
            }
        }

        if (userInfo.getAvatar() != null) {
            userInfoDto.setAvatar(userInfo.getAvatar());
        }

        userInfoDto.setEnableGrayScale(userInfo.getEnableGrayScale());

        return userInfoDto;
    }

    public void refreshUsers(List<String> userIds) {
        log.info("Refresh users: {}", userIds);
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        long startTime;
        for (String userId : userIds) {
            startTime = System.currentTimeMillis();
            List<String> roleNames = getUserRoles(userId, null);

            Query query = Query.query(Criteria.where("userInfo.id").is(userId));
            mongoTemplate.updateMulti(query, Update.update("userInfo.authoritys", roleNames), UserInfo.class);
            long cost = System.currentTimeMillis() - startTime;
            if (cost < 200) { // limit speed
                try {
                    Thread.sleep( 200 - cost);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }

    }

    public List<String> getUserRoles(String userId, String userSourceId) {
        String appId = userRoleNamespaceId;
        if (userSourceId != null && pattern.matcher(userSourceId).matches()) {
            appId = userSourceId;
        }
        try {
            ManagementClient managementClient = getInstance();
            GetUserRolesDto getUserRolesDto = new GetUserRolesDto();
            getUserRolesDto.setUserId(userId);
            getUserRolesDto.setNamespace(appId);
            RolePaginatedRespDto roles = managementClient.getUserRoles(getUserRolesDto);
            log.debug("Get user({}) roles {}, namespace {}", userId, roles, userRoleNamespaceId);
            return roles.getData().getList().stream()
                    .filter(roleDto -> "ENABLE".equalsIgnoreCase(roleDto.getStatus()))
                    .map(RoleDto::getCode).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Get user role failed for user {}({}).{}", appId, userSourceId, userId);
            return Collections.emptyList();
        }
    }

    public UserInfoDto getUserInfoByAuthing(String userId)throws BizException {

        ManagementClient managementClient = getInstance();

        try {
            GetUserDto getUserDto  = new GetUserDto();
            getUserDto.setUserId(userId);
            getUserDto.setWithIdentities(true);
            getUserDto.setWithCustomData(true);
            UserSingleRespDto userSingleRespDto = managementClient.getUser(getUserDto);
            if(userSingleRespDto.getStatusCode()!=200){
                log.error("Query authing user [{}] failed {}", userId, userSingleRespDto.getMessage());
                throw new BizException("QueryUserInfoFromAuthingFailed", userSingleRespDto.getMessage());
            }
            UserDto userDto = userSingleRespDto.getData();
            GetUserIdentitiesDto getUserIdentities = new GetUserIdentitiesDto();
            getUserIdentities.setUserId(getUserDto.getUserId());
            IdentityListRespDto userIdentities = managementClient.getUserIdentities(getUserIdentities);

            if (userIdentities.getStatusCode() != 200) {
                log.error("Query authing user identities [{}] failed {}", userId, userSingleRespDto.getMessage());
                throw new BizException("QueryUserIdentitiesFromAuthingFailed", userSingleRespDto.getMessage());
            }

            userDto.setIdentities(userIdentities.getData());
            return buildUserInfo(userDto);
        } catch (Exception e) {
            log.error("Query authing user [{}] failed", userId, e);
            throw new BizException("QueryUserInfoFromAuthingFailed", e);
        }
    }

    private UserInfoDto buildUserInfo(UserDto user) throws InvocationTargetException, IllegalAccessException {
        UserInfoDto userInfoDto = new UserInfoDto();
        if (user.getStatus() != null) {
            userInfoDto.setStatus(user.getStatus().getValue());
            user.setStatus(null);
        }
        UserDto.UserSourceType userSourceType = user.getUserSourceType();
        user.setUserSourceType(null);
        BeanUtils.copyProperties(userInfoDto, user);

        userInfoDto.setId(user.getUserId());
        userInfoDto.setUserId(user.getUserId());
        userInfoDto.setEmail(user.getEmail());
        userInfoDto.setTelephone(user.getPhone());
        userInfoDto.setPhoneCountryCode(user.getPhoneCountryCode());
            /*String phoneNumber = user.getPhone();
            if (phoneNumber != null && phoneNumber.length() > 8) {
                String temp = phoneNumber.substring(0,3) + "****" + phoneNumber.substring(7);
                userInfoDto.setTelephone(temp);
            }*/
        userInfoDto.setCustomerId(user.getUserId());
        userInfoDto.setUsername(user.getUsername() != null ? user.getUsername() : user.getName());
        if (userInfoDto.getUsername() == null) {
            userInfoDto.setUsername(user.getEmail());
        }
        if (userInfoDto.getUsername() == null) {
            userInfoDto.setUsername(user.getPhone());
        }
        String nickname = user.getNickname();
        if (nickname == null) nickname = userInfoDto.getUsername();
        if (nickname == null) nickname = user.getEmail();
        userInfoDto.setNickname(nickname);
        //userInfoDto.setAuthoritys(Collections.emptyList());
        userInfoDto.setAuthoritys(getUserRoles(user.getUserId(), user.getUserSourceId()));
        userInfoDto.setIsCustomer(0);

        if (StringUtils.isBlank(userInfoDto.getOpenid()) && user.getIdentities() != null) {
            user.getIdentities().stream().filter(r -> "wechat".equals(r.getProvider().getValue())).forEach(r -> {
                if ("openid".equals(r.getType())) {
                    userInfoDto.setOpenid(r.getUserIdInIdp());
                } else if ("unionid".equals(r.getType())) {
                    userInfoDto.setUnionid(r.getUserIdInIdp());
                }
            });
        }
        if (userSourceType != null) {
            userInfoDto.setUserSourceType(userSourceType.getValue());
        }
        return userInfoDto;
    }

    /**
     * 修改用户手机号码
     * @param id
     * @param phone
     * @param areaCode
     * @param phoneVerified
     * @return
     */
    public UserInfoDto updatePhone(String id, String phone, String areaCode, boolean phoneVerified) {
        UpdateUserReqDto updateUserReqDto = new UpdateUserReqDto();
        updateUserReqDto.setPhone(phone);
        updateUserReqDto.setPhoneVerified(phoneVerified);
        updateUserReqDto.setPhoneCountryCode(areaCode);
        return updateUser(id, updateUserReqDto);
    }

    /**
     * 修改用户邮箱
     * @param id
     * @param email
     * @param emailVerified
     * @return
     */
    public UserInfoDto updateEmail(String id, String email, boolean emailVerified) {
        UpdateUserReqDto updateUserReqDto = new UpdateUserReqDto();
        updateUserReqDto.setEmail(email);
        updateUserReqDto.setEmailVerified(emailVerified);
        return updateUser(id, updateUserReqDto);
    }

    /**
     * 修改用户密码
     * @param id
     * @param password
     * @return
     */
    public UserInfoDto updatePassword(String id, String password) {
        UpdateUserReqDto updateUserReqDto = new UpdateUserReqDto();
        updateUserReqDto.setPassword(password);
        UserInfoDto userInfo = getUserInfo(id);
        if (StringUtils.isNotBlank(userInfo.getUsername())) {
            updateUserReqDto.setUsername(userInfo.getUsername());
        }
        return updateUser(id, updateUserReqDto);
    }

    /**
     * 修改用户
     * @return
     */
    public UserInfoDto updateUserInfo(String id, UpdateUserInfoReq updateUserInfoReq) {
        UpdateUserReqDto updateUserReqDto = new UpdateUserReqDto();
        if (StringUtils.isNotBlank(updateUserInfoReq.getNickname()))
            updateUserReqDto.setNickname(updateUserInfoReq.getNickname());
        if (updateUserInfoReq.getCustomData() != null)
            updateUserReqDto.setCustomData(updateUserInfoReq.getCustomData());
        if (StringUtils.isNotBlank(updateUserInfoReq.getLocale()))
            updateUserReqDto.setLocale(updateUserInfoReq.getLocale());
        return updateUser(id, updateUserReqDto);
    }

    private UserInfoDto updateUser(String id, UpdateUserReqDto updateUserReqDto) {
        updateUserReqDto.setUserId(id);
        log.info("Update Authing user info: {}, {}", id, JsonUtil.toJson(updateUserReqDto));
        try {
            ManagementClient managementClient = getInstance();
            //managementClient.requestToken().execute();
            UserSingleRespDto userSingleRespDto = managementClient.updateUser(updateUserReqDto);
            if(userSingleRespDto.getStatusCode()!=200){
                log.error("Query authing user [{}] failed", id, userSingleRespDto.getMessage());
                throw new BizException("QueryUserInfoFromAuthingFailed", userSingleRespDto.getMessage());
            }
            return buildUserInfo(userSingleRespDto.getData());
        } catch (Exception e) {
            log.error("Update authing user [{}] failed", id, e);
            throw new BizException("Authing.User.Update.Failed", e);
        }
    }
}
