package com.tapdata.manager.agent.entity;

import com.tapdata.manager.agent.dto.FreeTime;
import com.tapdata.manager.agent.dto.OrderInfo;
import com.tapdata.manager.agent.dto.Spec;
import com.tapdata.manager.agent.dto.TmInfo;
import com.tapdata.manager.base.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2021/1/27 下午8:01
 * @description
 */
@Document("drs_agent")
@Setter
@Getter
@ToString(callSuper = true)
public class Agent extends BaseEntity {

    /**
     * 实例名称
     */
    private String name;

    /**
     * 云厂商
     */
    private String provider;

    /**
     * 地域
     */
    private String region;

    /**
     * 可用区
     */
    private String zone;

    /**
     * 实例状态，参考《状态变迁》章节
     */
    private String status;
    /**
     * 可维护时间设置
     */
    private FreeTime freeTime;

    /**
     * 实例规格
     */
    private Spec spec;

    /**
     * 订单信息
     */
    private OrderInfo orderInfo;

    /**
     * 状态为异常时，显示错误消息
     */
    private String message;

    /**
     * 状态为异常时，记录错误代码
     */
    private String errorCode;

    /**
     * 记录tapdata manager的一些关联信息
     */
    private TmInfo tmInfo;

    /**
     * k8s node selector
     */
    private Map<String, String> nodeSelector;

    private String clusterId;


    /**
     * 实例类型 local 本地自建   cloud  云上托管
     **/
    private String agentType;

    /**
     * 实例创建时间
     */
    private Date agentCreateTime;

    /**
     * 实例销毁时间
     */
    private Date agentDestroyTime;

    /**
     * 实例标签
     */
    private List<String> tags;

}
