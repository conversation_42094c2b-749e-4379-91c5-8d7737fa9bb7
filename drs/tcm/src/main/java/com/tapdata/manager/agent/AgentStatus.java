package com.tapdata.manager.agent;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2021/1/27 下午8:17
 * @description
 */
public enum AgentStatus {

    Approving,	// 审核中(等待创建中)
    Creating,	// 创建中
    Starting,	// 启动中
    Running,	// 运行中
    Stopping,	// 停止中
    Stopped,	// 已停止
    Error,		// 有错误
    Freezing,	// 冻结中
    Freeze,		// 已冻结
    Deleting,	// 删除中
    Deleted,	// 已删除
    WaitingRestart, // 等待重启中
    Restarting,	// 重启中
    Recovering,	// 恢复中(备份恢复)
    FreezeRecovering, // 恢复中(冻结恢复)
    Altering,	// 规格变更中
    WaitingAlter, // 等待规格变更
    WaitingDelete, //等待删除中

}
