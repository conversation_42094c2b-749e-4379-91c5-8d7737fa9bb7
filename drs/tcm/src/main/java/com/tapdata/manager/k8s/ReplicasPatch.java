package com.tapdata.manager.k8s;


import com.tapdata.manager.common.utils.JsonUtil;
import lombok.Data;

import java.util.Collections;

@Data
public class ReplicasPatch {

    public static final String ZERO = JsonUtil.toJson(Collections.singleton(new ReplicasPatch(0)));
    public static final String ONE = JsonUtil.toJson(Collections.singleton(new ReplicasPatch(1)));

    private final String op = "replace";
    private final String path = "/spec/replicas";
    private int value;

    private ReplicasPatch(int value) {
        this.value = value;
    }

}
