package com.tapdata.tm.client.ldp;

import com.tapdata.manager.common.http.MethodType;
import com.tapdata.manager.common.http.region.ProductDomain;
import com.tapdata.tm.client.BaseRequest;

/**
 * <AUTHOR>
 * create at 2023/6/7 16:45
 */
public class CreateLiveDataPlatformRequest extends BaseRequest<CreateLiveDataPlatformResponse> {

    private String mode;
    private String fdmStorageCluster;
    private String fdmStorageConnectionId;
    private String mdmStorageCluster;
    private String mdmStorageConnectionId;
    private boolean isInit = false;

    public CreateLiveDataPlatformRequest(ProductDomain productDomain) {
        super(productDomain, "/api/LiveDataPlatform");
        this.setMethod(MethodType.POST);

        this.putBodyParameter("isInit", false);
    }

    @Override
    public Class<CreateLiveDataPlatformResponse> getResponseClass() {
        return CreateLiveDataPlatformResponse.class;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
        this.putBodyParameter("mode", mode);
    }

    public String getFdmStorageCluster() {
        return fdmStorageCluster;
    }

    public void setFdmStorageCluster(String fdmStorageCluster) {
        this.fdmStorageCluster = fdmStorageCluster;
        this.putBodyParameter("fdmStorageCluster", fdmStorageCluster);
    }

    public String getFdmStorageConnectionId() {
        return fdmStorageConnectionId;
    }

    public void setFdmStorageConnectionId(String fdmStorageConnectionId) {
        this.fdmStorageConnectionId = fdmStorageConnectionId;
        this.putBodyParameter("fdmStorageConnectionId", fdmStorageConnectionId);
    }

    public String getMdmStorageCluster() {
        return mdmStorageCluster;
    }

    public void setMdmStorageCluster(String mdmStorageCluster) {
        this.mdmStorageCluster = mdmStorageCluster;
        this.putBodyParameter("mdmStorageCluster", mdmStorageCluster);
    }

    public String getMdmStorageConnectionId() {
        return mdmStorageConnectionId;
    }

    public void setMdmStorageConnectionId(String mdmStorageConnectionId) {
        this.mdmStorageConnectionId = mdmStorageConnectionId;
        this.putBodyParameter("mdmStorageConnectionId", mdmStorageConnectionId);
    }

    public boolean isInit() {
        return isInit;
    }

    public void setInit(boolean init) {
        this.putBodyParameter("isInit", init);
        isInit = init;
    }
}
