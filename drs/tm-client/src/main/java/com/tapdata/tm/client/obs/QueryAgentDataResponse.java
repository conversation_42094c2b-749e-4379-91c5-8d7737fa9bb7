package com.tapdata.tm.client.obs;

import com.tapdata.tm.client.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryAgentDataResponse extends BaseResponse<QueryAgentDataResponse.MetricData> {

    @Setter
    @Getter
    public static class MetricData{
        private Samples samples;

        @lombok.Data
        public static class Samples {
            private List<Data> data;
        }

        @lombok.Data
        public static class Data {
            private double memoryRate;
            private double gcRate;
            private double cpuUsage;
            private Map<String, Object> tags;
        }
    }
}
