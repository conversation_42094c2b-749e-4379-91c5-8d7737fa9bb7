package com.tapdata.atlas.client.cluster;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2023/5/19 17:08
 */
@Data
public class PrivateEndpoint {

    private String connectionString;
    private List<Endpoint> endpoints;
    private String srvConnectionString;
    private String srvShardOptimizedConnectionString;
    private String type;

    @Data
    public static class Endpoint {
        private String endpointId;
        private String providerName;
        private String region;
    }
}
