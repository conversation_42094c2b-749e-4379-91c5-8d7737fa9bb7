/**
 * <AUTHOR>
 * @date 12/11/19
 * @description
 */
const log = require('../dist').log.app;
class EurekaClient {
	constructor(options) {
		this.options = options;
		this.client = null;
	}

	createClient(){
		log.info('create eureka client');
		this.client = require('eureka-nodejs-client')({
			eureka: {
				serviceUrl: [ this.options.eureka_url || 'http://127.0.0.1:8761'],
				pollIntervalSeconds: this.options.eureka_pollIntervalSeconds || 10,
				registerWithEureka: true,
			},
			instance:{
				app: this.options.eureka_app_name,
				ipAddr: this.options.eureka_client_ip || '127.0.0.1',
				port: this.options.eureka_client_port,
				statusPageUrl: `http://${this.options.eureka_client_ip || '127.0.0.1'}:${this.options.eureka_client_port}/status`,
				healthCheckUrl: `http://${this.options.eureka_client_ip || '127.0.0.1'}:${this.options.eureka_client_port}/status`
			}
		}, this.options.eureka_client_log_level || 'info');
		return this;
	}

	start(){
		if( !this.client ) {
			this.createClient();
		}
		log.info('connect to eureka server');
		this.client.start();
		return this;
	}

	stop() {
		if( this.client) {
			log.info('delete eureka from server');
			this.client.delete()
		}
		return this;
	}
}

const config = require('../config');
const eurekaClient = new EurekaClient(config.springCloudConfig);

module.exports = eurekaClient;
