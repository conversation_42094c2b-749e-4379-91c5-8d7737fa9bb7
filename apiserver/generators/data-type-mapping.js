/**
 * <AUTHOR>
 * @date 8/19/19
 * @description
 */

const APIDefineDataTypeToTsType = {
	'ObjectId': 'string',
	'ObjectID': 'string',
	'String': 'string',
	'string': 'string',
	'Boolean': 'boolean',
	'boolean': 'boolean',
	'Integer': 'number',
	'Short': 'number',
	'Long': 'number',
	'Double': 'number',
	'Number': 'number',
	'Float': 'number',
	'Decimal128': 'Decimal128',
	'BigDecimal': 'Decimal128',
	'Date': 'date',
	'Document': 'object',
	'DOCUMENT': 'object',
	'ArrayList': 'array',
	'Array': 'array',
	'array': 'array',
	'number': 'number',
	'date': 'date',
	'object': 'object',
	'Object': 'object',
	'Map': 'object',
	'Byte': 'number',
	'Bytes': 'buffer',
	'Null': 'string',
};
const MongoDataTypeToJava = {
	'ObjectID': 'String',
	'ObjectId': 'String',
	'Array': 'Array',
	'Boolean': 'Boolean',
	'Date': 'Date',
	'Number': 'Long',
	'Null': 'Null',
	'Decimal128': 'BigDecimal',
	'Document': 'Map',
	'Binary': 'Bytes',
	'Double': 'Double',
	'Int32': 'Integer',
	'Long': 'Long',
	'MinKey': 'Integer',
	'MaxKey': 'Integer',
	'Timestamp': 'Long',
	'String': 'String',
};

const OpenAPIDataType = {
	integer: {
		type: 'integer', format: 'int32', description: ''
	},
	long: {
		type: 'integer', format: 'int64', description: ''
	},
	float: {
		type: 'number', format: 'float', precision: 0
	},
	double: {
		type: 'number', format: 'double', precision: 0
	},
	string: {
		type: 'string', length: 0
	},
	byte: {
		type: 'string', format: 'byte', description: '', length: 0
	},
	binary: {
		type: 'string', format: 'binary', description: '', length: 0
	},
	boolean: {
		type: 'boolean'
	},
	date: {
		type: 'string', format: 'date', description: ''
	},
	dateTime: {
		type: 'string', format: 'date-time', description: ''
	},
	password: {
		type: 'string', format: 'password', description: ''
	}
};
const APIDefineDataTypeToOpenAPIDataType = {
	'ObjectId': 'string',
	'ObjectID': 'string',
	'String': 'string',
	'string': 'string',
	'Boolean': 'boolean',
	'boolean': 'boolean',
	'Integer': 'integer',
	'Short': 'integer',
	'Long': 'long',
	'Double': 'double',
	/*'Number': 'number',*/
	'Float': 'float',
	'Decimal128': 'double',
	'BigDecimal': 'double',
	'Date': 'dateTime',
	/*'Document': 'object',*/
	/*'ArrayList': 'array',
	'Array': 'array',
	'array': 'array',
	'number': 'number',*/
	'date': 'dateTime',
	/*'object': 'object',
	'Object': 'object',
	'Map': 'object',*/
	'Byte': 'byte',
	'Bytes': 'binary',
	/*'Null': 'string',*/
	'password': 'password',
};
exports.APIDefineDataTypeToTsType = APIDefineDataTypeToTsType;
exports.MongoDataTypeToJava = MongoDataTypeToJava;
exports.APIDefineDataTypeToOpenAPIDataType = APIDefineDataTypeToOpenAPIDataType;
exports.OpenAPIDataType = OpenAPIDataType;
