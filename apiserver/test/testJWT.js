
const jwt = require('jsonwebtoken');

const publicKey =
	`-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwnEhVBLrafADQY8XWiAJ
kApaqD6QGY95oQjp4Xg4Tg77phn3bTzXULGWDlwehuMQChQZeprg296TLQalAKQR
N8dUdqbbWdnidx9/k6mcD6CFlCIarNH9LXhoOxAMcI+WhKBbgUZwhP/psC6k4Bq8
KkNUsMXG3u/MycLzKl4Tqu1vQP0da35d6Z1UigpB6IqsdBss0FmgcK1NnQUHQeL1
mMuJiU8N5cSd+h+r4NjfsRwJx2yklcH+JqlZtu1Q2DgIPSYvqPVGGeG8thptWnVF
WhF1LttOU9Lq0fZvm0YHLInGfbaJsjajCFzpiQxsvxdB8bjIPXY35fEAnAKqAOcE
6QIDAQAB
-----END PUBLIC KEY-----
`;

// token = '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
let token = '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
let payload = jwt.verify(token, publicKey, {algorithm: 'RS256'});

console.log(payload);

verify(payload, function(err, user, message){
	console.log("err:", err);
	console.log("user:", user);
	console.log("message:", message);
});

function verify( payload, cb ) {

	// @ts-ignore
	const apiRoles = ['admin'];

	payload = payload || {};

	const name = payload['name'],
		expireDateTime = name === 'Guest user' ? new Date().getTime() + 60000: payload['expiredate'],
		roles = payload['roles'],
		user_id = payload['clientId'],
		email = payload['email']
	;

	console.log(`auth user ${user_id}, payload is ${JSON.stringify(payload)}, api roles is ${JSON.stringify(apiRoles)}`);

	if( !expireDateTime || !roles || roles.length === 0 || !user_id ){
		cb(null, false, 'invalid token');
		return;
	}
	// 验证过期时间
	/*if( expireDateTime < new Date().getTime() ){
		cb(null, false, 'token expired');
		return;
	}*/

	// 验证api角色列表
	// @ts-ignore
	const hasRole = apiRoles.filter( role => '$everyone' === role || roles.includes(role));
	if( hasRole && hasRole.length > 0){
		cb(null, {
			id: user_id,
			name: name,
			email: email
		});
	} else {
		cb(null, false, 'Authorization fail');
	}
}
