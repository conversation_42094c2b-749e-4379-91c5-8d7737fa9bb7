{"description": "BulkWrite deleteOne-let", "schemaVersion": "1.0", "createEntities": [{"client": {"id": "client0", "observeEvents": ["commandStartedEvent"]}}, {"database": {"id": "database0", "client": "client0", "databaseName": "crud-tests"}}, {"collection": {"id": "collection0", "database": "database0", "collectionName": "coll0"}}], "initialData": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 1}, {"_id": 2}]}], "tests": [{"description": "BulkWrite deleteOne with let option", "runOnRequirements": [{"minServerVersion": "5.0"}], "operations": [{"object": "collection0", "name": "bulkWrite", "arguments": {"requests": [{"deleteOne": {"filter": {"$expr": {"$eq": ["$_id", "$$id"]}}}}], "let": {"id": 1}}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"delete": "coll0", "deletes": [{"q": {"$expr": {"$eq": ["$_id", "$$id"]}}, "limit": 1}], "let": {"id": 1}}}}]}], "outcome": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 2}]}]}, {"description": "BulkWrite deleteOne with let option unsupported (server-side error)", "runOnRequirements": [{"minServerVersion": "3.6.0", "maxServerVersion": "4.9"}], "operations": [{"object": "collection0", "name": "bulkWrite", "arguments": {"requests": [{"deleteOne": {"filter": {"$expr": {"$eq": ["$_id", "$$id"]}}}}], "let": {"id": 1}}, "expectError": {"errorContains": "'delete.let' is an unknown field", "isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"delete": "coll0", "deletes": [{"q": {"$expr": {"$eq": ["$_id", "$$id"]}}, "limit": 1}], "let": {"id": 1}}}}]}], "outcome": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 1}, {"_id": 2}]}]}]}