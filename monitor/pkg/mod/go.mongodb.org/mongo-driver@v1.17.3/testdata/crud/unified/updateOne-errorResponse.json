{"description": "updateOne-errorResponse", "schemaVersion": "1.12", "createEntities": [{"client": {"id": "client0", "useMultipleMongoses": false}}, {"database": {"id": "database0", "client": "client0", "databaseName": "crud-tests"}}, {"collection": {"id": "collection0", "database": "database0", "collectionName": "test"}}], "tests": [{"description": "update operations support errorResponse assertions", "runOnRequirements": [{"minServerVersion": "4.0.0", "topologies": ["single", "replicaset"]}, {"minServerVersion": "4.2.0", "topologies": ["sharded"]}], "operations": [{"name": "failPoint", "object": "testRunner", "arguments": {"client": "client0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "errorCode": 8}}}}, {"name": "updateOne", "object": "collection0", "arguments": {"filter": {"_id": 1}, "update": {"$set": {"x": 1}}}, "expectError": {"errorCode": 8, "errorResponse": {"code": 8}}}]}]}