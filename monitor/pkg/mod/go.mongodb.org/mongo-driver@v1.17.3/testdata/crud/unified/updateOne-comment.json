{"description": "updateOne-comment", "schemaVersion": "1.0", "createEntities": [{"client": {"id": "client0", "observeEvents": ["commandStartedEvent"]}}, {"database": {"id": "database0", "client": "client0", "databaseName": "crud-tests"}}, {"collection": {"id": "collection0", "database": "database0", "collectionName": "coll0"}}], "initialData": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 11}]}], "tests": [{"description": "Update<PERSON><PERSON> with string comment", "runOnRequirements": [{"minServerVersion": "4.4"}], "operations": [{"name": "updateOne", "object": "collection0", "arguments": {"filter": {"_id": 1}, "update": {"$set": {"x": 22}}, "comment": "comment"}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"update": "coll0", "updates": [{"q": {"_id": 1}, "u": {"$set": {"x": 22}}, "multi": {"$$unsetOrMatches": false}, "upsert": {"$$unsetOrMatches": false}}], "comment": "comment"}}}]}], "outcome": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 22}]}]}, {"description": "UpdateOne with document comment", "runOnRequirements": [{"minServerVersion": "4.4"}], "operations": [{"name": "updateOne", "object": "collection0", "arguments": {"filter": {"_id": 1}, "update": {"$set": {"x": 22}}, "comment": {"key": "value"}}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"update": "coll0", "updates": [{"q": {"_id": 1}, "u": {"$set": {"x": 22}}, "multi": {"$$unsetOrMatches": false}, "upsert": {"$$unsetOrMatches": false}}], "comment": {"key": "value"}}}}]}], "outcome": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 22}]}]}, {"description": "UpdateOne with comment - pre 4.4", "runOnRequirements": [{"minServerVersion": "3.4.0", "maxServerVersion": "4.2.99"}], "operations": [{"name": "updateOne", "object": "collection0", "arguments": {"filter": {"_id": 1}, "update": {"$set": {"x": 22}}, "comment": "comment"}, "expectError": {"isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"update": "coll0", "updates": [{"q": {"_id": 1}, "u": {"$set": {"x": 22}}, "multi": {"$$unsetOrMatches": false}, "upsert": {"$$unsetOrMatches": false}}], "comment": "comment"}}}]}], "outcome": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 11}]}]}]}