{"description": "replaceOne-comment", "schemaVersion": "1.0", "createEntities": [{"client": {"id": "client0", "observeEvents": ["commandStartedEvent"]}}, {"database": {"id": "database0", "client": "client0", "databaseName": "crud-tests"}}, {"collection": {"id": "collection0", "database": "database0", "collectionName": "coll0"}}], "initialData": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 11}]}], "tests": [{"description": "Replace<PERSON>ne with string comment", "runOnRequirements": [{"minServerVersion": "4.4"}], "operations": [{"name": "replaceOne", "object": "collection0", "arguments": {"filter": {"_id": 1}, "replacement": {"x": 22}, "comment": "comment"}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"update": "coll0", "updates": [{"q": {"_id": 1}, "u": {"x": 22}}], "comment": "comment"}}}]}], "outcome": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 22}]}]}, {"description": "ReplaceOne with document comment", "runOnRequirements": [{"minServerVersion": "4.4"}], "operations": [{"name": "replaceOne", "object": "collection0", "arguments": {"filter": {"_id": 1}, "replacement": {"x": 22}, "comment": {"key": "value"}}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"update": "coll0", "updates": [{"q": {"_id": 1}, "u": {"x": 22}}], "comment": {"key": "value"}}}}]}], "outcome": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 22}]}]}, {"description": "Replace<PERSON>ne with comment - pre 4.4", "runOnRequirements": [{"minServerVersion": "3.4.0", "maxServerVersion": "4.2.99"}], "operations": [{"name": "replaceOne", "object": "collection0", "arguments": {"filter": {"_id": 1}, "replacement": {"x": 22}, "comment": "comment"}, "expectError": {"isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"update": "coll0", "updates": [{"q": {"_id": 1}, "u": {"x": 22}}], "comment": "comment"}}}]}], "outcome": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 11}]}]}]}