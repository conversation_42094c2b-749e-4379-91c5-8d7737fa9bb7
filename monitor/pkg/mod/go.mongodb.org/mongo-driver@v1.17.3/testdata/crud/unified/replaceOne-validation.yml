description: "replaceOne-validation"

schemaVersion: "1.0"

createEntities:
  - client:
      id: &client0 client0
      observeEvents: [ commandStartedEvent ]
  - database:
      id: &database0 database0
      client: *client0
      databaseName: &database0Name crud-tests
  - collection:
      id: &collection0 collection0
      database: *database0
      collectionName: &collection0Name coll0

initialData: &initialData
  - collectionName: *collection0Name
    databaseName: *database0Name
    documents:
      - { _id: 1, x: 11 }

tests:
  - description: "ReplaceOne prohibits atomic modifiers"
    operations:
      - name: replaceOne
        object: *collection0
        arguments:
          filter: { _id: 1 }
          replacement: { $set: { x: 22 } }
        expectError:
          isClientError: true
    expectEvents:
      - client: *client0
        events: []
    outcome: *initialData
