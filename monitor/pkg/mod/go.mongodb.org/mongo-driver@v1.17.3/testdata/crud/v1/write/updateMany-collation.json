{"data": [{"_id": 1, "x": 11}, {"_id": 2, "x": "ping"}, {"_id": 3, "x": "pINg"}], "minServerVersion": "3.4", "serverless": "forbid", "tests": [{"description": "UpdateMany when many documents match with collation", "operation": {"name": "updateMany", "arguments": {"filter": {"x": "ping"}, "update": {"$set": {"x": "pong"}}, "collation": {"locale": "en_US", "strength": 2}}}, "outcome": {"result": {"matchedCount": 2, "modifiedCount": 2, "upsertedCount": 0}, "collection": {"data": [{"_id": 1, "x": 11}, {"_id": 2, "x": "pong"}, {"_id": 3, "x": "pong"}]}}}]}