{"data": [{"_id": 1, "x": "PING"}], "minServerVersion": "3.4", "serverless": "forbid", "tests": [{"description": "Count documents with collation", "operation": {"name": "countDocuments", "arguments": {"filter": {"x": "ping"}, "collation": {"locale": "en_US", "strength": 2}}}, "outcome": {"result": 1}}, {"description": "Deprecated count with collation", "operation": {"name": "count", "arguments": {"filter": {"x": "ping"}, "collation": {"locale": "en_US", "strength": 2}}}, "outcome": {"result": 1}}]}