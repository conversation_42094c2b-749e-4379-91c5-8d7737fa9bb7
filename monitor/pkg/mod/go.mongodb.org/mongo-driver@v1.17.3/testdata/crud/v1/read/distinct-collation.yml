data:
    - {_id: 1, string: 'PING'}
    - {_id: 2, string: 'ping'}
minServerVersion: '3.4'
serverless: 'forbid'

tests:
    -
        description: "Distinct with a collation"
        operation:
            name: distinct
            arguments:
                fieldName: "string"
                collation: { locale: 'en_US', strength: 2 } # https://www.mongodb.com/docs/manual/reference/collation/#collation-document

        outcome:
            result:
                - 'PING'
