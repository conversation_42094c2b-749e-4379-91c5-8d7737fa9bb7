{"data": [{"_id": 1, "x": 11}, {"_id": 2, "x": 22}, {"_id": 3, "x": 33}], "minServerVersion": "2.6", "tests": [{"description": "UpdateOne when many documents match", "operation": {"name": "updateOne", "arguments": {"filter": {"_id": {"$gt": 1}}, "update": {"$inc": {"x": 1}}}}, "outcome": {"result": {"matchedCount": 1, "modifiedCount": 1, "upsertedCount": 0}}}, {"description": "UpdateOne when one document matches", "operation": {"name": "updateOne", "arguments": {"filter": {"_id": 1}, "update": {"$inc": {"x": 1}}}}, "outcome": {"result": {"matchedCount": 1, "modifiedCount": 1, "upsertedCount": 0}, "collection": {"data": [{"_id": 1, "x": 12}, {"_id": 2, "x": 22}, {"_id": 3, "x": 33}]}}}, {"description": "UpdateOne when no documents match", "operation": {"name": "updateOne", "arguments": {"filter": {"_id": 4}, "update": {"$inc": {"x": 1}}}}, "outcome": {"result": {"matchedCount": 0, "modifiedCount": 0, "upsertedCount": 0}, "collection": {"data": [{"_id": 1, "x": 11}, {"_id": 2, "x": 22}, {"_id": 3, "x": 33}]}}}, {"description": "UpdateOne with upsert when no documents match", "operation": {"name": "updateOne", "arguments": {"filter": {"_id": 4}, "update": {"$inc": {"x": 1}}, "upsert": true}}, "outcome": {"result": {"matchedCount": 0, "modifiedCount": 0, "upsertedCount": 1, "upsertedId": 4}, "collection": {"data": [{"_id": 1, "x": 11}, {"_id": 2, "x": 22}, {"_id": 3, "x": 33}, {"_id": 4, "x": 1}]}}}]}