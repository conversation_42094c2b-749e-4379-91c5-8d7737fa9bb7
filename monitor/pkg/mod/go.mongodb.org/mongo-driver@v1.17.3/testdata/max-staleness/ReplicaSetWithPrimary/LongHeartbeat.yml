# If users configure a longer ``heartbeatFrequencyMS`` than the default,
# ``maxStalenessSeconds`` might have a larger minimum.
---
heartbeatFrequencyMS: 120000  # 120 seconds.
topology_description:
  type: ReplicaSetWithPrimary
  servers:
  - &1
    address: a:27017
    type: RSPrimary
    avg_rtt_ms: 5
    lastUpdateTime: 0
    maxWireVersion: 6
    lastWrite: {lastWriteDate: {$numberLong: "1"}}
  - &2
    address: b:27017
    type: RSSecondary
    avg_rtt_ms: 50  # Too far.
    lastUpdateTime: 0
    maxWireVersion: 6
    lastWrite: {lastWriteDate: {$numberLong: "1"}}
read_preference:
  mode: Nearest
  maxStalenessSeconds: 130  # OK, must be 120 + 10 = 130 seconds.
suitable_servers:
- *1
- *2
in_latency_window:
- *1
