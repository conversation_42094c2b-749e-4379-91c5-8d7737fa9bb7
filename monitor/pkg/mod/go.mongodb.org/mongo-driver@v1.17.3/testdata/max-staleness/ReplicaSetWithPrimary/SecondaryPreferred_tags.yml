# Primary's lastWriteDate is used normally with SecondaryPreferred and tags.
---
heartbeatFrequencyMS: 25000  # 25 seconds.
topology_description:
  type: ReplicaSetWithPrimary
  servers:
  - &1
    address: a:27017
    type: RSPrimary
    avg_rtt_ms: 5
    lastUpdateTime: 0
    maxWireVersion: 6
    lastWrite: {lastWriteDate: {$numberLong: "125002"}}
  - &2
    address: b:27017
    type: RSSecondary
    avg_rtt_ms: 5
    lastUpdateTime: 0
    maxWireVersion: 6
    lastWrite: {lastWriteDate: {$numberLong: "2"}}  # 125 sec stale + 25 sec heartbeat <= 150 sec maxStaleness.
    tags:
      data_center: nyc
  - &3
    address: c:27017
    type: RSSecondary
    avg_rtt_ms: 50  # Too far.
    lastUpdateTime: 1
    maxWireVersion: 6
    lastWrite: {lastWriteDate: {$numberLong: "1000001"}}  # Not used in estimate since we have a primary.
    tags:
      data_center: nyc
  - &4
    address: d:27017
    type: RSSecondary
    avg_rtt_ms: 5
    lastUpdateTime: 0
    maxWireVersion: 6
    lastWrite: {lastWriteDate: {$numberLong: "1"}}  # Too stale.
    tags:
      data_center: nyc
  - &5
    address: e:27017
    type: RSSecondary
    avg_rtt_ms: 5
    lastUpdateTime: 0
    maxWireVersion: 6
    lastWrite: {lastWriteDate: {$numberLong: "2"}}
    tags:
      data_center: tokyo  # No match.
read_preference:
  mode: SecondaryPreferred
  maxStalenessSeconds: 150
  tag_sets:
  - data_center: nyc
suitable_servers:
- *2
- *3
in_latency_window:
- *2
