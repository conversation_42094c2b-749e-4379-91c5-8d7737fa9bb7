{"version": 1, "style": "unit", "description": "must never exceed maxPoolSize total connections", "poolOptions": {"maxPoolSize": 3}, "operations": [{"name": "ready"}, {"name": "checkOut", "label": "conn1"}, {"name": "checkOut"}, {"name": "checkOut", "label": "conn2"}, {"name": "checkIn", "connection": "conn2"}, {"name": "checkOut"}, {"name": "start", "target": "thread1"}, {"name": "checkOut", "thread": "thread1"}, {"name": "waitForEvent", "event": "ConnectionCheckOutStarted", "count": 5}, {"name": "checkIn", "connection": "conn1"}, {"name": "waitForThread", "target": "thread1"}], "events": [{"type": "ConnectionPoolCreated", "address": 42, "options": 42}, {"type": "ConnectionCheckOutStarted", "address": 42}, {"type": "ConnectionCreated", "connectionId": 42, "address": 42}, {"type": "ConnectionCheckedOut", "connectionId": 42, "address": 42}, {"type": "ConnectionCheckOutStarted", "address": 42}, {"type": "ConnectionCreated", "connectionId": 42, "address": 42}, {"type": "ConnectionCheckedOut", "connectionId": 42, "address": 42}, {"type": "ConnectionCheckOutStarted", "address": 42}, {"type": "ConnectionCreated", "connectionId": 42, "address": 42}, {"type": "ConnectionCheckedOut", "connectionId": 42, "address": 42}, {"type": "ConnectionCheckedIn", "connectionId": 42, "address": 42}, {"type": "ConnectionCheckOutStarted", "address": 42}, {"type": "ConnectionCheckedOut", "connectionId": 42, "address": 42}, {"type": "ConnectionCheckOutStarted", "address": 42}, {"type": "ConnectionCheckedIn", "connectionId": 42, "address": 42}, {"type": "ConnectionCheckedOut", "connectionId": 42, "address": 42}], "ignore": ["ConnectionReady", "ConnectionPoolReady"]}