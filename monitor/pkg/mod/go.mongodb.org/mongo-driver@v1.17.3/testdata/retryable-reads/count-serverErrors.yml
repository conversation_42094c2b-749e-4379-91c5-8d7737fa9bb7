runOn:
    -
        minServerVersion: "4.0"
        topology: ["single", "replicaset"]
    -
        minServerVersion: "4.1.7"
        topology: ["sharded", "load-balanced"]

database_name: &database_name "retryable-reads-tests"
collection_name: &collection_name "coll"

data:
    - { _id: 1, x: 11 }
    - { _id: 2, x: 22 }

tests:
    -
        description: "Count succeeds after InterruptedAtShutdown"
        failPoint: &failCommand_failPoint
            configureFailPoint: failCommand
            mode: { times: 1 }
            data: { failCommands: [count], errorCode: 11600 }
        operations:
            - &retryable_operation_succeeds
                <<: &retryable_operation
                    name: count
                    object: collection
                    arguments: { filter: { } }
                result: 2
        expectations:
            - &retryable_command_started_event
                command_started_event:
                    command:
                        count: *collection_name
                    database_name: *database_name
            - *retryable_command_started_event
    -
        description: "Count succeeds after InterruptedDueToReplStateChange"
        failPoint:
            <<: *failCommand_failPoint
            data: { failCommands: [count], errorCode: 11602 }
        operations: [*retryable_operation_succeeds]
        expectations:
             - *retryable_command_started_event
             - *retryable_command_started_event
    -
        description: "Count succeeds after NotWritablePrimary"
        failPoint:
            <<: *failCommand_failPoint
            data: { failCommands: [count], errorCode: 10107 }
        operations: [*retryable_operation_succeeds]
        expectations:
             - *retryable_command_started_event
             - *retryable_command_started_event
    -
        description: "Count succeeds after NotPrimaryNoSecondaryOk"
        failPoint:
            <<: *failCommand_failPoint
            data: { failCommands: [count], errorCode: 13435 }
        operations: [*retryable_operation_succeeds]
        expectations:
             - *retryable_command_started_event
             - *retryable_command_started_event
    -
        description: "Count succeeds after NotPrimaryOrSecondary"
        failPoint:
            <<: *failCommand_failPoint
            data: { failCommands: [count], errorCode: 13436 }
        operations: [*retryable_operation_succeeds]
        expectations:
             - *retryable_command_started_event
             - *retryable_command_started_event
    -
        description: "Count succeeds after PrimarySteppedDown"
        failPoint:
            <<: *failCommand_failPoint
            data: { failCommands: [count], errorCode: 189 }
        operations: [*retryable_operation_succeeds]
        expectations:
             - *retryable_command_started_event
             - *retryable_command_started_event
    -
        description: "Count succeeds after ShutdownInProgress"
        failPoint:
            <<: *failCommand_failPoint
            data: { failCommands: [count], errorCode: 91 }
        operations: [*retryable_operation_succeeds]
        expectations:
             - *retryable_command_started_event
             - *retryable_command_started_event
    -
        description: "Count succeeds after HostNotFound"
        failPoint:
            <<: *failCommand_failPoint
            data: { failCommands: [count], errorCode: 7 }
        operations: [*retryable_operation_succeeds]
        expectations:
             - *retryable_command_started_event
             - *retryable_command_started_event
    -
        description: "Count succeeds after HostUnreachable"
        failPoint:
            <<: *failCommand_failPoint
            data: { failCommands: [count], errorCode: 6 }
        operations: [*retryable_operation_succeeds]
        expectations:
             - *retryable_command_started_event
             - *retryable_command_started_event
    -
        description: "Count succeeds after NetworkTimeout"
        failPoint:
            <<: *failCommand_failPoint
            data: { failCommands: [count], errorCode: 89 }
        operations: [*retryable_operation_succeeds]
        expectations:
             - *retryable_command_started_event
             - *retryable_command_started_event
    -
        description: "Count succeeds after SocketException"
        failPoint:
            <<: *failCommand_failPoint
            data: { failCommands: [count], errorCode: 9001 }
        operations: [*retryable_operation_succeeds]
        expectations:
             - *retryable_command_started_event
             - *retryable_command_started_event
    -
        description: "Count fails after two NotWritablePrimary errors"
        failPoint:
            <<: *failCommand_failPoint
            mode: { times: 2 }
            data: { failCommands: [count], errorCode: 10107 }
        operations:
            - &retryable_operation_fails
                <<: *retryable_operation
                error: true
        expectations:
             - *retryable_command_started_event
             - *retryable_command_started_event
    -
        description: "Count fails after NotWritablePrimary when retryReads is false"
        clientOptions:
            retryReads: false
        failPoint:
            <<: *failCommand_failPoint
            data: { failCommands: [count], errorCode: 10107 }
        operations: [*retryable_operation_fails]
        expectations:
             - *retryable_command_started_event

