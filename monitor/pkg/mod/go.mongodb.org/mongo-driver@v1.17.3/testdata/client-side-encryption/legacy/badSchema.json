{"runOn": [{"minServerVersion": "4.1.10"}], "database_name": "default", "collection_name": "default", "data": [], "key_vault_data": [{"status": 1, "_id": {"$binary": {"base64": "AAAAAAAAAAAAAAAAAAAAAA==", "subType": "04"}}, "masterKey": {"provider": "aws", "key": "arn:aws:kms:us-east-1:579766882180:key/89fcc2c4-08b0-4bd9-9f25-e30687b580d0", "region": "us-east-1"}, "updateDate": {"$date": {"$numberLong": "1552949630483"}}, "keyMaterial": {"$binary": {"base64": "AQICAHhQNmWG2CzOm1dq3kWLM+iDUZhEqnhJwH9wZVpuZ94A8gEqnsxXlR51T5EbEVezUqqKAAAAwjCBvwYJKoZIhvcNAQcGoIGxMIGuAgEAMIGoBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDHa4jo6yp0Z18KgbUgIBEIB74sKxWtV8/YHje5lv5THTl0HIbhSwM6EqRlmBiFFatmEWaeMk4tO4xBX65eq670I5TWPSLMzpp8ncGHMmvHqRajNBnmFtbYxN3E3/WjxmdbOOe+OXpnGJPcGsftc7cB2shRfA4lICPnE26+oVNXT6p0Lo20nY5XC7jyCO", "subType": "00"}}, "creationDate": {"$date": {"$numberLong": "1552949630483"}}, "keyAltNames": ["altname", "another_altname"]}], "tests": [{"description": "Schema with an encrypted field in an array", "clientOptions": {"autoEncryptOpts": {"schemaMap": {"default.default": {"properties": {"encrypted_string": {"encrypt": {"keyId": [{"$binary": {"base64": "AAAAAAAAAAAAAAAAAAAAAA==", "subType": "04"}}], "bsonType": "string", "algorithm": "AEAD_AES_256_CBC_HMAC_SHA_512-Random"}}}, "bsonType": "array"}}, "kmsProviders": {"aws": {}}}}, "operations": [{"name": "insertOne", "arguments": {"document": {"_id": 1, "encrypted_string": "string0"}}, "result": {"errorContains": "Invalid schema"}}], "outcome": {"collection": {"data": []}}}, {"description": "Schema without specifying parent object types", "clientOptions": {"autoEncryptOpts": {"schemaMap": {"default.default": {"properties": {"foo": {"properties": {"bar": {"encrypt": {"keyId": [{"$binary": {"base64": "AAAAAAAAAAAAAAAAAAAAAA==", "subType": "04"}}], "bsonType": "string", "algorithm": "AEAD_AES_256_CBC_HMAC_SHA_512-Random"}}}}}}}, "kmsProviders": {"aws": {}}}}, "operations": [{"name": "insertOne", "arguments": {"document": {"_id": 1, "encrypted_string": "string0"}}, "result": {"errorContains": "Invalid schema"}}], "outcome": {"collection": {"data": []}}}, {"description": "<PERSON><PERSON><PERSON> with siblings of encrypt document", "clientOptions": {"autoEncryptOpts": {"schemaMap": {"default.default": {"properties": {"encrypted_string": {"encrypt": {"keyId": [{"$binary": {"base64": "AAAAAAAAAAAAAAAAAAAAAA==", "subType": "04"}}], "bsonType": "string", "algorithm": "AEAD_AES_256_CBC_HMAC_SHA_512-Random"}, "bsonType": "object"}}}}, "kmsProviders": {"aws": {}}}}, "operations": [{"name": "insertOne", "arguments": {"document": {"_id": 1, "encrypted_string": "string0"}}, "result": {"errorContains": "'encrypt' cannot be used in conjunction with 'bsonType'"}}], "outcome": {"collection": {"data": []}}}, {"description": "Schema with logical keywords", "clientOptions": {"autoEncryptOpts": {"schemaMap": {"default.default": {"anyOf": [{"properties": {"encrypted_string": {"encrypt": {"keyId": [{"$binary": {"base64": "AAAAAAAAAAAAAAAAAAAAAA==", "subType": "04"}}], "bsonType": "string", "algorithm": "AEAD_AES_256_CBC_HMAC_SHA_512-Random"}}}}]}}, "kmsProviders": {"aws": {}}}}, "operations": [{"name": "insertOne", "arguments": {"document": {"_id": 1, "encrypted_string": "string0"}}, "result": {"errorContains": "Invalid schema"}}], "outcome": {"collection": {"data": []}}}]}