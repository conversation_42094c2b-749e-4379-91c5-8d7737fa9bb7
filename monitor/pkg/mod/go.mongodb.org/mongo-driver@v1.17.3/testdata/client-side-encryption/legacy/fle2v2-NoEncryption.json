{"runOn": [{"minServerVersion": "7.0.0", "topology": ["replicaset", "sharded", "load-balanced"]}], "database_name": "default", "collection_name": "default", "data": [], "key_vault_data": [], "encrypted_fields": {"fields": []}, "tests": [{"description": "insert with no encryption succeeds", "clientOptions": {"autoEncryptOpts": {"kmsProviders": {"local": {"key": {"$binary": {"base64": "Mng0NCt4ZHVUYUJCa1kxNkVyNUR1QURhZ2h2UzR2d2RrZzh0cFBwM3R6NmdWMDFBMUN3YkQ5aXRRMkhGRGdQV09wOGVNYUMxT2k3NjZKelhaQmRCZGJkTXVyZG9uSjFk", "subType": "00"}}}}}}, "operations": [{"name": "insertOne", "arguments": {"document": {"_id": 1, "foo": "bar"}}}], "expectations": [{"command_started_event": {"command": {"listCollections": 1, "filter": {"name": "default"}}, "command_name": "listCollections"}}, {"command_started_event": {"command": {"insert": "default", "documents": [{"_id": 1, "foo": "bar"}], "ordered": true}, "command_name": "insert"}}], "outcome": {"collection": {"data": [{"_id": 1, "foo": "bar"}]}}}]}