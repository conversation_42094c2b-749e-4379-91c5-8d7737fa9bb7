runOn:
  - minServerVersion: "4.1.10"
database_name: &database_name "default"
collection_name: &collection_name "default"

data: [{_id: 1, encrypted_string: {'$binary': {'base64': 'AQAAAAAAAAAAAAAAAAAAAAACwj+3zkv2VM+aTfk60RqhXq6a/77WlLwu/BxXFkL7EppGsju/m8f0x5kBDD3EZTtGALGXlym5jnpZAoSIkswHoA==', 'subType': '06'}} }]
json_schema: {'properties': {'encrypted_w_altname': {'encrypt': {'keyId': '/altname', 'bsonType': 'string', 'algorithm': 'AEAD_AES_256_CBC_HMAC_SHA_512-Random'}}, 'encrypted_string': {'encrypt': {'keyId': [{'$binary': {'base64': 'AAAAAAAAAAAAAAAAAAAAAA==', 'subType': '04'}}], 'bsonType': 'string', 'algorithm': 'AEAD_AES_256_CBC_HMAC_SHA_512-Deterministic'}}, 'random': {'encrypt': {'keyId': [{'$binary': {'base64': 'AAAAAAAAAAAAAAAAAAAAAA==', 'subType': '04'}}], 'bsonType': 'string', 'algorithm': 'AEAD_AES_256_CBC_HMAC_SHA_512-Random'}}, 'encrypted_string_equivalent': {'encrypt': {'keyId': [{'$binary': {'base64': 'AAAAAAAAAAAAAAAAAAAAAA==', 'subType': '04'}}], 'bsonType': 'string', 'algorithm': 'AEAD_AES_256_CBC_HMAC_SHA_512-Deterministic'}}}, 'bsonType': 'object'}
key_vault_data: [{'status': 1, '_id': {'$binary': {'base64': 'AAAAAAAAAAAAAAAAAAAAAA==', 'subType': '04'}}, 'masterKey': {'provider': 'aws', 'key': 'arn:aws:kms:us-east-1:579766882180:key/89fcc2c4-08b0-4bd9-9f25-e30687b580d0', 'region': 'us-east-1'}, 'updateDate': {'$date': {'$numberLong': '1552949630483'}}, 'keyMaterial': {'$binary': {'base64': 'AQICAHhQNmWG2CzOm1dq3kWLM+iDUZhEqnhJwH9wZVpuZ94A8gEqnsxXlR51T5EbEVezUqqKAAAAwjCBvwYJKoZIhvcNAQcGoIGxMIGuAgEAMIGoBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDHa4jo6yp0Z18KgbUgIBEIB74sKxWtV8/YHje5lv5THTl0HIbhSwM6EqRlmBiFFatmEWaeMk4tO4xBX65eq670I5TWPSLMzpp8ncGHMmvHqRajNBnmFtbYxN3E3/WjxmdbOOe+OXpnGJPcGsftc7cB2shRfA4lICPnE26+oVNXT6p0Lo20nY5XC7jyCO', 'subType': '00'}}, 'creationDate': {'$date': {'$numberLong': '1552949630483'}}, 'keyAltNames': ['altname', 'another_altname']}]

tests:
  - description: "Insert with bypassAutoEncryption"
    clientOptions:
      autoEncryptOpts:
        bypassAutoEncryption: true
        kmsProviders:
          aws: {} # Credentials filled in from environment.
    operations:
      - name: insertOne
        arguments:
          document: { _id: 2, encrypted_string: "string0" }
          bypassDocumentValidation: true
      - name: find
        arguments:
          filter: { }
        result:
          - { _id: 1, encrypted_string: "string0" }
          - { _id: 2, encrypted_string: "string0" }
    expectations:
      - command_started_event:
          command:
            insert: *collection_name
            documents:
              # No encryption.
              - { _id: 2, encrypted_string: "string0" }
            ordered: true
          command_name: insert
      - command_started_event:
          command:
            find: *collection_name
            filter: { }
          command_name: find
      - command_started_event:
          command:
            find: datakeys
            filter: {"$or": [{"_id": {"$in": [ {'$binary': {'base64': 'AAAAAAAAAAAAAAAAAAAAAA==', 'subType': '04'}} ] }}, {"keyAltNames": {"$in": []}}]}
            $db: keyvault
            readConcern: { level: "majority" }
          command_name: find
    outcome:
      collection:
        # Outcome is checked using a separate MongoClient without auto encryption.
        data:
          - { _id: 1, encrypted_string: {'$binary': {'base64': 'AQAAAAAAAAAAAAAAAAAAAAACwj+3zkv2VM+aTfk60RqhXq6a/77WlLwu/BxXFkL7EppGsju/m8f0x5kBDD3EZTtGALGXlym5jnpZAoSIkswHoA==', 'subType': '06'}} }
          - { _id: 2, encrypted_string: "string0" }
  - description: "Insert with bypassAutoEncryption for local schema"
    clientOptions:
      autoEncryptOpts:
        schemaMap:
          "default.default": {'properties': {'encrypted_w_altname': {'encrypt': {'keyId': '/altname', 'bsonType': 'string', 'algorithm': 'AEAD_AES_256_CBC_HMAC_SHA_512-Random'}}, 'encrypted_string': {'encrypt': {'keyId': [{'$binary': {'base64': 'AAAAAAAAAAAAAAAAAAAAAA==', 'subType': '04'}}], 'bsonType': 'string', 'algorithm': 'AEAD_AES_256_CBC_HMAC_SHA_512-Deterministic'}}, 'random': {'encrypt': {'keyId': [{'$binary': {'base64': 'AAAAAAAAAAAAAAAAAAAAAA==', 'subType': '04'}}], 'bsonType': 'string', 'algorithm': 'AEAD_AES_256_CBC_HMAC_SHA_512-Random'}}, 'encrypted_string_equivalent': {'encrypt': {'keyId': [{'$binary': {'base64': 'AAAAAAAAAAAAAAAAAAAAAA==', 'subType': '04'}}], 'bsonType': 'string', 'algorithm': 'AEAD_AES_256_CBC_HMAC_SHA_512-Deterministic'}}}, 'bsonType': 'object'}
        bypassAutoEncryption: true
        kmsProviders:
          aws: {} # Credentials filled in from environment.
    operations:
      - name: insertOne
        arguments:
          document: { _id: 2, encrypted_string: "string0" }
          bypassDocumentValidation: true
      - name: find
        arguments:
          filter: { }
        result:
          - { _id: 1, encrypted_string: "string0" }
          - { _id: 2, encrypted_string: "string0" }
    expectations:
      - command_started_event:
          command:
            insert: *collection_name
            documents:
              # No encryption.
              - { _id: 2, encrypted_string: "string0" }
            ordered: true
          command_name: insert
      - command_started_event:
          command:
            find: *collection_name
            filter: { }
          command_name: find
      - command_started_event:
          command:
            find: datakeys
            filter: {"$or": [{"_id": {"$in": [ {'$binary': {'base64': 'AAAAAAAAAAAAAAAAAAAAAA==', 'subType': '04'}} ] }}, {"keyAltNames": {"$in": []}}]}
            $db: keyvault
            readConcern: { level: "majority" }
          command_name: find
    outcome:
      collection:
        # Outcome is checked using a separate MongoClient without auto encryption.
        data:
          - { _id: 1, encrypted_string: {'$binary': {'base64': 'AQAAAAAAAAAAAAAAAAAAAAACwj+3zkv2VM+aTfk60RqhXq6a/77WlLwu/BxXFkL7EppGsju/m8f0x5kBDD3EZTtGALGXlym5jnpZAoSIkswHoA==', 'subType': '06'}} }
          - { _id: 2, encrypted_string: "string0" }