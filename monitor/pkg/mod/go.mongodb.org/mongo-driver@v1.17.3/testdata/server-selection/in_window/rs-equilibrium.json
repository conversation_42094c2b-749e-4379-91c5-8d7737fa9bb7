{"description": "When in equilibrium selection is evenly distributed (replica set)", "topology_description": {"type": "ReplicaSetWithPrimary", "servers": [{"address": "a:27017", "avg_rtt_ms": 35, "type": "RSPrimary"}, {"address": "b:27017", "avg_rtt_ms": 35, "type": "RSSecondary"}, {"address": "c:27017", "avg_rtt_ms": 35, "type": "RSSecondary"}]}, "mocked_topology_state": [{"address": "a:27017", "operation_count": 6}, {"address": "b:27017", "operation_count": 6}, {"address": "c:27017", "operation_count": 6}], "iterations": 2000, "outcome": {"tolerance": 0.05, "expected_frequencies": {"a:27017": 0.33, "b:27017": 0.33, "c:27017": 0.33}}}