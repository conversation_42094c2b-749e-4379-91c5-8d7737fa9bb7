{"description": "snapshot-sessions-unsupported-ops", "schemaVersion": "1.0", "runOnRequirements": [{"minServerVersion": "5.0", "topologies": ["replicaset", "sharded-replicaset"]}], "createEntities": [{"client": {"id": "client0", "observeEvents": ["commandStartedEvent", "commandFailedEvent"]}}, {"database": {"id": "database0", "client": "client0", "databaseName": "database0"}}, {"collection": {"id": "collection0", "database": "database0", "collectionName": "collection0"}}, {"session": {"id": "session0", "client": "client0", "sessionOptions": {"snapshot": true}}}], "initialData": [{"collectionName": "collection0", "databaseName": "database0", "documents": [{"_id": 1, "x": 11}]}], "tests": [{"description": "Server returns an error on insertOne with snapshot", "runOnRequirements": [{"topologies": ["replicaset"]}], "operations": [{"name": "insertOne", "object": "collection0", "arguments": {"session": "session0", "document": {"_id": 22, "x": 22}}, "expectError": {"isError": true, "isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"insert": "collection0", "readConcern": {"level": "snapshot", "atClusterTime": {"$$exists": false}}}}}, {"commandFailedEvent": {"commandName": "insert"}}]}]}, {"description": "Server returns an error on insertMany with snapshot", "runOnRequirements": [{"topologies": ["replicaset"]}], "operations": [{"name": "insertMany", "object": "collection0", "arguments": {"session": "session0", "documents": [{"_id": 22, "x": 22}, {"_id": 33, "x": 33}]}, "expectError": {"isError": true, "isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"insert": "collection0", "readConcern": {"level": "snapshot", "atClusterTime": {"$$exists": false}}}}}, {"commandFailedEvent": {"commandName": "insert"}}]}]}, {"description": "Server returns an error on deleteOne with snapshot", "runOnRequirements": [{"topologies": ["replicaset"]}], "operations": [{"name": "deleteOne", "object": "collection0", "arguments": {"session": "session0", "filter": {}}, "expectError": {"isError": true, "isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"delete": "collection0", "readConcern": {"level": "snapshot", "atClusterTime": {"$$exists": false}}}}}, {"commandFailedEvent": {"commandName": "delete"}}]}]}, {"description": "Server returns an error on updateOne with snapshot", "runOnRequirements": [{"topologies": ["replicaset"]}], "operations": [{"name": "updateOne", "object": "collection0", "arguments": {"session": "session0", "filter": {"_id": 1}, "update": {"$inc": {"x": 1}}}, "expectError": {"isError": true, "isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"update": "collection0", "readConcern": {"level": "snapshot", "atClusterTime": {"$$exists": false}}}}}, {"commandFailedEvent": {"commandName": "update"}}]}]}, {"description": "Server returns an error on findOneAndUpdate with snapshot", "operations": [{"name": "findOneAndUpdate", "object": "collection0", "arguments": {"session": "session0", "filter": {"_id": 1}, "update": {"$inc": {"x": 1}}}, "expectError": {"isError": true, "isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"findAndModify": "collection0", "readConcern": {"level": "snapshot", "atClusterTime": {"$$exists": false}}}}}, {"commandFailedEvent": {"commandName": "findAndModify"}}]}]}, {"description": "Server returns an error on listDatabases with snapshot", "operations": [{"name": "listDatabases", "object": "client0", "arguments": {"session": "session0"}, "expectError": {"isError": true, "isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"listDatabases": 1, "readConcern": {"level": "snapshot", "atClusterTime": {"$$exists": false}}}}}, {"commandFailedEvent": {"commandName": "listDatabases"}}]}]}, {"description": "Server returns an error on listCollections with snapshot", "operations": [{"name": "listCollections", "object": "database0", "arguments": {"session": "session0"}, "expectError": {"isError": true, "isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"listCollections": 1, "readConcern": {"level": "snapshot", "atClusterTime": {"$$exists": false}}}}}, {"commandFailedEvent": {"commandName": "listCollections"}}]}]}, {"description": "Server returns an error on listIndexes with snapshot", "operations": [{"name": "listIndexes", "object": "collection0", "arguments": {"session": "session0"}, "expectError": {"isError": true, "isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"listIndexes": "collection0", "readConcern": {"level": "snapshot", "atClusterTime": {"$$exists": false}}}}}, {"commandFailedEvent": {"commandName": "listIndexes"}}]}]}, {"description": "Server returns an error on runCommand with snapshot", "operations": [{"name": "runCommand", "object": "database0", "arguments": {"session": "session0", "commandName": "listCollections", "command": {"listCollections": 1}}, "expectError": {"isError": true, "isClientError": false}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"listCollections": 1, "readConcern": {"level": "snapshot", "atClusterTime": {"$$exists": false}}}}}, {"commandFailedEvent": {"commandName": "listCollections"}}]}]}]}