tests:
    -
        description: "Unix domain socket (absolute path with trailing slash)"
        uri: "mongodb://%2Ftmp%2Fmongodb-27017.sock/"
        valid: true
        warning: false
        hosts:
            -
                type: "unix"
                host: "/tmp/mongodb-27017.sock"
                port: ~
        auth: ~
        options: ~
    -
        description: "Unix domain socket (absolute path without trailing slash)"
        uri: "mongodb://%2Ftmp%2Fmongodb-27017.sock"
        valid: true
        warning: false
        hosts:
            -
                type: "unix"
                host: "/tmp/mongodb-27017.sock"
                port: ~
        auth: ~
        options: ~
    -
        description: "Unix domain socket (absolute path with spaces in path)"
        uri: "mongodb://%2Ftmp%2F %2Fmongodb-27017.sock"
        valid: true
        warning: false
        hosts:
            -
                type: "unix"
                host: "/tmp/ /mongodb-27017.sock"
                port: ~
        auth: ~
        options: ~
    -
        description: "Multiple Unix domain sockets (absolute paths)"
        uri: "mongodb://%2Ftmp%2Fmongodb-27017.sock,%2Ftmp%2Fmongodb-27018.sock"
        valid: true
        warning: false
        hosts:
            -
                type: "unix"
                host: "/tmp/mongodb-27017.sock"
                port: ~
            -
                type: "unix"
                host: "/tmp/mongodb-27018.sock"
                port: ~
        auth: ~
        options: ~
    -
        description: "Multiple hosts (absolute path and ipv4)"
        uri: "mongodb://127.0.0.1:27017,%2Ftmp%2Fmongodb-27017.sock"
        valid: true
        warning: false
        hosts:
            -
                type: "ipv4"
                host: "127.0.0.1"
                port: 27017
            -
                type: "unix"
                host: "/tmp/mongodb-27017.sock"
                port: ~
        auth: ~
        options: ~
    -
        description: "Multiple hosts (absolute path and hostname resembling relative path)"
        uri: "mongodb://mongodb-27017.sock,%2Ftmp%2Fmongodb-27018.sock"
        valid: true
        warning: false
        hosts:
            -
                type: "hostname"
                host: "mongodb-27017.sock"
                port: ~
            -
                type: "unix"
                host: "/tmp/mongodb-27018.sock"
                port: ~
        auth: ~
        options: ~
    -
        description: "Unix domain socket with auth database (absolute path)"
        uri: "mongodb://alice:foo@%2Ftmp%2Fmongodb-27017.sock/admin"
        valid: true
        warning: false
        hosts:
            -
                type: "unix"
                host: "/tmp/mongodb-27017.sock"
                port: ~
        auth:
            username: "alice"
            password: "foo"
            db: "admin"
        options: ~
    -
        description: "Unix domain socket with path resembling socket file (absolute path with trailing slash)"
        uri: "mongodb://%2Ftmp%2Fpath.to.sock%2Fmongodb-27017.sock/"
        valid: true
        warning: false
        hosts:
            -
                type: "unix"
                host: "/tmp/path.to.sock/mongodb-27017.sock"
                port: ~
        auth: ~
        options: ~
    -
        description: "Unix domain socket with path resembling socket file (absolute path without trailing slash)"
        uri: "mongodb://%2Ftmp%2Fpath.to.sock%2Fmongodb-27017.sock"
        valid: true
        warning: false
        hosts:
            -
                type: "unix"
                host: "/tmp/path.to.sock/mongodb-27017.sock"
                port: ~
        auth: ~
        options: ~
    -
        description: "Unix domain socket with path resembling socket file and auth (absolute path)"
        uri: "mongodb://bob:bar@%2Ftmp%2Fpath.to.sock%2Fmongodb-27017.sock/admin"
        valid: true
        warning: false
        hosts:
            -
                type: "unix"
                host: "/tmp/path.to.sock/mongodb-27017.sock"
                port: ~
        auth:
            username: "bob"
            password: "bar"
            db: "admin"
        options: ~
    -
        description: "Multiple Unix domain sockets and auth DB (absolute path)"
        uri: "mongodb://%2Ftmp%2Fmongodb-27017.sock,%2Ftmp%2Fmongodb-27018.sock/admin"
        valid: true
        warning: false
        hosts:
            -
                type: "unix"
                host: "/tmp/mongodb-27017.sock"
                port: ~
            -
                type: "unix"
                host: "/tmp/mongodb-27018.sock"
                port: ~
        auth:
            username: ~
            password: ~
            db: "admin"
        options: ~
    -
        description: "Multiple Unix domain sockets with auth DB (absolute path)"
        uri: "mongodb://%2Ftmp%2Fmongodb-27017.sock,%2Ftmp%2Fmongodb-27018.sock/admin"
        valid: true
        warning: false
        hosts:
            -
                type: "unix"
                host: "/tmp/mongodb-27017.sock"
                port: ~
            -
                type: "unix"
                host: "/tmp/mongodb-27018.sock"
                port: ~
        auth:
            username: ~
            password: ~
            db: "admin"
        options: ~
    -
        description: "Multiple Unix domain sockets with auth and query string (absolute path)"
        uri: "mongodb://bob:bar@%2Ftmp%2Fmongodb-27017.sock,%2Ftmp%2Fmongodb-27018.sock/admin?w=1"
        valid: true
        warning: false
        hosts:
            -
                type: "unix"
                host: "/tmp/mongodb-27017.sock"
                port: ~
            -
                type: "unix"
                host: "/tmp/mongodb-27018.sock"
                port: ~
        auth:
            username: "bob"
            password: "bar"
            db: "admin"
        options:
            w: 1
