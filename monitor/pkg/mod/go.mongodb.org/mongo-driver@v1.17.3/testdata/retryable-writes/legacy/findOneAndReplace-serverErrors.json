{"runOn": [{"minServerVersion": "4.0", "topology": ["replicaset"]}, {"minServerVersion": "4.1.7", "topology": ["sharded", "load-balanced"]}], "data": [{"_id": 1, "x": 11}, {"_id": 2, "x": 22}], "tests": [{"description": "FindOneAndReplace succeeds after PrimarySteppedDown", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["findAndModify"], "errorCode": 189, "errorLabels": ["RetryableWriteError"]}}, "operation": {"name": "findOneAndReplace", "arguments": {"filter": {"_id": 1}, "replacement": {"_id": 1, "x": 111}, "returnDocument": "Before"}}, "outcome": {"result": {"_id": 1, "x": 11}, "collection": {"data": [{"_id": 1, "x": 111}, {"_id": 2, "x": 22}]}}}, {"description": "FindOneAndReplace succeeds after WriteConcernError ShutdownInProgress", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["findAndModify"], "writeConcernError": {"code": 91, "errmsg": "Replication is being shut down", "errorLabels": ["RetryableWriteError"]}}}, "operation": {"name": "findOneAndReplace", "arguments": {"filter": {"_id": 1}, "replacement": {"_id": 1, "x": 111}, "returnDocument": "Before"}}, "outcome": {"result": {"_id": 1, "x": 11}, "collection": {"data": [{"_id": 1, "x": 111}, {"_id": 2, "x": 22}]}}}, {"description": "FindOneAndReplace fails with a RetryableWriteError label after two connection failures", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 2}, "data": {"failCommands": ["findAndModify"], "closeConnection": true}}, "operation": {"name": "findOneAndReplace", "arguments": {"filter": {"_id": 1}, "replacement": {"_id": 1, "x": 111}, "returnDocument": "Before"}}, "outcome": {"error": true, "result": {"errorLabelsContain": ["RetryableWriteError"]}, "collection": {"data": [{"_id": 1, "x": 11}, {"_id": 2, "x": 22}]}}}]}