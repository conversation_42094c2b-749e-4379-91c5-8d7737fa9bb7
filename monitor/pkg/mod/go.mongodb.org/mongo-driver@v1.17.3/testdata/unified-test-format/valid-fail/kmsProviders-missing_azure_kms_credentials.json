{"description": "kmsProviders-missing_azure_kms_credentials", "schemaVersion": "1.8", "runOnRequirements": [{"csfle": true}], "createEntities": [{"client": {"id": "client0"}}, {"clientEncryption": {"id": "clientEncryption0", "clientEncryptionOpts": {"keyVaultClient": "client0", "keyVaultNamespace": "keyvault.datakeys", "kmsProviders": {"azure": {"tenantId": "tenantId"}}}}}], "tests": [{"description": "", "operations": []}]}