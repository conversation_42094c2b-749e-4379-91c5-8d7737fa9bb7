{"description": "poc-crud", "schemaVersion": "1.4", "createEntities": [{"client": {"id": "client0", "observeEvents": ["commandStartedEvent"]}}, {"database": {"id": "database0", "client": "client0", "databaseName": "crud-tests"}}, {"database": {"id": "database1", "client": "client0", "databaseName": "admin"}}, {"collection": {"id": "collection0", "database": "database0", "collectionName": "coll0"}}, {"collection": {"id": "collection1", "database": "database0", "collectionName": "coll1"}}, {"collection": {"id": "collection2", "database": "database0", "collectionName": "coll2", "collectionOptions": {"readConcern": {"level": "majority"}}}}], "initialData": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 11}, {"_id": 2, "x": 22}]}, {"collectionName": "coll1", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 11}]}, {"collectionName": "coll2", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 11}, {"_id": 2, "x": 22}, {"_id": 3, "x": 33}]}, {"collectionName": "aggregate_out", "databaseName": "crud-tests", "documents": []}], "tests": [{"description": "BulkWrite with mixed ordered operations", "operations": [{"name": "bulkWrite", "object": "collection0", "arguments": {"requests": [{"insertOne": {"document": {"_id": 3, "x": 33}}}, {"updateOne": {"filter": {"_id": 2}, "update": {"$inc": {"x": 1}}}}, {"updateMany": {"filter": {"_id": {"$gt": 1}}, "update": {"$inc": {"x": 1}}}}, {"insertOne": {"document": {"_id": 4, "x": 44}}}, {"deleteMany": {"filter": {"x": {"$nin": [24, 34]}}}}, {"replaceOne": {"filter": {"_id": 4}, "replacement": {"_id": 4, "x": 44}, "upsert": true}}], "ordered": true}, "expectResult": {"deletedCount": 2, "insertedCount": 2, "insertedIds": {"$$unsetOrMatches": {"0": 3, "3": 4}}, "matchedCount": 3, "modifiedCount": 3, "upsertedCount": 1, "upsertedIds": {"5": 4}}}], "outcome": [{"collectionName": "coll0", "databaseName": "crud-tests", "documents": [{"_id": 2, "x": 24}, {"_id": 3, "x": 34}, {"_id": 4, "x": 44}]}]}, {"description": "InsertMany continue-on-error behavior with unordered (duplicate key in requests)", "operations": [{"name": "insertMany", "object": "collection1", "arguments": {"documents": [{"_id": 2, "x": 22}, {"_id": 2, "x": 22}, {"_id": 3, "x": 33}], "ordered": false}, "expectError": {"expectResult": {"$$unsetOrMatches": {"deletedCount": 0, "insertedCount": 2, "matchedCount": 0, "modifiedCount": 0, "upsertedCount": 0, "upsertedIds": {}}}}}], "outcome": [{"collectionName": "coll1", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 11}, {"_id": 2, "x": 22}, {"_id": 3, "x": 33}]}]}, {"description": "ReplaceOne prohibits atomic modifiers", "operations": [{"name": "replaceOne", "object": "collection1", "arguments": {"filter": {"_id": 1}, "replacement": {"$set": {"x": 22}}}, "expectError": {"isClientError": true}}], "expectEvents": [{"client": "client0", "events": []}], "outcome": [{"collectionName": "coll1", "databaseName": "crud-tests", "documents": [{"_id": 1, "x": 11}]}]}, {"description": "readConcern majority with out stage", "runOnRequirements": [{"minServerVersion": "4.1.0", "topologies": ["replicaset", "sharded-replicaset"], "serverless": "forbid"}], "operations": [{"name": "aggregate", "object": "collection2", "arguments": {"pipeline": [{"$sort": {"x": 1}}, {"$match": {"_id": {"$gt": 1}}}, {"$out": "aggregate_out"}]}}], "expectEvents": [{"client": "client0", "events": [{"commandStartedEvent": {"command": {"aggregate": "coll2", "pipeline": [{"$sort": {"x": 1}}, {"$match": {"_id": {"$gt": 1}}}, {"$out": "aggregate_out"}], "readConcern": {"level": "majority"}}, "commandName": "aggregate", "databaseName": "crud-tests"}}]}], "outcome": [{"collectionName": "aggregate_out", "databaseName": "crud-tests", "documents": [{"_id": 2, "x": 22}, {"_id": 3, "x": 33}]}]}, {"description": "Aggregate with $listLocalSessions", "runOnRequirements": [{"minServerVersion": "3.6.0", "serverless": "forbid"}], "operations": [{"name": "aggregate", "object": "database1", "arguments": {"pipeline": [{"$listLocalSessions": {}}, {"$limit": 1}, {"$addFields": {"dummy": "dummy field"}}, {"$project": {"_id": 0, "dummy": 1}}]}, "expectResult": [{"dummy": "dummy field"}]}]}]}