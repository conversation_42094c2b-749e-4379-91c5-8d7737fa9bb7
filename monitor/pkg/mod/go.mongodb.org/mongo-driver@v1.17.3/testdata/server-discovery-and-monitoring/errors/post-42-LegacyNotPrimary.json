{"description": "Post-4.2 LegacyNotPrimary error", "uri": "mongodb://a/?replicaSet=rs", "phases": [{"description": "Primary A is discovered", "responses": [["a:27017", {"ok": 1, "helloOk": true, "isWritablePrimary": true, "hosts": ["a:27017"], "setName": "rs", "minWireVersion": 0, "maxWireVersion": 8}]], "outcome": {"servers": {"a:27017": {"type": "RSPrimary", "setName": "rs", "topologyVersion": null, "pool": {"generation": 0}}}, "topologyType": "ReplicaSetWithPrimary", "logicalSessionTimeoutMinutes": null, "setName": "rs"}}, {"description": "Post-4.2 LegacyNotPrimary error marks server Unknown", "applicationErrors": [{"address": "a:27017", "when": "afterHandshakeCompletes", "maxWireVersion": 8, "type": "command", "response": {"ok": 0, "errmsg": "LegacyNotPrimary", "code": 10058}}], "outcome": {"servers": {"a:27017": {"type": "Unknown", "topologyVersion": null, "pool": {"generation": 0}}}, "topologyType": "ReplicaSetNoPrimary", "logicalSessionTimeoutMinutes": null, "setName": "rs"}}]}