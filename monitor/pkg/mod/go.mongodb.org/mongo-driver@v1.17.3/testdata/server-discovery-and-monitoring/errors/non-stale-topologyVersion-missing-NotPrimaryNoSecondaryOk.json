{"description": "Non-stale topologyVersion missing NotPrimaryNoSecondaryOk error", "uri": "mongodb://a/?replicaSet=rs", "phases": [{"description": "Primary A is discovered", "responses": [["a:27017", {"ok": 1, "helloOk": true, "isWritablePrimary": true, "hosts": ["a:27017"], "setName": "rs", "minWireVersion": 0, "maxWireVersion": 9, "topologyVersion": {"processId": {"$oid": "000000000000000000000001"}, "counter": {"$numberLong": "1"}}}]], "outcome": {"servers": {"a:27017": {"type": "RSPrimary", "setName": "rs", "topologyVersion": {"processId": {"$oid": "000000000000000000000001"}, "counter": {"$numberLong": "1"}}, "pool": {"generation": 0}}}, "topologyType": "ReplicaSetWithPrimary", "logicalSessionTimeoutMinutes": null, "setName": "rs"}}, {"description": "Non-stale topologyVersion missing NotPrimaryNoSecondaryOk error marks server Unknown", "applicationErrors": [{"address": "a:27017", "when": "afterHandshakeCompletes", "maxWireVersion": 9, "type": "command", "response": {"ok": 0, "errmsg": "NotPrimaryNoSecondaryOk", "code": 13435}}], "outcome": {"servers": {"a:27017": {"type": "Unknown", "topologyVersion": null, "pool": {"generation": 0}}}, "topologyType": "ReplicaSetNoPrimary", "logicalSessionTimeoutMinutes": null, "setName": "rs"}}]}