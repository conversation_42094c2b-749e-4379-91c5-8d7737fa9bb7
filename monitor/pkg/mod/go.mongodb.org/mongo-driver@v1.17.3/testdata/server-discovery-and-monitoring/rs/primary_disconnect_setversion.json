{"description": "Disconnected from primary, reject primary with stale setVersion", "uri": "mongodb://a/?replicaSet=rs", "phases": [{"responses": [["a:27017", {"ok": 1, "helloOk": true, "isWritablePrimary": true, "hosts": ["a:27017", "b:27017"], "setName": "rs", "setVersion": 1, "electionId": {"$oid": "000000000000000000000001"}, "minWireVersion": 0, "maxWireVersion": 6}], ["b:27017", {"ok": 1, "helloOk": true, "isWritablePrimary": true, "hosts": ["a:27017", "b:27017"], "setName": "rs", "setVersion": 2, "electionId": {"$oid": "000000000000000000000001"}, "minWireVersion": 0, "maxWireVersion": 6}]], "outcome": {"servers": {"a:27017": {"type": "Unknown", "setName": null, "electionId": null}, "b:27017": {"type": "RSPrimary", "setName": "rs", "setVersion": 2, "electionId": {"$oid": "000000000000000000000001"}}}, "topologyType": "ReplicaSetWithPrimary", "logicalSessionTimeoutMinutes": null, "setName": "rs", "maxSetVersion": 2, "maxElectionId": {"$oid": "000000000000000000000001"}}}, {"responses": [["b:27017", {}]], "outcome": {"servers": {"a:27017": {"type": "Unknown", "setName": null, "electionId": null}, "b:27017": {"type": "Unknown", "setName": null, "electionId": null}}, "topologyType": "ReplicaSetNoPrimary", "logicalSessionTimeoutMinutes": null, "setName": "rs", "maxSetVersion": 2, "maxElectionId": {"$oid": "000000000000000000000001"}}}, {"responses": [["a:27017", {"ok": 1, "helloOk": true, "isWritablePrimary": true, "hosts": ["a:27017", "b:27017"], "setName": "rs", "setVersion": 1, "electionId": {"$oid": "000000000000000000000001"}, "minWireVersion": 0, "maxWireVersion": 6}]], "outcome": {"servers": {"a:27017": {"type": "Unknown", "setName": null, "electionId": null}, "b:27017": {"type": "Unknown", "setName": null, "electionId": null}}, "topologyType": "ReplicaSetNoPrimary", "logicalSessionTimeoutMinutes": null, "setName": "rs", "maxSetVersion": 2, "maxElectionId": {"$oid": "000000000000000000000001"}}}, {"responses": [["a:27017", {"ok": 1, "helloOk": true, "isWritablePrimary": true, "hosts": ["a:27017", "b:27017"], "setName": "rs", "setVersion": 2, "electionId": {"$oid": "000000000000000000000002"}, "minWireVersion": 0, "maxWireVersion": 6}]], "outcome": {"servers": {"a:27017": {"type": "RSPrimary", "setName": "rs", "setVersion": 2, "electionId": {"$oid": "000000000000000000000002"}}, "b:27017": {"type": "Unknown", "setName": null, "electionId": null}}, "topologyType": "ReplicaSetWithPrimary", "logicalSessionTimeoutMinutes": null, "setName": "rs", "maxSetVersion": 2, "maxElectionId": {"$oid": "000000000000000000000002"}}}, {"responses": [["b:27017", {"ok": 1, "helloOk": true, "isWritablePrimary": false, "secondary": true, "hosts": ["a:27017", "b:27017"], "setName": "rs", "minWireVersion": 0, "maxWireVersion": 6}]], "outcome": {"servers": {"a:27017": {"type": "RSPrimary", "setName": "rs", "setVersion": 2, "electionId": {"$oid": "000000000000000000000002"}}, "b:27017": {"type": "RSSecondary", "setName": "rs"}}, "topologyType": "ReplicaSetWithPrimary", "logicalSessionTimeoutMinutes": null, "setName": "rs", "maxSetVersion": 2, "maxElectionId": {"$oid": "000000000000000000000002"}}}]}