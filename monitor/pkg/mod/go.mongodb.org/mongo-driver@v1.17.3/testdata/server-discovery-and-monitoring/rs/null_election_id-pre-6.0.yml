description: "Pre 6.0 Primaries with and without electionIds"

uri: "mongodb://a/?replicaSet=rs"

phases: [

    # Primary A has no electionId.
    {
        responses: [
            ["a:27017", {
                ok: 1,
                helloOk: true,
                isWritablePrimary: true,
                hosts: ["a:27017", "b:27017", "c:27017"],
                setVersion: 1,
                setName: "rs",
                minWireVersion: 0,
                maxWireVersion: 6
            }]
        ],

        outcome: {
            servers: {
                "a:27017": {
                    type: "RSPrimary",
                    setName: "rs",
                    setVersion: 1,
                    electionId:
                },
                "b:27017": {
                    type: "Unknown",
                    setName: ,
                    electionId:
                },
                "c:27017": {
                    type: "Unknown",
                    setName: ,
                    electionId:
                }
            },
            topologyType: "ReplicaSetWithPrimary",
            logicalSessionTimeoutMinutes: null,
            setName: "rs",
            maxSetVersion: 1,
        }
    },

    # B is elected, it has an electionId.
    {
        responses: [
            ["b:27017", {
                ok: 1,
                helloOk: true,
                isWritablePrimary: true,
                hosts: ["a:27017", "b:27017", "c:27017"],
                setName: "rs",
                setVersion: 1,
                electionId: {"$oid": "000000000000000000000002"},
                minWireVersion: 0,
                maxWireVersion: 6
            }]
        ],

        outcome: {
            servers: {
                "a:27017": {
                    type: "Unknown",
                    setName: ,
                    electionId:
                },
                "b:27017": {
                    type: "RSPrimary",
                    setName: "rs",
                    setVersion: 1,
                    electionId: {"$oid": "000000000000000000000002"}
                },
                "c:27017": {
                    type: "Unknown",
                    setName: ,
                    electionId:
                }
            },
            topologyType: "ReplicaSetWithPrimary",
            logicalSessionTimeoutMinutes: null,
            setName: "rs",
            maxSetVersion: 1,
            maxElectionId: {"$oid": "000000000000000000000002"},
        }
    },

    # A still claims to be primary, no electionId, we have to trust it.
    {
        responses: [
            ["a:27017", {
                ok: 1,
                helloOk: true,
                isWritablePrimary: true,
                hosts: ["a:27017", "b:27017", "c:27017"],
                setVersion: 1,
                setName: "rs",
                minWireVersion: 0,
                maxWireVersion: 6
            }]
        ],
        outcome: {
            servers: {
                "a:27017": {
                    type: "RSPrimary",
                    setName: "rs",
                    setVersion: 1,
                    electionId:
                },
                "b:27017": {
                    type: "Unknown",
                    setName: ,
                    electionId:
                },
                "c:27017": {
                    type: "Unknown",
                    setName: ,
                    electionId:
                }
            },
            topologyType: "ReplicaSetWithPrimary",
            logicalSessionTimeoutMinutes: null,
            setName: "rs",
            maxSetVersion: 1,
            maxElectionId: {"$oid": "000000000000000000000002"},
        }
    },

    # But we remember B's electionId, so when we finally hear from C
    # claiming it is primary, we ignore it due to its outdated electionId
    {
        responses: [
            ["c:27017", {
                ok: 1,
                helloOk: true,
                isWritablePrimary: true,
                hosts: ["a:27017", "b:27017", "c:27017"],
                setName: "rs",
                setVersion: 1,
                electionId: {"$oid": "000000000000000000000001"},
                minWireVersion: 0,
                maxWireVersion: 6
            }]
        ],
        outcome: {
            servers: {
                # Still primary.
                "a:27017": {
                    type: "RSPrimary",
                    setName: "rs",
                    setVersion: 1,
                    electionId:
                },
                "b:27017": {
                    type: "Unknown",
                    setName: ,
                    electionId:
                },
                "c:27017": {
                    type: "Unknown",
                    setName: ,
                    electionId:
                }
            },
            topologyType: "ReplicaSetWithPrimary",
            logicalSessionTimeoutMinutes: null,
            setName: "rs",
            maxSetVersion: 1,
            maxElectionId: {"$oid": "000000000000000000000002"},
        }
    }
]
