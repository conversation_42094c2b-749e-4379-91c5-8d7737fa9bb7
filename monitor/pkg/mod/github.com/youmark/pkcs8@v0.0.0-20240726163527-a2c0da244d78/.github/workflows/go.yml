# This workflow will build a golang project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-go

name: Go


on: [push, pull_request]

jobs:

  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        go-version: [ '1.17', '1.18', '1.19', '1.20', '1.21', '1.22' ]

    steps:
    - uses: actions/checkout@v4

    - name: Setup Go ${{ matrix.go-version }}
      uses: actions/setup-go@v5
      with:
        go-version: ${{ matrix.go-version }}

    # You can test your matrix by printing the current Go version
    - name: Display Go version
      run: go version

    - name: Test
      run: go test -v ./...
