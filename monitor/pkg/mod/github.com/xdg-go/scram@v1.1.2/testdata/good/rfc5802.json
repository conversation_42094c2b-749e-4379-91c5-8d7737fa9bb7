{"label": "RFC 5802 example", "digest": "SHA-1", "user": "user", "pass": "pencil", "authID": "", "skipSASLprep": false, "salt64": "QSXCR+Q6sek8bf92", "iters": 4096, "clientNonce": "fyko+d2lbbFgONRv9qkxdawL", "serverNonce": "3rfcNHYJY1ZVvWVs7j", "valid": true, "steps": ["n,,n=user,r=fyko+d2lbbFgONRv9qkxdawL", "r=fyko+d2lbbFgONRv9qkxdawL3rfcNHYJY1ZVvWVs7j,s=QSXCR+Q6sek8bf92,i=4096", "c=biws,r=fyko+d2lbbFgONRv9qkxdawL3rfcNHYJY1ZVvWVs7j,p=v0X8v3Bz2T0CJGbJQyF0X+HI4Ts=", "v=rmF9pqV8S7suAoZWja4dJRkFsKQ=", ""]}