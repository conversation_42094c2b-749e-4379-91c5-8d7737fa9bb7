[TRACE] 2025-05-30 14:49:18.724 - [任务 5 - Copy] - Task initialization... 
[TRACE] 2025-05-30 14:49:18.724 - [任务 5 - Copy] - Start task milestones: 683954b667afe97eb5c39f24(任务 5 - Copy) 
[INFO ] 2025-05-30 14:49:18.819 - [任务 5 - Copy] - Loading table structure completed 
[TRACE] 2025-05-30 14:49:18.819 - [任务 5 - Copy] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-05-30 14:49:18.851 - [任务 5 - Copy] - The engine receives 任务 5 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-30 14:49:18.851 - [任务 5 - Copy] - Task started 
[TRACE] 2025-05-30 14:49:18.861 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] start preload schema,table counts: 1 
[TRACE] 2025-05-30 14:49:18.861 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] preload schema finished, cost 0 ms 
[TRACE] 2025-05-30 14:49:18.861 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] start preload schema,table counts: 1 
[INFO ] 2025-05-30 14:49:18.862 - [任务 5 - Copy][local_pg] - Enable partition table support for source database 
[TRACE] 2025-05-30 14:49:18.862 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] preload schema finished, cost 0 ms 
[INFO ] 2025-05-30 14:49:19.291 - [任务 5 - Copy][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-05-30 14:49:19.291 - [任务 5 - Copy][local_pg] - Source node "local_pg" read batch size: 500 
[TRACE] 2025-05-30 14:49:19.291 - [任务 5 - Copy][local_pg] - Source node "local_pg" event queue capacity: 1000 
[TRACE] 2025-05-30 14:49:19.292 - [任务 5 - Copy][local_pg] - Sync progress not exists, will run task as first time 
[TRACE] 2025-05-30 14:49:19.292 - [任务 5 - Copy][local_pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-05-30 14:49:19.310 - [任务 5 - Copy][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-05-30 14:49:19.322 - [任务 5 - Copy][local_pg] - new logical replication slot created, slotName:tapdata_cdc_d5b0bb07_1ddb_4a07_ba24_b46be112c215 
[INFO ] 2025-05-30 14:49:19.322 - [任务 5 - Copy][local_pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-30 14:49:19.369 - [任务 5 - Copy][local_pg] - Starting batch read from 1 tables 
[TRACE] 2025-05-30 14:49:19.369 - [任务 5 - Copy][local_pg] - Initial sync started 
[INFO ] 2025-05-30 14:49:19.369 - [任务 5 - Copy][local_pg] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-05-30 14:49:19.369 - [任务 5 - Copy][local_pg] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-05-30 14:49:19.371 - [任务 5 - Copy][local_pg] - Query snapshot row size completed: local_pg(a32c8b2d-1241-4169-96ee-5b3d00fc3593) 
[INFO ] 2025-05-30 14:49:19.488 - [任务 5 - Copy][sqlserver -ag1] - Sink connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-30 14:49:19.488 - [任务 5 - Copy][sqlserver -ag1] - Node(sqlserver -ag1) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-30 14:49:19.488 - [任务 5 - Copy][sqlserver -ag1] - Write batch size: 100, max wait ms per batch: 500 
[TRACE] 2025-05-30 14:49:19.500 - [任务 5 - Copy][sqlserver -ag1] - Sync progress not exists, will run task as first time 
[INFO ] 2025-05-30 14:49:19.501 - [任务 5 - Copy][sqlserver -ag1] - Apply table structure to target database 
[TRACE] 2025-05-30 14:49:19.501 - [任务 5 - Copy][sqlserver -ag1] - Sync progress not exists, will run task as first time 
[INFO ] 2025-05-30 14:50:26.417 - [任务 5 - Copy][local_pg] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-05-30 14:50:26.419 - [任务 5 - Copy][local_pg] - Initial sync completed 
[INFO ] 2025-05-30 14:50:26.419 - [任务 5 - Copy][local_pg] - Batch read completed. 
[TRACE] 2025-05-30 14:50:26.420 - [任务 5 - Copy][local_pg] - Incremental sync starting... 
[TRACE] 2025-05-30 14:50:26.420 - [任务 5 - Copy][local_pg] - Initial sync completed 
[TRACE] 2025-05-30 14:50:26.420 - [任务 5 - Copy][local_pg] - Starting stream read, table list: [SourceOfRegion], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-30 14:50:26.420 - [任务 5 - Copy][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-05-30 14:50:26.421 - [任务 5 - Copy][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-05-30 14:50:26.464 - [任务 5 - Copy][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_d5b0bb07_1ddb_4a07_ba24_b46be112c215 
[TRACE] 2025-05-30 14:50:26.464 - [任务 5 - Copy][local_pg] - Connector PostgreSQL incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-05-30 14:50:43.973 - [任务 5 - Copy][sqlserver -ag1] - Process after table "SourceOfRegion" initial sync finished, cost: 5 ms 
[INFO ] 2025-05-30 14:50:43.974 - [任务 5 - Copy][sqlserver -ag1] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-05-30 14:59:31.522 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] running status set to false 
[TRACE] 2025-05-30 14:59:31.853 - [任务 5 - Copy][local_pg] - Incremental sync completed 
[TRACE] 2025-05-30 14:59:31.861 - [任务 5 - Copy][local_pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_a32c8b2d-1241-4169-96ee-5b3d00fc3593_1748587759148 
[TRACE] 2025-05-30 14:59:31.862 - [任务 5 - Copy][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_a32c8b2d-1241-4169-96ee-5b3d00fc3593_1748587759148 
[TRACE] 2025-05-30 14:59:31.862 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] schema data cleaned 
[TRACE] 2025-05-30 14:59:31.862 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] monitor closed 
[TRACE] 2025-05-30 14:59:31.864 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] close complete, cost 439 ms 
[TRACE] 2025-05-30 14:59:31.865 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] running status set to false 
[TRACE] 2025-05-30 14:59:31.879 - [任务 5 - Copy][sqlserver -ag1] - PDK connector node stopped: HazelcastTargetPdkDataNode_40beebb0-7747-42de-8af9-7ca3981b2334_1748587759129 
[TRACE] 2025-05-30 14:59:31.880 - [任务 5 - Copy][sqlserver -ag1] - PDK connector node released: HazelcastTargetPdkDataNode_40beebb0-7747-42de-8af9-7ca3981b2334_1748587759129 
[TRACE] 2025-05-30 14:59:31.880 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] schema data cleaned 
[TRACE] 2025-05-30 14:59:31.880 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] monitor closed 
[TRACE] 2025-05-30 14:59:32.086 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] close complete, cost 16 ms 
[TRACE] 2025-05-30 14:59:38.601 - [任务 5 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-30 14:59:39.608 - [任务 5 - Copy] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@5157f8e3 
[TRACE] 2025-05-30 14:59:39.609 - [任务 5 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@436e6cec 
[TRACE] 2025-05-30 14:59:39.732 - [任务 5 - Copy] - Stop task milestones: 683954b667afe97eb5c39f24(任务 5 - Copy)  
[TRACE] 2025-05-30 14:59:39.732 - [任务 5 - Copy] - Stopped task aspect(s) 
[TRACE] 2025-05-30 14:59:39.733 - [任务 5 - Copy] - Snapshot order controller have been removed 
[INFO ] 2025-05-30 14:59:39.733 - [任务 5 - Copy] - Task stopped. 
[TRACE] 2025-05-30 14:59:39.770 - [任务 5 - Copy] - Remove memory task client succeed, task: 任务 5 - Copy[683954b667afe97eb5c39f24] 
[TRACE] 2025-05-30 14:59:39.771 - [任务 5 - Copy] - Destroy memory task client cache succeed, task: 任务 5 - Copy[683954b667afe97eb5c39f24] 
[TRACE] 2025-05-30 15:10:54.091 - [任务 5 - Copy] - Task initialization... 
[TRACE] 2025-05-30 15:10:54.092 - [任务 5 - Copy] - Start task milestones: 683954b667afe97eb5c39f24(任务 5 - Copy) 
[INFO ] 2025-05-30 15:10:54.194 - [任务 5 - Copy] - Loading table structure completed 
[TRACE] 2025-05-30 15:10:54.194 - [任务 5 - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-30 15:10:54.228 - [任务 5 - Copy] - The engine receives 任务 5 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-30 15:10:54.228 - [任务 5 - Copy] - Task started 
[TRACE] 2025-05-30 15:10:54.253 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] start preload schema,table counts: 1 
[TRACE] 2025-05-30 15:10:54.253 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] preload schema finished, cost 0 ms 
[TRACE] 2025-05-30 15:10:54.253 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] start preload schema,table counts: 1 
[INFO ] 2025-05-30 15:10:54.254 - [任务 5 - Copy][local_pg] - Enable partition table support for source database 
[TRACE] 2025-05-30 15:10:54.254 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] preload schema finished, cost 0 ms 
[INFO ] 2025-05-30 15:10:54.400 - [任务 5 - Copy][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-05-30 15:10:54.400 - [任务 5 - Copy][local_pg] - Source node "local_pg" read batch size: 500 
[TRACE] 2025-05-30 15:10:54.400 - [任务 5 - Copy][local_pg] - Source node "local_pg" event queue capacity: 1000 
[INFO ] 2025-05-30 15:10:54.400 - [任务 5 - Copy][local_pg] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-05-30 15:10:54.401 - [任务 5 - Copy][local_pg] - Use existing batch read offset: {"SourceOfRegion":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-30 15:10:54.444 - [任务 5 - Copy][local_pg] - Batch read completed. 
[TRACE] 2025-05-30 15:10:54.444 - [任务 5 - Copy][local_pg] - Incremental sync starting... 
[TRACE] 2025-05-30 15:10:54.444 - [任务 5 - Copy][local_pg] - Initial sync completed 
[TRACE] 2025-05-30 15:10:54.444 - [任务 5 - Copy][local_pg] - Starting stream read, table list: [SourceOfRegion], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-30 15:10:54.444 - [任务 5 - Copy][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-05-30 15:10:54.444 - [任务 5 - Copy][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-05-30 15:10:54.448 - [任务 5 - Copy][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_d5b0bb07_1ddb_4a07_ba24_b46be112c215 
[TRACE] 2025-05-30 15:10:54.648 - [任务 5 - Copy][local_pg] - Connector PostgreSQL incremental start succeed, tables: [SourceOfRegion], data change syncing 
[INFO ] 2025-05-30 15:10:54.648 - [任务 5 - Copy][sqlserver -ag1] - Sink connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-30 15:10:54.649 - [任务 5 - Copy][sqlserver -ag1] - Node(sqlserver -ag1) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-30 15:10:54.660 - [任务 5 - Copy][sqlserver -ag1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-30 15:10:54.661 - [任务 5 - Copy][sqlserver -ag1] - Apply table structure to target database 
[TRACE] 2025-05-30 15:58:07.506 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] running status set to false 
[TRACE] 2025-05-30 15:58:07.513 - [任务 5 - Copy][local_pg] - Incremental sync completed 
[WARN ] 2025-05-30 15:58:07.513 - [任务 5 - Copy][local_pg] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode_a32c8b2d-1241-4169-96ee-5b3d00fc3593_1748589054275 
[TRACE] 2025-05-30 15:58:07.513 - [任务 5 - Copy][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_a32c8b2d-1241-4169-96ee-5b3d00fc3593_1748589054275 
[TRACE] 2025-05-30 15:58:29.216 - [任务 5 - Copy] - Task initialization... 
[TRACE] 2025-05-30 15:58:29.422 - [任务 5 - Copy] - Start task milestones: 683954b667afe97eb5c39f24(任务 5 - Copy) 
[INFO ] 2025-05-30 15:58:29.479 - [任务 5 - Copy] - Loading table structure completed 
[TRACE] 2025-05-30 15:58:29.850 - [任务 5 - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-30 15:58:29.966 - [任务 5 - Copy] - The engine receives 任务 5 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-30 15:58:29.966 - [任务 5 - Copy] - Task started 
[TRACE] 2025-05-30 15:58:30.033 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] start preload schema,table counts: 1 
[TRACE] 2025-05-30 15:58:30.033 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] start preload schema,table counts: 1 
[TRACE] 2025-05-30 15:58:30.034 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] preload schema finished, cost 0 ms 
[TRACE] 2025-05-30 15:58:30.034 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] preload schema finished, cost 0 ms 
[INFO ] 2025-05-30 15:58:30.035 - [任务 5 - Copy][local_pg] - Enable partition table support for source database 
[INFO ] 2025-05-30 15:58:30.411 - [任务 5 - Copy][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-05-30 15:58:30.412 - [任务 5 - Copy][local_pg] - Source node "local_pg" read batch size: 500 
[TRACE] 2025-05-30 15:58:30.412 - [任务 5 - Copy][local_pg] - Source node "local_pg" event queue capacity: 1000 
[INFO ] 2025-05-30 15:58:30.417 - [任务 5 - Copy][local_pg] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-05-30 15:58:30.417 - [任务 5 - Copy][local_pg] - Use existing batch read offset: {"SourceOfRegion":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-30 15:58:30.467 - [任务 5 - Copy][local_pg] - Batch read completed. 
[TRACE] 2025-05-30 15:58:30.467 - [任务 5 - Copy][local_pg] - Incremental sync starting... 
[TRACE] 2025-05-30 15:58:30.468 - [任务 5 - Copy][local_pg] - Initial sync completed 
[TRACE] 2025-05-30 15:58:30.470 - [任务 5 - Copy][local_pg] - Starting stream read, table list: [SourceOfRegion], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-30 15:58:30.470 - [任务 5 - Copy][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-05-30 15:58:30.474 - [任务 5 - Copy][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-05-30 15:58:30.640 - [任务 5 - Copy][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_d5b0bb07_1ddb_4a07_ba24_b46be112c215 
[TRACE] 2025-05-30 15:58:30.640 - [任务 5 - Copy][local_pg] - Connector PostgreSQL incremental start succeed, tables: [SourceOfRegion], data change syncing 
[INFO ] 2025-05-30 15:58:30.807 - [任务 5 - Copy][sqlserver -ag1] - Sink connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-30 15:58:30.807 - [任务 5 - Copy][sqlserver -ag1] - Node(sqlserver -ag1) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-30 15:58:30.808 - [任务 5 - Copy][sqlserver -ag1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-30 15:58:31.009 - [任务 5 - Copy][sqlserver -ag1] - Apply table structure to target database 
[TRACE] 2025-05-30 16:01:30.992 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] running status set to false 
[TRACE] 2025-05-30 16:01:31.349 - [任务 5 - Copy][local_pg] - Incremental sync completed 
[WARN ] 2025-05-30 16:01:31.355 - [任务 5 - Copy][local_pg] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode_a32c8b2d-1241-4169-96ee-5b3d00fc3593_1748591910057 
[TRACE] 2025-05-30 16:01:31.355 - [任务 5 - Copy][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_a32c8b2d-1241-4169-96ee-5b3d00fc3593_1748591910057 
[TRACE] 2025-05-30 16:01:31.356 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] schema data cleaned 
[TRACE] 2025-05-30 16:01:31.356 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] monitor closed 
[TRACE] 2025-05-30 16:01:31.360 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] close complete, cost 379 ms 
[TRACE] 2025-05-30 16:01:31.360 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] running status set to false 
[WARN ] 2025-05-30 16:01:31.364 - [任务 5 - Copy][sqlserver -ag1] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode_40beebb0-7747-42de-8af9-7ca3981b2334_1748591910060 
[TRACE] 2025-05-30 16:01:31.364 - [任务 5 - Copy][sqlserver -ag1] - PDK connector node released: HazelcastTargetPdkDataNode_40beebb0-7747-42de-8af9-7ca3981b2334_1748591910060 
[TRACE] 2025-05-30 16:01:31.364 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] schema data cleaned 
[TRACE] 2025-05-30 16:01:31.365 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] monitor closed 
[TRACE] 2025-05-30 16:01:31.570 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] close complete, cost 5 ms 
[TRACE] 2025-05-30 16:02:07.385 - [任务 5 - Copy] - Task initialization... 
[TRACE] 2025-05-30 16:02:07.386 - [任务 5 - Copy] - Start task milestones: 683954b667afe97eb5c39f24(任务 5 - Copy) 
[INFO ] 2025-05-30 16:02:07.697 - [任务 5 - Copy] - Loading table structure completed 
[TRACE] 2025-05-30 16:02:07.697 - [任务 5 - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-30 16:02:07.817 - [任务 5 - Copy] - The engine receives 任务 5 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-30 16:02:07.817 - [任务 5 - Copy] - Task started 
[TRACE] 2025-05-30 16:02:07.884 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] start preload schema,table counts: 1 
[TRACE] 2025-05-30 16:02:07.885 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] start preload schema,table counts: 1 
[TRACE] 2025-05-30 16:02:07.885 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] preload schema finished, cost 0 ms 
[TRACE] 2025-05-30 16:02:07.885 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] preload schema finished, cost 0 ms 
[INFO ] 2025-05-30 16:02:07.887 - [任务 5 - Copy][local_pg] - Enable partition table support for source database 
[INFO ] 2025-05-30 16:02:08.063 - [任务 5 - Copy][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-05-30 16:02:08.063 - [任务 5 - Copy][local_pg] - Source node "local_pg" read batch size: 500 
[TRACE] 2025-05-30 16:02:08.065 - [任务 5 - Copy][local_pg] - Source node "local_pg" event queue capacity: 1000 
[INFO ] 2025-05-30 16:02:08.065 - [任务 5 - Copy][local_pg] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-05-30 16:02:08.136 - [任务 5 - Copy][local_pg] - Use existing batch read offset: {"SourceOfRegion":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-30 16:02:08.136 - [任务 5 - Copy][local_pg] - Batch read completed. 
[TRACE] 2025-05-30 16:02:08.140 - [任务 5 - Copy][local_pg] - Incremental sync starting... 
[TRACE] 2025-05-30 16:02:08.140 - [任务 5 - Copy][local_pg] - Initial sync completed 
[TRACE] 2025-05-30 16:02:08.143 - [任务 5 - Copy][local_pg] - Starting stream read, table list: [SourceOfRegion], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-30 16:02:08.143 - [任务 5 - Copy][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-05-30 16:02:08.149 - [任务 5 - Copy][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-05-30 16:02:08.149 - [任务 5 - Copy][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_d5b0bb07_1ddb_4a07_ba24_b46be112c215 
[TRACE] 2025-05-30 16:02:08.346 - [任务 5 - Copy][local_pg] - Connector PostgreSQL incremental start succeed, tables: [SourceOfRegion], data change syncing 
[INFO ] 2025-05-30 16:02:08.346 - [任务 5 - Copy][sqlserver -ag1] - Sink connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-30 16:02:08.346 - [任务 5 - Copy][sqlserver -ag1] - Node(sqlserver -ag1) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-30 16:02:08.347 - [任务 5 - Copy][sqlserver -ag1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-30 16:02:08.364 - [任务 5 - Copy][sqlserver -ag1] - Apply table structure to target database 
[TRACE] 2025-05-30 16:07:27.469 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] running status set to false 
[TRACE] 2025-05-30 16:21:44.433 - [任务 5 - Copy] - Task initialization... 
[TRACE] 2025-05-30 16:21:44.434 - [任务 5 - Copy] - Start task milestones: 683954b667afe97eb5c39f24(任务 5 - Copy) 
[INFO ] 2025-05-30 16:21:44.582 - [任务 5 - Copy] - Loading table structure completed 
[TRACE] 2025-05-30 16:21:44.583 - [任务 5 - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-30 16:21:44.625 - [任务 5 - Copy] - The engine receives 任务 5 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-30 16:21:44.625 - [任务 5 - Copy] - Task started 
[TRACE] 2025-05-30 16:21:44.640 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] start preload schema,table counts: 1 
[TRACE] 2025-05-30 16:21:44.643 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] preload schema finished, cost 0 ms 
[TRACE] 2025-05-30 16:21:44.643 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] start preload schema,table counts: 1 
[TRACE] 2025-05-30 16:21:44.643 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] preload schema finished, cost 0 ms 
[INFO ] 2025-05-30 16:21:44.643 - [任务 5 - Copy][local_pg] - Enable partition table support for source database 
[INFO ] 2025-05-30 16:21:44.807 - [任务 5 - Copy][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-05-30 16:21:44.807 - [任务 5 - Copy][local_pg] - Source node "local_pg" read batch size: 500 
[TRACE] 2025-05-30 16:21:44.807 - [任务 5 - Copy][local_pg] - Source node "local_pg" event queue capacity: 1000 
[INFO ] 2025-05-30 16:21:44.808 - [任务 5 - Copy][local_pg] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-05-30 16:21:44.808 - [任务 5 - Copy][local_pg] - Use existing batch read offset: {"SourceOfRegion":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-30 16:21:44.854 - [任务 5 - Copy][local_pg] - Batch read completed. 
[TRACE] 2025-05-30 16:21:44.854 - [任务 5 - Copy][local_pg] - Incremental sync starting... 
[TRACE] 2025-05-30 16:21:44.854 - [任务 5 - Copy][local_pg] - Initial sync completed 
[TRACE] 2025-05-30 16:21:44.855 - [任务 5 - Copy][local_pg] - Starting stream read, table list: [SourceOfRegion], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-30 16:21:44.855 - [任务 5 - Copy][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-05-30 16:21:44.857 - [任务 5 - Copy][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-05-30 16:21:45.050 - [任务 5 - Copy][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_d5b0bb07_1ddb_4a07_ba24_b46be112c215 
[TRACE] 2025-05-30 16:21:45.051 - [任务 5 - Copy][local_pg] - Connector PostgreSQL incremental start succeed, tables: [SourceOfRegion], data change syncing 
[INFO ] 2025-05-30 16:21:45.113 - [任务 5 - Copy][sqlserver -ag1] - Sink connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-30 16:21:45.114 - [任务 5 - Copy][sqlserver -ag1] - Node(sqlserver -ag1) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-30 16:21:45.114 - [任务 5 - Copy][sqlserver -ag1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-30 16:21:45.320 - [任务 5 - Copy][sqlserver -ag1] - Apply table structure to target database 
[TRACE] 2025-05-30 16:24:49.068 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] running status set to false 
[TRACE] 2025-05-30 16:24:49.460 - [任务 5 - Copy][local_pg] - Incremental sync completed 
[TRACE] 2025-05-30 16:24:49.461 - [任务 5 - Copy][local_pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_a32c8b2d-1241-4169-96ee-5b3d00fc3593_1748593304672 
[TRACE] 2025-05-30 16:24:49.461 - [任务 5 - Copy][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_a32c8b2d-1241-4169-96ee-5b3d00fc3593_1748593304672 
[TRACE] 2025-05-30 16:24:49.462 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] schema data cleaned 
[TRACE] 2025-05-30 16:24:49.462 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] monitor closed 
[TRACE] 2025-05-30 16:24:49.463 - [任务 5 - Copy][local_pg] - Node local_pg[a32c8b2d-1241-4169-96ee-5b3d00fc3593] close complete, cost 398 ms 
[TRACE] 2025-05-30 16:24:49.464 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] running status set to false 
[TRACE] 2025-05-30 16:24:49.468 - [任务 5 - Copy][sqlserver -ag1] - PDK connector node stopped: HazelcastTargetPdkDataNode_40beebb0-7747-42de-8af9-7ca3981b2334_1748593304660 
[TRACE] 2025-05-30 16:24:49.468 - [任务 5 - Copy][sqlserver -ag1] - PDK connector node released: HazelcastTargetPdkDataNode_40beebb0-7747-42de-8af9-7ca3981b2334_1748593304660 
[TRACE] 2025-05-30 16:24:49.469 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] schema data cleaned 
[TRACE] 2025-05-30 16:24:49.469 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] monitor closed 
[TRACE] 2025-05-30 16:24:49.674 - [任务 5 - Copy][sqlserver -ag1] - Node sqlserver -ag1[40beebb0-7747-42de-8af9-7ca3981b2334] close complete, cost 6 ms 
[TRACE] 2025-05-30 16:24:58.403 - [任务 5 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-30 16:24:59.289 - [任务 5 - Copy] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@4bb0f1bc 
[TRACE] 2025-05-30 16:24:59.289 - [任务 5 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1aaf9311 
[TRACE] 2025-05-30 16:24:59.427 - [任务 5 - Copy] - Stop task milestones: 683954b667afe97eb5c39f24(任务 5 - Copy)  
[TRACE] 2025-05-30 16:24:59.428 - [任务 5 - Copy] - Stopped task aspect(s) 
[TRACE] 2025-05-30 16:24:59.428 - [任务 5 - Copy] - Snapshot order controller have been removed 
[INFO ] 2025-05-30 16:24:59.429 - [任务 5 - Copy] - Task stopped. 
[TRACE] 2025-05-30 16:24:59.470 - [任务 5 - Copy] - Remove memory task client succeed, task: 任务 5 - Copy[683954b667afe97eb5c39f24] 
[TRACE] 2025-05-30 16:24:59.470 - [任务 5 - Copy] - Destroy memory task client cache succeed, task: 任务 5 - Copy[683954b667afe97eb5c39f24] 
