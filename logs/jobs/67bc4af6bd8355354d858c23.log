[TRACE] 2025-02-25 10:18:02.499 - [任务 23] - Start task milestones: 67bc4af6bd8355354d858c23(任务 23) 
[TRACE] 2025-02-25 10:18:02.504 - [任务 23] - Task initialization... 
[INFO ] 2025-02-25 10:18:02.581 - [任务 23] - Loading table structure completed 
[TRACE] 2025-02-25 10:18:02.582 - [任务 23] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-02-25 10:18:02.617 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-25 10:18:02.618 - [任务 23] - Task started 
[TRACE] 2025-02-25 10:18:02.629 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] start preload schema,table counts: 1 
[TRACE] 2025-02-25 10:18:02.629 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] start preload schema,table counts: 1 
[TRACE] 2025-02-25 10:18:02.629 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] preload schema finished, cost 0 ms 
[TRACE] 2025-02-25 10:18:02.833 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] preload schema finished, cost 0 ms 
[INFO ] 2025-02-25 10:18:03.162 - [任务 23][IV_REGN] - Source connector(IV_REGN) initialization completed 
[TRACE] 2025-02-25 10:18:03.162 - [任务 23][IV_REGN] - Source node "IV_REGN" read batch size: 100 
[TRACE] 2025-02-25 10:18:03.162 - [任务 23][IV_REGN] - Source node "IV_REGN" event queue capacity: 200 
[TRACE] 2025-02-25 10:18:03.163 - [任务 23][IV_REGN] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-25 10:18:03.261 - [任务 23][NEW_IV] - Sink connector(NEW_IV) initialization completed 
[TRACE] 2025-02-25 10:18:03.261 - [任务 23][NEW_IV] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-25 10:18:03.316 - [任务 23][NEW_IV] - Apply table structure to target database 
[INFO ] 2025-02-25 10:18:03.316 - [任务 23][IV_REGN] - Use existing stream offset: {"sortString":null,"offsetValue":null,"lastScn":739032042,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[TRACE] 2025-02-25 10:18:03.316 - [任务 23][IV_REGN] - Before the event is output to the target from source, it will automatically block field changes 
[TRACE] 2025-02-25 10:18:03.367 - [任务 23][NEW_IV] - The table NEW_IV has already exist. 
[INFO ] 2025-02-25 10:18:03.374 - [任务 23][IV_REGN] - Starting batch read from 1 tables 
[TRACE] 2025-02-25 10:18:03.374 - [任务 23][IV_REGN] - Initial sync started 
[INFO ] 2025-02-25 10:18:03.376 - [任务 23][IV_REGN] - Starting batch read from table: IV_REGN 
[TRACE] 2025-02-25 10:18:03.376 - [任务 23][IV_REGN] - Table IV_REGN is going to be initial synced 
[TRACE] 2025-02-25 10:18:47.777 - [任务 23][IV_REGN] - Query snapshot row size completed: IV_REGN(9ec213ec-8cad-40d3-8378-13f95719ba31) 
[TRACE] 2025-02-25 10:18:47.953 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-02-25 10:18:48.019 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-02-25 10:18:48.426 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-02-25 10:18:48.587 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-02-25 10:18:48.693 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-02-25 10:18:48.693 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-02-25 10:18:48.800 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-02-25 10:18:48.800 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-02-25 10:18:48.943 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-02-25 10:18:48.943 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-02-25 10:18:48.995 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-02-25 10:18:48.995 - [任务 23][NEW_IV] - Table 'NEW_IV' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-02-25 10:19:01.317 - [任务 23][IV_REGN] - Table IV_REGN has been completed batch read 
[TRACE] 2025-02-25 10:19:01.318 - [任务 23][IV_REGN] - Initial sync completed 
[INFO ] 2025-02-25 10:19:01.318 - [任务 23][IV_REGN] - Batch read completed. 
[TRACE] 2025-02-25 10:19:01.318 - [任务 23][IV_REGN] - Incremental sync starting... 
[TRACE] 2025-02-25 10:19:01.318 - [任务 23][IV_REGN] - Initial sync completed 
[TRACE] 2025-02-25 10:19:01.318 - [任务 23][IV_REGN] - Starting stream read, table list: [IV_REGN], offset: {"sortString":null,"offsetValue":null,"lastScn":739032042,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2025-02-25 10:19:01.318 - [任务 23][IV_REGN] - Starting incremental sync using database log parser 
[INFO ] 2025-02-25 10:19:01.400 - [任务 23][IV_REGN] - Checking whether archived log exists... 
[INFO ] 2025-02-25 10:19:01.535 - [任务 23][IV_REGN] - building new log file... 
[INFO ] 2025-02-25 10:19:05.361 - [任务 23][IV_REGN] - Redo Log Miner is starting... 
[INFO ] 2025-02-25 10:19:58.560 - [任务 23][IV_REGN] - Redo Log Miner has been started... 
[TRACE] 2025-02-25 10:19:58.560 - [任务 23][IV_REGN] - Connector Oracle incremental start succeed, tables: [IV_REGN], data change syncing 
[TRACE] 2025-02-25 10:20:28.540 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] running status set to false 
[INFO ] 2025-02-25 10:20:28.540 - [任务 23][IV_REGN] - Log Miner is shutting down... 
[INFO ] 2025-02-25 10:20:28.543 - [任务 23][IV_REGN] - Auto redo oracle log miner result set closed 
[TRACE] 2025-02-25 10:20:28.543 - [任务 23][IV_REGN] - Incremental sync completed 
[INFO ] 2025-02-25 10:20:28.749 - [任务 23][IV_REGN] - Log Miner has been closed! 
[TRACE] 2025-02-25 10:20:28.795 - [任务 23][IV_REGN] - PDK connector node stopped: HazelcastSourcePdkDataNode_9ec213ec-8cad-40d3-8378-13f95719ba31_1740449882871 
[TRACE] 2025-02-25 10:20:28.795 - [任务 23][IV_REGN] - PDK connector node released: HazelcastSourcePdkDataNode_9ec213ec-8cad-40d3-8378-13f95719ba31_1740449882871 
[TRACE] 2025-02-25 10:20:28.795 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] schema data cleaned 
[TRACE] 2025-02-25 10:20:28.795 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] monitor closed 
[TRACE] 2025-02-25 10:20:28.795 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] close complete, cost 259 ms 
[TRACE] 2025-02-25 10:20:28.796 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] running status set to false 
[TRACE] 2025-02-25 10:20:28.834 - [任务 23][NEW_IV] - PDK connector node stopped: HazelcastTargetPdkDataNode_f52e0962-c4f7-4457-be9c-16d0e13f84c6_1740449882884 
[TRACE] 2025-02-25 10:20:28.834 - [任务 23][NEW_IV] - PDK connector node released: HazelcastTargetPdkDataNode_f52e0962-c4f7-4457-be9c-16d0e13f84c6_1740449882884 
[TRACE] 2025-02-25 10:20:28.834 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] schema data cleaned 
[TRACE] 2025-02-25 10:20:28.834 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] monitor closed 
[TRACE] 2025-02-25 10:20:28.835 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] close complete, cost 38 ms 
[TRACE] 2025-02-25 10:20:32.637 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-25 10:20:32.642 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3c75ecaa 
[TRACE] 2025-02-25 10:20:32.642 - [任务 23] - Stop task milestones: 67bc4af6bd8355354d858c23(任务 23)  
[TRACE] 2025-02-25 10:20:32.766 - [任务 23] - Stopped task aspect(s) 
[TRACE] 2025-02-25 10:20:32.766 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2025-02-25 10:20:32.766 - [任务 23] - Task stopped. 
[TRACE] 2025-02-25 10:20:32.818 - [任务 23] - Remove memory task client succeed, task: 任务 23[67bc4af6bd8355354d858c23] 
[TRACE] 2025-02-25 10:20:32.819 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[67bc4af6bd8355354d858c23] 
[TRACE] 2025-02-25 11:03:04.902 - [任务 23] - Start task milestones: 67bc4af6bd8355354d858c23(任务 23) 
[TRACE] 2025-02-25 11:03:04.903 - [任务 23] - Task initialization... 
[INFO ] 2025-02-25 11:03:05.124 - [任务 23] - Loading table structure completed 
[TRACE] 2025-02-25 11:03:05.182 - [任务 23] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-25 11:03:05.182 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-25 11:03:05.344 - [任务 23] - Task started 
[TRACE] 2025-02-25 11:03:05.344 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] start preload schema,table counts: 1 
[TRACE] 2025-02-25 11:03:05.344 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] start preload schema,table counts: 1 
[TRACE] 2025-02-25 11:03:05.344 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] preload schema finished, cost 0 ms 
[TRACE] 2025-02-25 11:03:05.345 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] preload schema finished, cost 0 ms 
[INFO ] 2025-02-25 11:03:05.897 - [任务 23][NEW_IV] - Sink connector(NEW_IV) initialization completed 
[TRACE] 2025-02-25 11:03:05.899 - [任务 23][NEW_IV] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-25 11:03:06.015 - [任务 23][NEW_IV] - Apply table structure to target database 
[INFO ] 2025-02-25 11:03:06.015 - [任务 23][IV_REGN] - Source connector(IV_REGN) initialization completed 
[TRACE] 2025-02-25 11:03:06.016 - [任务 23][IV_REGN] - Source node "IV_REGN" read batch size: 100 
[TRACE] 2025-02-25 11:03:06.016 - [任务 23][IV_REGN] - Source node "IV_REGN" event queue capacity: 200 
[TRACE] 2025-02-25 11:03:06.016 - [任务 23][IV_REGN] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-25 11:03:06.058 - [任务 23][NEW_IV] - The table NEW_IV has already exist. 
[INFO ] 2025-02-25 11:03:06.195 - [任务 23][IV_REGN] - Use existing stream offset: {"sortString":null,"offsetValue":null,"lastScn":739064553,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[TRACE] 2025-02-25 11:03:06.195 - [任务 23][IV_REGN] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-25 11:03:06.262 - [任务 23][IV_REGN] - Starting batch read from 1 tables 
[TRACE] 2025-02-25 11:03:06.262 - [任务 23][IV_REGN] - Initial sync started 
[INFO ] 2025-02-25 11:03:06.263 - [任务 23][IV_REGN] - Starting batch read from table: IV_REGN 
[TRACE] 2025-02-25 11:03:06.264 - [任务 23][IV_REGN] - Table IV_REGN is going to be initial synced 
[TRACE] 2025-02-25 11:03:09.443 - [任务 23][IV_REGN] - Query snapshot row size completed: IV_REGN(9ec213ec-8cad-40d3-8378-13f95719ba31) 
[TRACE] 2025-02-25 11:03:09.644 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-02-25 11:03:09.721 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-02-25 11:03:09.723 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-02-25 11:03:09.865 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-02-25 11:03:09.865 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-02-25 11:03:09.975 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-02-25 11:03:09.976 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-02-25 11:03:10.083 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-02-25 11:03:10.084 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-02-25 11:03:10.218 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-02-25 11:03:10.219 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-02-25 11:03:10.424 - [任务 23][NEW_IV] - Table 'NEW_IV' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-02-25 11:03:19.612 - [任务 23][IV_REGN] - Table IV_REGN has been completed batch read 
[TRACE] 2025-02-25 11:03:19.613 - [任务 23][IV_REGN] - Initial sync completed 
[INFO ] 2025-02-25 11:03:19.613 - [任务 23][IV_REGN] - Batch read completed. 
[TRACE] 2025-02-25 11:03:19.615 - [任务 23][IV_REGN] - Incremental sync starting... 
[TRACE] 2025-02-25 11:03:19.615 - [任务 23][IV_REGN] - Initial sync completed 
[TRACE] 2025-02-25 11:03:19.618 - [任务 23][IV_REGN] - Starting stream read, table list: [IV_REGN], offset: {"sortString":null,"offsetValue":null,"lastScn":739064553,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2025-02-25 11:03:19.618 - [任务 23][IV_REGN] - Starting incremental sync using database log parser 
[INFO ] 2025-02-25 11:03:19.737 - [任务 23][IV_REGN] - Checking whether archived log exists... 
[INFO ] 2025-02-25 11:03:19.901 - [任务 23][IV_REGN] - building new log file... 
[INFO ] 2025-02-25 11:03:23.473 - [任务 23][IV_REGN] - Redo Log Miner is starting... 
[INFO ] 2025-02-25 11:04:14.517 - [任务 23][IV_REGN] - Redo Log Miner has been started... 
[TRACE] 2025-02-25 11:04:14.518 - [任务 23][IV_REGN] - Connector Oracle incremental start succeed, tables: [IV_REGN], data change syncing 
[TRACE] 2025-02-25 11:14:08.274 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] running status set to false 
[INFO ] 2025-02-25 11:14:08.276 - [任务 23][IV_REGN] - Log Miner is shutting down... 
[INFO ] 2025-02-25 11:14:08.284 - [任务 23][IV_REGN] - Auto redo oracle log miner result set closed 
[INFO ] 2025-02-25 11:14:08.285 - [任务 23][IV_REGN] - Log Miner has been closed! 
[TRACE] 2025-02-25 11:14:08.285 - [任务 23][IV_REGN] - Incremental sync completed 
[TRACE] 2025-02-25 11:14:08.319 - [任务 23][IV_REGN] - PDK connector node stopped: HazelcastSourcePdkDataNode_9ec213ec-8cad-40d3-8378-13f95719ba31_1740452585611 
[TRACE] 2025-02-25 11:14:08.321 - [任务 23][IV_REGN] - PDK connector node released: HazelcastSourcePdkDataNode_9ec213ec-8cad-40d3-8378-13f95719ba31_1740452585611 
[TRACE] 2025-02-25 11:14:08.321 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] schema data cleaned 
[TRACE] 2025-02-25 11:14:08.327 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] monitor closed 
[TRACE] 2025-02-25 11:14:08.327 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] close complete, cost 58 ms 
[TRACE] 2025-02-25 11:14:08.353 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] running status set to false 
[TRACE] 2025-02-25 11:14:08.353 - [任务 23][NEW_IV] - PDK connector node stopped: HazelcastTargetPdkDataNode_f52e0962-c4f7-4457-be9c-16d0e13f84c6_1740452585599 
[TRACE] 2025-02-25 11:14:08.354 - [任务 23][NEW_IV] - PDK connector node released: HazelcastTargetPdkDataNode_f52e0962-c4f7-4457-be9c-16d0e13f84c6_1740452585599 
[TRACE] 2025-02-25 11:14:08.354 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] schema data cleaned 
[TRACE] 2025-02-25 11:14:08.355 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] monitor closed 
[TRACE] 2025-02-25 11:14:08.355 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] close complete, cost 27 ms 
[TRACE] 2025-02-25 11:14:09.877 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-25 11:14:09.881 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6ae3dabc 
[TRACE] 2025-02-25 11:14:09.882 - [任务 23] - Stop task milestones: 67bc4af6bd8355354d858c23(任务 23)  
[TRACE] 2025-02-25 11:14:10.008 - [任务 23] - Stopped task aspect(s) 
[TRACE] 2025-02-25 11:14:10.012 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2025-02-25 11:14:10.012 - [任务 23] - Task stopped. 
[TRACE] 2025-02-25 11:14:10.075 - [任务 23] - Remove memory task client succeed, task: 任务 23[67bc4af6bd8355354d858c23] 
[TRACE] 2025-02-25 11:14:10.279 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[67bc4af6bd8355354d858c23] 
[TRACE] 2025-02-25 11:14:15.496 - [任务 23] - Start task milestones: 67bc4af6bd8355354d858c23(任务 23) 
[TRACE] 2025-02-25 11:14:15.605 - [任务 23] - Task initialization... 
[INFO ] 2025-02-25 11:14:15.605 - [任务 23] - Loading table structure completed 
[TRACE] 2025-02-25 11:14:15.667 - [任务 23] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-25 11:14:15.667 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-25 11:14:15.685 - [任务 23] - Task started 
[TRACE] 2025-02-25 11:14:15.685 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] start preload schema,table counts: 1 
[TRACE] 2025-02-25 11:14:15.685 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] start preload schema,table counts: 1 
[TRACE] 2025-02-25 11:14:15.686 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] preload schema finished, cost 0 ms 
[TRACE] 2025-02-25 11:14:15.890 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] preload schema finished, cost 0 ms 
[INFO ] 2025-02-25 11:14:16.263 - [任务 23][NEW_IV] - Sink connector(NEW_IV) initialization completed 
[TRACE] 2025-02-25 11:14:16.263 - [任务 23][NEW_IV] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-25 11:14:16.381 - [任务 23][NEW_IV] - Apply table structure to target database 
[INFO ] 2025-02-25 11:14:16.382 - [任务 23][IV_REGN] - Source connector(IV_REGN) initialization completed 
[TRACE] 2025-02-25 11:14:16.382 - [任务 23][IV_REGN] - Source node "IV_REGN" read batch size: 100 
[TRACE] 2025-02-25 11:14:16.382 - [任务 23][IV_REGN] - Source node "IV_REGN" event queue capacity: 200 
[TRACE] 2025-02-25 11:14:16.382 - [任务 23][IV_REGN] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-25 11:14:16.588 - [任务 23][NEW_IV] - The table NEW_IV has already exist. 
[INFO ] 2025-02-25 11:14:16.615 - [任务 23][IV_REGN] - Use existing stream offset: {"sortString":null,"offsetValue":null,"lastScn":739097347,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[TRACE] 2025-02-25 11:14:16.615 - [任务 23][IV_REGN] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-25 11:14:16.676 - [任务 23][IV_REGN] - Starting batch read from 1 tables 
[TRACE] 2025-02-25 11:14:16.676 - [任务 23][IV_REGN] - Initial sync started 
[INFO ] 2025-02-25 11:14:16.677 - [任务 23][IV_REGN] - Starting batch read from table: IV_REGN 
[TRACE] 2025-02-25 11:14:16.677 - [任务 23][IV_REGN] - Table IV_REGN is going to be initial synced 
[TRACE] 2025-02-25 11:14:16.797 - [任务 23][IV_REGN] - Query snapshot row size completed: IV_REGN(9ec213ec-8cad-40d3-8378-13f95719ba31) 
[TRACE] 2025-02-25 11:14:16.797 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-02-25 11:14:16.926 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-02-25 11:14:16.926 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-02-25 11:14:17.132 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-02-25 11:14:17.298 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-02-25 11:14:17.418 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-02-25 11:14:17.552 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-02-25 11:14:17.552 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-02-25 11:14:17.684 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-02-25 11:14:17.685 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-02-25 11:14:17.753 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-02-25 11:14:17.753 - [任务 23][NEW_IV] - Table 'NEW_IV' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-02-25 11:14:28.959 - [任务 23][IV_REGN] - Table IV_REGN has been completed batch read 
[TRACE] 2025-02-25 11:14:28.961 - [任务 23][IV_REGN] - Initial sync completed 
[INFO ] 2025-02-25 11:14:28.961 - [任务 23][IV_REGN] - Batch read completed. 
[TRACE] 2025-02-25 11:14:28.961 - [任务 23][IV_REGN] - Incremental sync starting... 
[TRACE] 2025-02-25 11:14:28.961 - [任务 23][IV_REGN] - Initial sync completed 
[TRACE] 2025-02-25 11:14:28.962 - [任务 23][IV_REGN] - Starting stream read, table list: [IV_REGN], offset: {"sortString":null,"offsetValue":null,"lastScn":739097347,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2025-02-25 11:14:28.963 - [任务 23][IV_REGN] - Starting incremental sync using database log parser 
[INFO ] 2025-02-25 11:14:29.316 - [任务 23][IV_REGN] - Checking whether archived log exists... 
[INFO ] 2025-02-25 11:14:29.417 - [任务 23][IV_REGN] - building new log file... 
[INFO ] 2025-02-25 11:14:34.483 - [任务 23][IV_REGN] - Redo Log Miner is starting... 
[INFO ] 2025-02-25 11:23:11.462 - [任务 23][IV_REGN] - Redo Log Miner has been started... 
[TRACE] 2025-02-25 11:23:11.463 - [任务 23][IV_REGN] - Connector Oracle incremental start succeed, tables: [IV_REGN], data change syncing 
[TRACE] 2025-02-25 11:24:58.581 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] running status set to false 
[INFO ] 2025-02-25 11:24:58.581 - [任务 23][IV_REGN] - Log Miner is shutting down... 
[INFO ] 2025-02-25 11:24:58.583 - [任务 23][IV_REGN] - Auto redo oracle log miner result set closed 
[TRACE] 2025-02-25 11:24:58.583 - [任务 23][IV_REGN] - Incremental sync completed 
[INFO ] 2025-02-25 11:24:58.584 - [任务 23][IV_REGN] - Log Miner has been closed! 
[TRACE] 2025-02-25 11:25:20.597 - [任务 23] - Start task milestones: 67bc4af6bd8355354d858c23(任务 23) 
[TRACE] 2025-02-25 11:25:20.803 - [任务 23] - Task initialization... 
[INFO ] 2025-02-25 11:25:20.895 - [任务 23] - Loading table structure completed 
[TRACE] 2025-02-25 11:25:20.990 - [任务 23] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-25 11:25:20.990 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-25 11:25:21.156 - [任务 23] - Task started 
[TRACE] 2025-02-25 11:25:21.157 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] start preload schema,table counts: 1 
[TRACE] 2025-02-25 11:25:21.157 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] start preload schema,table counts: 1 
[TRACE] 2025-02-25 11:25:21.157 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] preload schema finished, cost 0 ms 
[TRACE] 2025-02-25 11:25:21.157 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] preload schema finished, cost 0 ms 
[INFO ] 2025-02-25 11:25:21.732 - [任务 23][NEW_IV] - Sink connector(NEW_IV) initialization completed 
[TRACE] 2025-02-25 11:25:21.733 - [任务 23][NEW_IV] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-25 11:25:21.735 - [任务 23][NEW_IV] - Apply table structure to target database 
[INFO ] 2025-02-25 11:25:21.860 - [任务 23][IV_REGN] - Source connector(IV_REGN) initialization completed 
[TRACE] 2025-02-25 11:25:21.860 - [任务 23][IV_REGN] - Source node "IV_REGN" read batch size: 100 
[TRACE] 2025-02-25 11:25:21.860 - [任务 23][IV_REGN] - Source node "IV_REGN" event queue capacity: 200 
[TRACE] 2025-02-25 11:25:21.860 - [任务 23][IV_REGN] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-25 11:25:21.863 - [任务 23][NEW_IV] - The table NEW_IV has already exist. 
[INFO ] 2025-02-25 11:25:22.011 - [任务 23][IV_REGN] - Use existing stream offset: {"sortString":null,"offsetValue":null,"lastScn":739131100,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[TRACE] 2025-02-25 11:25:22.012 - [任务 23][IV_REGN] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-25 11:25:22.231 - [任务 23][IV_REGN] - Starting batch read from 1 tables 
[TRACE] 2025-02-25 11:25:22.242 - [任务 23][IV_REGN] - Initial sync started 
[INFO ] 2025-02-25 11:25:22.242 - [任务 23][IV_REGN] - Starting batch read from table: IV_REGN 
[TRACE] 2025-02-25 11:25:22.271 - [任务 23][IV_REGN] - Table IV_REGN is going to be initial synced 
[TRACE] 2025-02-25 11:25:22.271 - [任务 23][IV_REGN] - Query snapshot row size completed: IV_REGN(9ec213ec-8cad-40d3-8378-13f95719ba31) 
[TRACE] 2025-02-25 11:25:22.466 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-02-25 11:25:22.466 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-02-25 11:25:22.549 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-02-25 11:25:22.549 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-02-25 11:25:22.631 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-02-25 11:25:22.631 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-02-25 11:25:22.715 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-02-25 11:25:22.715 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-02-25 11:25:22.790 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-02-25 11:25:22.790 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-02-25 11:25:22.835 - [任务 23][NEW_IV] - Table 'NEW_IV' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-02-25 11:25:22.836 - [任务 23][NEW_IV] - Table 'NEW_IV' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-02-25 11:25:30.281 - [任务 23][IV_REGN] - Table IV_REGN has been completed batch read 
[TRACE] 2025-02-25 11:25:30.283 - [任务 23][IV_REGN] - Initial sync completed 
[INFO ] 2025-02-25 11:25:30.283 - [任务 23][IV_REGN] - Batch read completed. 
[TRACE] 2025-02-25 11:25:30.284 - [任务 23][IV_REGN] - Incremental sync starting... 
[TRACE] 2025-02-25 11:25:30.284 - [任务 23][IV_REGN] - Initial sync completed 
[TRACE] 2025-02-25 11:25:30.286 - [任务 23][IV_REGN] - Starting stream read, table list: [IV_REGN], offset: {"sortString":null,"offsetValue":null,"lastScn":739131100,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2025-02-25 11:25:30.286 - [任务 23][IV_REGN] - Starting incremental sync using database log parser 
[INFO ] 2025-02-25 11:25:30.490 - [任务 23][IV_REGN] - Checking whether archived log exists... 
[INFO ] 2025-02-25 11:25:30.691 - [任务 23][IV_REGN] - building new log file... 
[INFO ] 2025-02-25 11:25:33.718 - [任务 23][IV_REGN] - Redo Log Miner is starting... 
[INFO ] 2025-02-25 11:26:24.399 - [任务 23][IV_REGN] - Redo Log Miner has been started... 
[TRACE] 2025-02-25 11:26:24.400 - [任务 23][IV_REGN] - Connector Oracle incremental start succeed, tables: [IV_REGN], data change syncing 
[TRACE] 2025-02-25 12:50:28.419 - [任务 23][IV_REGN] - Incremental sync completed 
[TRACE] 2025-02-25 12:50:28.449 - [任务 23][IV_REGN] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17410): when operate table: unknown, java.sql.SQLRecoverableException: 无法从套接字读取更多的数据 
[ERROR] 2025-02-25 12:50:28.450 - [任务 23][IV_REGN] - java.sql.SQLRecoverableException: 无法从套接字读取更多的数据 <-- Error Message -->
java.sql.SQLRecoverableException: 无法从套接字读取更多的数据

<-- Simple Stack Trace -->
Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	...

<-- Full Stack Trace -->
java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:468)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:901)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:801)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.fetch(T4CStatement.java:1178)
	at oracle.jdbc.driver.OracleStatement.fetchMoreRows(OracleStatement.java:3785)
	at oracle.jdbc.driver.InsensitiveScrollableResultSet.fetchMoreRows(InsensitiveScrollableResultSet.java:825)
	at oracle.jdbc.driver.InsensitiveScrollableResultSet.fetchNextRows(InsensitiveScrollableResultSet.java:763)
	at oracle.jdbc.driver.InsensitiveScrollableResultSet.absoluteInternal(InsensitiveScrollableResultSet.java:735)
	at oracle.jdbc.driver.InsensitiveScrollableResultSet.next(InsensitiveScrollableResultSet.java:440)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.next(HikariProxyResultSet.java)
	at io.tapdata.connector.oracle.cdc.logminer.AutoRedoOracleLogMiner.startMiner(AutoRedoOracleLogMiner.java:57)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[TRACE] 2025-02-25 12:50:28.532 - [任务 23][IV_REGN] - Job suspend in error handle 
[TRACE] 2025-02-25 12:50:28.532 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] running status set to false 
[INFO ] 2025-02-25 12:50:28.537 - [任务 23][IV_REGN] - Log Miner is shutting down... 
[INFO ] 2025-02-25 12:50:28.537 - [任务 23][IV_REGN] - Log Miner has been closed! 
[TRACE] 2025-02-25 12:50:28.554 - [任务 23][IV_REGN] - PDK connector node stopped: HazelcastSourcePdkDataNode_9ec213ec-8cad-40d3-8378-13f95719ba31_1740453921506 
[TRACE] 2025-02-25 12:50:28.554 - [任务 23][IV_REGN] - PDK connector node released: HazelcastSourcePdkDataNode_9ec213ec-8cad-40d3-8378-13f95719ba31_1740453921506 
[TRACE] 2025-02-25 12:50:28.556 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] schema data cleaned 
[TRACE] 2025-02-25 12:50:28.556 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] monitor closed 
[TRACE] 2025-02-25 12:50:28.562 - [任务 23][IV_REGN] - Node IV_REGN[9ec213ec-8cad-40d3-8378-13f95719ba31] close complete, cost 26 ms 
[TRACE] 2025-02-25 12:50:28.562 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] running status set to false 
[TRACE] 2025-02-25 12:50:28.586 - [任务 23][NEW_IV] - PDK connector node stopped: HazelcastTargetPdkDataNode_f52e0962-c4f7-4457-be9c-16d0e13f84c6_1740453921483 
[TRACE] 2025-02-25 12:50:28.586 - [任务 23][NEW_IV] - PDK connector node released: HazelcastTargetPdkDataNode_f52e0962-c4f7-4457-be9c-16d0e13f84c6_1740453921483 
[TRACE] 2025-02-25 12:50:28.586 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] schema data cleaned 
[TRACE] 2025-02-25 12:50:28.587 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] monitor closed 
[TRACE] 2025-02-25 12:50:28.797 - [任务 23][NEW_IV] - Node NEW_IV[f52e0962-c4f7-4457-be9c-16d0e13f84c6] close complete, cost 25 ms 
[INFO ] 2025-02-25 12:50:29.851 - [任务 23] - Task [任务 23] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-02-25 12:50:29.878 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-25 12:50:29.879 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@33c309b6 
[TRACE] 2025-02-25 12:50:30.002 - [任务 23] - Stop task milestones: 67bc4af6bd8355354d858c23(任务 23)  
[TRACE] 2025-02-25 12:50:30.003 - [任务 23] - Stopped task aspect(s) 
[TRACE] 2025-02-25 12:50:30.008 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2025-02-25 12:50:30.008 - [任务 23] - Task stopped. 
[TRACE] 2025-02-25 12:50:30.049 - [任务 23] - Remove memory task client succeed, task: 任务 23[67bc4af6bd8355354d858c23] 
[TRACE] 2025-02-25 12:50:30.051 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[67bc4af6bd8355354d858c23] 
