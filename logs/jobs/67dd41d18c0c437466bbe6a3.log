[ERROR] 2025-03-24 17:15:02.533 - [任务 34] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create Ehcache TapTableMap, node id: 3a92a96c-2ed5-49c3-8fad-84e7d6ea36f3, map name: TAP_TABLE_3a92a96c-2ed5-49c3-8fad-84e7d6ea36f3, error: Cache 'TAP_TABLE_3a92a96c-2ed5-49c3-8fad-84e7d6ea36f3' already exists
	at io.tapdata.schema.TapTableMapEhcache.createEhcacheMap(TapTableMapEhcache.java:50)
	at io.tapdata.schema.TapTableMapEhcache.<init>(TapTableMapEhcache.java:35)
	at io.tapdata.schema.TapTableMap.create(TapTableMap.java:110)
	at io.tapdata.schema.TapTableMap.create(TapTableMap.java:131)
	at io.tapdata.schema.TapTableUtil.getTapTableMap(TapTableUtil.java:84)
	at io.tapdata.schema.TapTableUtil.getTapTableMap(TapTableUtil.java:68)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.getTapTableMap(HazelcastTaskService.java:509)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:363)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:253)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.getJsResult(DAGDataEngineServiceImpl.java:115)
	at com.tapdata.tm.commons.dag.process.script.MigrateScriptProcessNode.loadSchema(MigrateScriptProcessNode.java:137)
	at com.tapdata.tm.commons.dag.process.script.MigrateScriptProcessNode.loadSchema(MigrateScriptProcessNode.java:54)
	at com.tapdata.tm.commons.dag.Node.loadSchema(Node.java:338)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.nodes.DatabaseNode.transformSchema(DatabaseNode.java:249)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:292)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.IllegalArgumentException: Cache 'TAP_TABLE_3a92a96c-2ed5-49c3-8fad-84e7d6ea36f3' already exists
	at org.ehcache.core.EhcacheManager.createCache(EhcacheManager.java:266)
	at org.ehcache.core.EhcacheManager.createCache(EhcacheManager.java:252)
	at org.ehcache.core.EhcacheManager.createCache(EhcacheManager.java:247)
	at io.tapdata.pdk.core.utils.cache.EhcacheKVMap.init(EhcacheKVMap.java:123)
	at io.tapdata.pdk.core.utils.cache.EhcacheKVMap.init(EhcacheKVMap.java:69)
	at io.tapdata.schema.TapTableMapEhcache.createEhcacheMap(TapTableMapEhcache.java:46)
	... 30 more

[TRACE] 2025-03-24 17:15:04.005 - [任务 34] - load MigrateJsResultVos task 67dd41d18c0c437466bbe6a2-671c6739-f73e-439e-92b6-c748f415e61d complete, cost 1863ms 
[TRACE] 2025-03-24 17:15:34.329 - [任务 34] - load MigrateJsResultVos task 67dd41d18c0c437466bbe6a2-810ac465-6aaa-428d-9663-1b4e95a26e0e complete, cost 371ms 
[TRACE] 2025-03-24 17:15:43.501 - [任务 34] - load MigrateJsResultVos task 67dd41d18c0c437466bbe6a2-3765c479-f247-4b05-bf50-c894f88a3ac1 complete, cost 915ms 
[TRACE] 2025-03-24 17:15:51.334 - [任务 34] - load MigrateJsResultVos task 67dd41d18c0c437466bbe6a2-875fc7ec-f569-41d3-b57d-fb733568aaae complete, cost 1266ms 
