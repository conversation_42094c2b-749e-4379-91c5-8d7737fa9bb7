[TRACE] 2025-06-25 17:02:26.390 - [任务 25] - Task initialization... 
[TRACE] 2025-06-25 17:02:26.390 - [任务 25] - Start task milestones: 685bba993439e7780d3c63dd(任务 25) 
[INFO ] 2025-06-25 17:02:26.576 - [任务 25] - Loading table structure completed 
[TRACE] 2025-06-25 17:02:26.576 - [任务 25] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 17:02:26.650 - [任务 25] - The engine receives 任务 25 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 17:02:26.650 - [任务 25] - Task started 
[TRACE] 2025-06-25 17:02:26.664 - [任务 25][local_pg] - Node local_pg[c53722d9-0fcc-4523-bb03-09a18ac679e5] start preload schema,table counts: 1 
[TRACE] 2025-06-25 17:02:26.664 - [任务 25][local_pg - Copy] - Node local_pg - Copy[bfc6eaeb-aae3-4888-8c40-7e9ef8ce6485] start preload schema,table counts: 1 
[TRACE] 2025-06-25 17:02:26.664 - [任务 25][local_pg] - Node local_pg[c53722d9-0fcc-4523-bb03-09a18ac679e5] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 17:02:26.664 - [任务 25][local_pg - Copy] - Node local_pg - Copy[bfc6eaeb-aae3-4888-8c40-7e9ef8ce6485] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 17:02:26.868 - [任务 25][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 17:02:27.100 - [任务 25][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 17:02:27.100 - [任务 25][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 17:02:27.100 - [任务 25][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 17:02:27.208 - [任务 25][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-25 17:02:27.209 - [任务 25][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 17:02:27.209 - [任务 25][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 17:02:27.209 - [任务 25][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[TRACE] 2025-06-25 17:02:27.209 - [任务 25][local_pg - Copy] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-25 17:02:27.225 - [任务 25][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 17:02:27.243 - [任务 25][local_pg - Copy] - new logical replication slot created, slotName:tapdata_cdc_e4d214e6_75bf_4a1e_aaa1_3a8825dc32d1 
[INFO ] 2025-06-25 17:02:27.243 - [任务 25][local_pg - Copy] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 17:02:31.139 - [任务 25][local_pg - Copy] - Starting batch read from 1 tables 
[TRACE] 2025-06-25 17:02:31.158 - [任务 25][local_pg - Copy] - Initial sync started 
[INFO ] 2025-06-25 17:02:31.159 - [任务 25][local_pg - Copy] - Starting batch read from table: table_1 
[TRACE] 2025-06-25 17:02:31.159 - [任务 25][local_pg - Copy] - Table table_1 is going to be initial synced 
[TRACE] 2025-06-25 17:02:31.159 - [任务 25][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(bfc6eaeb-aae3-4888-8c40-7e9ef8ce6485) 
[INFO ] 2025-06-25 17:02:33.830 - [任务 25][local_pg - Copy] - Table table_1 has been completed batch read 
[TRACE] 2025-06-25 17:02:33.830 - [任务 25][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 17:02:33.831 - [任务 25][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 17:02:33.831 - [任务 25][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 17:02:33.831 - [任务 25][local_pg - Copy] - Initial sync completed 
[TRACE] 2025-06-25 17:02:33.831 - [任务 25][local_pg - Copy] - Starting stream read, table list: [table_1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 17:02:33.831 - [任务 25][local_pg - Copy] - Starting incremental sync using database log parser 
[WARN ] 2025-06-25 17:02:33.835 - [任务 25][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 17:02:33.835 - [任务 25][local_pg - Copy] - Using an existing logical replication slot, slotName:tapdata_cdc_e4d214e6_75bf_4a1e_aaa1_3a8825dc32d1 
[TRACE] 2025-06-25 17:02:34.040 - [任务 25][local_pg - Copy] - Connector PostgreSQL incremental start succeed, tables: [table_1], data change syncing 
[TRACE] 2025-06-25 17:02:34.365 - [任务 25][local_pg] - Process after table "table_1" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-25 17:02:34.570 - [任务 25][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-25 17:13:21.046 - [任务 25][local_pg - Copy] - Node local_pg - Copy[bfc6eaeb-aae3-4888-8c40-7e9ef8ce6485] running status set to false 
[TRACE] 2025-06-25 17:13:21.184 - [任务 25][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 17:13:21.189 - [任务 25][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_bfc6eaeb-aae3-4888-8c40-7e9ef8ce6485_1750842146968 
[TRACE] 2025-06-25 17:13:21.189 - [任务 25][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_bfc6eaeb-aae3-4888-8c40-7e9ef8ce6485_1750842146968 
[TRACE] 2025-06-25 17:13:21.189 - [任务 25][local_pg - Copy] - Node local_pg - Copy[bfc6eaeb-aae3-4888-8c40-7e9ef8ce6485] schema data cleaned 
[TRACE] 2025-06-25 17:13:21.189 - [任务 25][local_pg - Copy] - Node local_pg - Copy[bfc6eaeb-aae3-4888-8c40-7e9ef8ce6485] monitor closed 
[TRACE] 2025-06-25 17:13:21.190 - [任务 25][local_pg - Copy] - Node local_pg - Copy[bfc6eaeb-aae3-4888-8c40-7e9ef8ce6485] close complete, cost 332 ms 
[TRACE] 2025-06-25 17:13:21.190 - [任务 25][local_pg] - Node local_pg[c53722d9-0fcc-4523-bb03-09a18ac679e5] running status set to false 
[TRACE] 2025-06-25 17:13:21.197 - [任务 25][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_c53722d9-0fcc-4523-bb03-09a18ac679e5_1750842146951 
[TRACE] 2025-06-25 17:13:21.197 - [任务 25][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_c53722d9-0fcc-4523-bb03-09a18ac679e5_1750842146951 
[TRACE] 2025-06-25 17:13:21.197 - [任务 25][local_pg] - Node local_pg[c53722d9-0fcc-4523-bb03-09a18ac679e5] schema data cleaned 
[TRACE] 2025-06-25 17:13:21.198 - [任务 25][local_pg] - Node local_pg[c53722d9-0fcc-4523-bb03-09a18ac679e5] monitor closed 
[TRACE] 2025-06-25 17:13:21.403 - [任务 25][local_pg] - Node local_pg[c53722d9-0fcc-4523-bb03-09a18ac679e5] close complete, cost 7 ms 
[TRACE] 2025-06-25 17:13:26.672 - [任务 25] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 17:13:27.678 - [任务 25] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@31c80035 
[TRACE] 2025-06-25 17:13:27.678 - [任务 25] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@695ed03c 
[TRACE] 2025-06-25 17:13:27.799 - [任务 25] - Stop task milestones: 685bba993439e7780d3c63dd(任务 25)  
[TRACE] 2025-06-25 17:13:27.799 - [任务 25] - Stopped task aspect(s) 
[TRACE] 2025-06-25 17:13:27.799 - [任务 25] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 17:13:27.800 - [任务 25] - Task stopped. 
[TRACE] 2025-06-25 17:13:27.851 - [任务 25] - Remove memory task client succeed, task: 任务 25[685bba993439e7780d3c63dd] 
[TRACE] 2025-06-25 17:13:27.854 - [任务 25] - Destroy memory task client cache succeed, task: 任务 25[685bba993439e7780d3c63dd] 
