[TRACE] 2025-05-29 09:52:58.019 - [sqlserver_pg] - Task initialization... 
[TRACE] 2025-05-29 09:52:58.029 - [sqlserver_pg] - Start task milestones: 6835768578d7e01e143c5201(sqlserver_pg) 
[INFO ] 2025-05-29 09:52:58.324 - [sqlserver_pg] - Loading table structure completed 
[TRACE] 2025-05-29 09:52:58.325 - [sqlserver_pg] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-29 09:52:58.493 - [sqlserver_pg] - The engine receives sqlserver_pg task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-29 09:52:58.645 - [sqlserver_pg] - Task started 
[TRACE] 2025-05-29 09:52:58.645 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] start preload schema,table counts: 1 
[TRACE] 2025-05-29 09:52:58.645 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] start preload schema,table counts: 1 
[TRACE] 2025-05-29 09:52:58.646 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] preload schema finished, cost 0 ms 
[TRACE] 2025-05-29 09:52:58.646 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] preload schema finished, cost 0 ms 
[INFO ] 2025-05-29 09:52:59.063 - [sqlserver_pg][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-05-29 09:52:59.064 - [sqlserver_pg][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-29 09:52:59.064 - [sqlserver_pg][local_pg] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-29 09:52:59.117 - [sqlserver_pg][local_pg] - Apply table structure to target database 
[TRACE] 2025-05-29 09:52:59.117 - [sqlserver_pg][local_pg] - The table SourceOfRegion has already exist. 
[INFO ] 2025-05-29 09:52:59.257 - [sqlserver_pg][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-29 09:52:59.257 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" read batch size: 100 
[TRACE] 2025-05-29 09:52:59.257 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" event queue capacity: 200 
[TRACE] 2025-05-29 09:52:59.258 - [sqlserver_pg][sqlserver -ag1] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-29 09:52:59.481 - [sqlserver_pg][sqlserver -ag1] - Use existing stream offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 09:52:59.538 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from 1 tables 
[TRACE] 2025-05-29 09:52:59.539 - [sqlserver_pg][sqlserver -ag1] - Initial sync started 
[INFO ] 2025-05-29 09:52:59.539 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-05-29 09:52:59.619 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-05-29 09:52:59.619 - [sqlserver_pg][sqlserver -ag1] - Query snapshot row size completed: sqlserver -ag1(1262911e-dca9-4c94-93cf-597bee433e76) 
[INFO ] 2025-05-29 09:52:59.736 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-05-29 09:52:59.736 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[INFO ] 2025-05-29 09:52:59.736 - [sqlserver_pg][sqlserver -ag1] - Batch read completed. 
[TRACE] 2025-05-29 09:52:59.738 - [sqlserver_pg][sqlserver -ag1] - Incremental sync starting... 
[TRACE] 2025-05-29 09:52:59.738 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[TRACE] 2025-05-29 09:52:59.740 - [sqlserver_pg][sqlserver -ag1] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 09:52:59.740 - [sqlserver_pg][sqlserver -ag1] - Starting incremental sync using database log parser 
[INFO ] 2025-05-29 09:52:59.940 - [sqlserver_pg][sqlserver -ag1] - opened cdc tables: [RefreshToken, SourceOfRegion] 
[INFO ] 2025-05-29 09:53:00.112 - [sqlserver_pg][sqlserver -ag1] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-05-29 09:53:00.117 - [sqlserver_pg][sqlserver -ag1] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-05-29 09:53:00.117 - [sqlserver_pg][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-29 09:53:00.117 - [sqlserver_pg][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-05-29 10:00:12.119 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] running status set to false 
[TRACE] 2025-05-29 10:00:12.126 - [sqlserver_pg] - Task initialization... 
[TRACE] 2025-05-29 10:00:12.331 - [sqlserver_pg] - Start task milestones: 6835768578d7e01e143c5201(sqlserver_pg) 
[INFO ] 2025-05-29 10:00:12.407 - [sqlserver_pg] - Loading table structure completed 
[TRACE] 2025-05-29 10:00:12.578 - [sqlserver_pg] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-29 10:00:12.578 - [sqlserver_pg] - The engine receives sqlserver_pg task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-29 10:00:12.715 - [sqlserver_pg] - Task started 
[TRACE] 2025-05-29 10:00:12.715 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] start preload schema,table counts: 1 
[TRACE] 2025-05-29 10:00:12.716 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] start preload schema,table counts: 1 
[TRACE] 2025-05-29 10:00:12.716 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] preload schema finished, cost 0 ms 
[TRACE] 2025-05-29 10:00:12.716 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] preload schema finished, cost 0 ms 
[INFO ] 2025-05-29 10:00:12.880 - [sqlserver_pg][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-05-29 10:00:12.880 - [sqlserver_pg][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-29 10:00:12.880 - [sqlserver_pg][local_pg] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-29 10:00:13.066 - [sqlserver_pg][local_pg] - Apply table structure to target database 
[INFO ] 2025-05-29 10:00:13.066 - [sqlserver_pg][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-29 10:00:13.068 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" read batch size: 100 
[TRACE] 2025-05-29 10:00:13.068 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" event queue capacity: 200 
[INFO ] 2025-05-29 10:00:13.068 - [sqlserver_pg][sqlserver -ag1] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-05-29 10:00:13.076 - [sqlserver_pg][sqlserver -ag1] - Use existing batch read offset: {"SourceOfRegion":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{"SourceOfRegion":"0000002400000C600004"},"ddlOffset":"AAAAJAAADGAABA=="} 
[INFO ] 2025-05-29 10:00:13.327 - [sqlserver_pg][sqlserver -ag1] - Batch read completed. 
[TRACE] 2025-05-29 10:00:13.327 - [sqlserver_pg][sqlserver -ag1] - Incremental sync starting... 
[TRACE] 2025-05-29 10:00:13.327 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[TRACE] 2025-05-29 10:00:13.331 - [sqlserver_pg][sqlserver -ag1] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{"SourceOfRegion":"0000002400000C600004"},"ddlOffset":"AAAAJAAADGAABA=="} 
[INFO ] 2025-05-29 10:00:13.331 - [sqlserver_pg][sqlserver -ag1] - Starting incremental sync using database log parser 
[INFO ] 2025-05-29 10:00:13.534 - [sqlserver_pg][sqlserver -ag1] - opened cdc tables: [RefreshToken, SourceOfRegion] 
[INFO ] 2025-05-29 10:00:13.777 - [sqlserver_pg][sqlserver -ag1] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-05-29 10:00:13.978 - [sqlserver_pg][sqlserver -ag1] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-05-29 10:01:33.539 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] running status set to false 
[TRACE] 2025-05-29 10:08:22.600 - [sqlserver_pg][sqlserver -ag1] - Incremental sync completed 
[TRACE] 2025-05-29 10:08:22.617 - [sqlserver_pg] - Task initialization... 
[TRACE] 2025-05-29 10:08:22.617 - [sqlserver_pg] - Start task milestones: 6835768578d7e01e143c5201(sqlserver_pg) 
[INFO ] 2025-05-29 10:08:22.921 - [sqlserver_pg] - Loading table structure completed 
[TRACE] 2025-05-29 10:08:23.268 - [sqlserver_pg] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-29 10:08:23.393 - [sqlserver_pg] - The engine receives sqlserver_pg task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-29 10:08:23.393 - [sqlserver_pg] - Task started 
[TRACE] 2025-05-29 10:08:23.483 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] start preload schema,table counts: 1 
[TRACE] 2025-05-29 10:08:23.483 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] start preload schema,table counts: 1 
[TRACE] 2025-05-29 10:08:23.483 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] preload schema finished, cost 0 ms 
[TRACE] 2025-05-29 10:08:23.488 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] preload schema finished, cost 0 ms 
[INFO ] 2025-05-29 10:08:23.902 - [sqlserver_pg][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-05-29 10:08:23.903 - [sqlserver_pg][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-29 10:08:23.903 - [sqlserver_pg][local_pg] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-29 10:08:24.108 - [sqlserver_pg][local_pg] - Apply table structure to target database 
[INFO ] 2025-05-29 10:08:24.231 - [sqlserver_pg][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-29 10:08:24.233 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" read batch size: 100 
[TRACE] 2025-05-29 10:08:24.233 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" event queue capacity: 200 
[INFO ] 2025-05-29 10:08:24.233 - [sqlserver_pg][sqlserver -ag1] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-05-29 10:08:24.237 - [sqlserver_pg][sqlserver -ag1] - Use existing batch read offset: {"SourceOfRegion":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: "{\"currentStartLSN\":\"0000002400000C600004\",\"ddlOffset\":\"AAAAJAAADGAABA==\",\"tablesOffset\":{\"SourceOfRegion\":\"0000002400000C600004\"}}" 
[INFO ] 2025-05-29 10:08:24.290 - [sqlserver_pg][sqlserver -ag1] - Batch read completed. 
[TRACE] 2025-05-29 10:08:24.290 - [sqlserver_pg][sqlserver -ag1] - Incremental sync starting... 
[TRACE] 2025-05-29 10:08:24.290 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[TRACE] 2025-05-29 10:08:24.292 - [sqlserver_pg][sqlserver -ag1] - Starting stream read, table list: [SourceOfRegion], offset: "{\"currentStartLSN\":\"0000002400000C600004\",\"ddlOffset\":\"AAAAJAAADGAABA==\",\"tablesOffset\":{\"SourceOfRegion\":\"0000002400000C600004\"}}" 
[INFO ] 2025-05-29 10:08:24.496 - [sqlserver_pg][sqlserver -ag1] - Starting incremental sync using database log parser 
[INFO ] 2025-05-29 10:08:24.697 - [sqlserver_pg][sqlserver -ag1] - opened cdc tables: [RefreshToken, SourceOfRegion] 
[INFO ] 2025-05-29 10:08:24.816 - [sqlserver_pg][sqlserver -ag1] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-05-29 10:08:25.017 - [sqlserver_pg][sqlserver -ag1] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-05-29 10:08:28.644 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] running status set to false 
[TRACE] 2025-05-29 10:13:28.343 - [sqlserver_pg] - Task initialization... 
[TRACE] 2025-05-29 10:13:28.544 - [sqlserver_pg] - Start task milestones: 6835768578d7e01e143c5201(sqlserver_pg) 
[INFO ] 2025-05-29 10:13:28.985 - [sqlserver_pg] - Loading table structure completed 
[TRACE] 2025-05-29 10:13:29.336 - [sqlserver_pg] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-29 10:13:29.336 - [sqlserver_pg] - The engine receives sqlserver_pg task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-29 10:13:29.506 - [sqlserver_pg] - Task started 
[TRACE] 2025-05-29 10:13:29.507 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] start preload schema,table counts: 1 
[TRACE] 2025-05-29 10:13:29.507 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] start preload schema,table counts: 1 
[TRACE] 2025-05-29 10:13:29.507 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] preload schema finished, cost 0 ms 
[TRACE] 2025-05-29 10:13:29.508 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] preload schema finished, cost 0 ms 
[INFO ] 2025-05-29 10:13:30.134 - [sqlserver_pg][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-05-29 10:13:30.134 - [sqlserver_pg][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-29 10:13:30.135 - [sqlserver_pg][local_pg] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-29 10:13:30.245 - [sqlserver_pg][local_pg] - Apply table structure to target database 
[INFO ] 2025-05-29 10:13:30.245 - [sqlserver_pg][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-29 10:13:30.247 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" read batch size: 100 
[TRACE] 2025-05-29 10:13:30.247 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" event queue capacity: 200 
[INFO ] 2025-05-29 10:13:30.248 - [sqlserver_pg][sqlserver -ag1] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-05-29 10:13:30.251 - [sqlserver_pg][sqlserver -ag1] - Use existing batch read offset: {"SourceOfRegion":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: "{\"currentStartLSN\":\"0000002400000C600004\",\"ddlOffset\":\"AAAAJAAADGAABA==\",\"tablesOffset\":{\"SourceOfRegion\":\"0000002400000C600004\"}}" 
[INFO ] 2025-05-29 10:13:30.332 - [sqlserver_pg][sqlserver -ag1] - Batch read completed. 
[TRACE] 2025-05-29 10:13:30.332 - [sqlserver_pg][sqlserver -ag1] - Incremental sync starting... 
[TRACE] 2025-05-29 10:13:30.333 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[TRACE] 2025-05-29 10:13:30.335 - [sqlserver_pg][sqlserver -ag1] - Starting stream read, table list: [SourceOfRegion], offset: "{\"currentStartLSN\":\"0000002400000C600004\",\"ddlOffset\":\"AAAAJAAADGAABA==\",\"tablesOffset\":{\"SourceOfRegion\":\"0000002400000C600004\"}}" 
[INFO ] 2025-05-29 10:13:30.335 - [sqlserver_pg][sqlserver -ag1] - Starting incremental sync using database log parser 
[INFO ] 2025-05-29 10:13:30.539 - [sqlserver_pg][sqlserver -ag1] - opened cdc tables: [RefreshToken, SourceOfRegion] 
[INFO ] 2025-05-29 10:13:30.618 - [sqlserver_pg][sqlserver -ag1] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-05-29 10:13:30.619 - [sqlserver_pg][sqlserver -ag1] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-05-29 10:14:51.368 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] running status set to false 
[TRACE] 2025-05-29 10:17:18.568 - [sqlserver_pg] - Task initialization... 
[TRACE] 2025-05-29 10:17:18.755 - [sqlserver_pg] - Start task milestones: 6835768578d7e01e143c5201(sqlserver_pg) 
[INFO ] 2025-05-29 10:17:18.756 - [sqlserver_pg] - Loading table structure completed 
[TRACE] 2025-05-29 10:17:18.802 - [sqlserver_pg] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-29 10:17:18.932 - [sqlserver_pg] - The engine receives sqlserver_pg task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-29 10:17:18.932 - [sqlserver_pg] - Task started 
[TRACE] 2025-05-29 10:17:18.990 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] start preload schema,table counts: 1 
[TRACE] 2025-05-29 10:17:18.990 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] start preload schema,table counts: 1 
[TRACE] 2025-05-29 10:17:18.990 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] preload schema finished, cost 0 ms 
[TRACE] 2025-05-29 10:17:18.990 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] preload schema finished, cost 0 ms 
[INFO ] 2025-05-29 10:17:19.386 - [sqlserver_pg][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-05-29 10:17:19.386 - [sqlserver_pg][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-29 10:17:19.387 - [sqlserver_pg][local_pg] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-29 10:17:19.440 - [sqlserver_pg][local_pg] - Apply table structure to target database 
[TRACE] 2025-05-29 10:17:19.440 - [sqlserver_pg][local_pg] - The table SourceOfRegion has already exist. 
[INFO ] 2025-05-29 10:17:19.611 - [sqlserver_pg][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-29 10:17:19.612 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" read batch size: 100 
[TRACE] 2025-05-29 10:17:19.612 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" event queue capacity: 200 
[TRACE] 2025-05-29 10:17:19.771 - [sqlserver_pg][sqlserver -ag1] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-29 10:17:19.771 - [sqlserver_pg][sqlserver -ag1] - Use existing stream offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 10:17:19.824 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from 1 tables 
[TRACE] 2025-05-29 10:17:19.833 - [sqlserver_pg][sqlserver -ag1] - Initial sync started 
[INFO ] 2025-05-29 10:17:19.833 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-05-29 10:17:19.926 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-05-29 10:17:19.926 - [sqlserver_pg][sqlserver -ag1] - Query snapshot row size completed: sqlserver -ag1(1262911e-dca9-4c94-93cf-597bee433e76) 
[INFO ] 2025-05-29 10:17:20.055 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-05-29 10:17:20.057 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[INFO ] 2025-05-29 10:17:20.057 - [sqlserver_pg][sqlserver -ag1] - Batch read completed. 
[TRACE] 2025-05-29 10:17:20.058 - [sqlserver_pg][sqlserver -ag1] - Incremental sync starting... 
[TRACE] 2025-05-29 10:17:20.058 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[TRACE] 2025-05-29 10:17:20.059 - [sqlserver_pg][sqlserver -ag1] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 10:17:20.059 - [sqlserver_pg][sqlserver -ag1] - Starting incremental sync using database log parser 
[INFO ] 2025-05-29 10:17:20.260 - [sqlserver_pg][sqlserver -ag1] - opened cdc tables: [RefreshToken, SourceOfRegion] 
[TRACE] 2025-05-29 10:17:20.447 - [sqlserver_pg][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 1 ms 
[INFO ] 2025-05-29 10:17:20.648 - [sqlserver_pg][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-05-29 10:17:21.212 - [sqlserver_pg][sqlserver -ag1] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-05-29 10:17:21.413 - [sqlserver_pg][sqlserver -ag1] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-05-29 10:25:40.158 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] running status set to false 
[TRACE] 2025-05-29 10:29:10.911 - [sqlserver_pg] - Task initialization... 
[TRACE] 2025-05-29 10:29:11.117 - [sqlserver_pg] - Start task milestones: 6835768578d7e01e143c5201(sqlserver_pg) 
[INFO ] 2025-05-29 10:29:11.125 - [sqlserver_pg] - Loading table structure completed 
[TRACE] 2025-05-29 10:29:11.216 - [sqlserver_pg] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-29 10:29:11.216 - [sqlserver_pg] - The engine receives sqlserver_pg task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-29 10:29:11.347 - [sqlserver_pg] - Task started 
[TRACE] 2025-05-29 10:29:11.347 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] start preload schema,table counts: 1 
[TRACE] 2025-05-29 10:29:11.347 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] start preload schema,table counts: 1 
[TRACE] 2025-05-29 10:29:11.347 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] preload schema finished, cost 0 ms 
[TRACE] 2025-05-29 10:29:11.348 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] preload schema finished, cost 0 ms 
[INFO ] 2025-05-29 10:29:11.774 - [sqlserver_pg][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-05-29 10:29:11.776 - [sqlserver_pg][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-29 10:29:11.776 - [sqlserver_pg][local_pg] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-29 10:29:11.840 - [sqlserver_pg][local_pg] - Apply table structure to target database 
[TRACE] 2025-05-29 10:29:11.844 - [sqlserver_pg][local_pg] - The table SourceOfRegion has already exist. 
[INFO ] 2025-05-29 10:29:12.096 - [sqlserver_pg][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-29 10:29:12.096 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" read batch size: 100 
[TRACE] 2025-05-29 10:29:12.097 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" event queue capacity: 200 
[TRACE] 2025-05-29 10:29:12.097 - [sqlserver_pg][sqlserver -ag1] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-29 10:29:12.266 - [sqlserver_pg][sqlserver -ag1] - Use existing stream offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 10:29:12.349 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from 1 tables 
[TRACE] 2025-05-29 10:29:12.360 - [sqlserver_pg][sqlserver -ag1] - Initial sync started 
[INFO ] 2025-05-29 10:29:12.361 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-05-29 10:29:12.441 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-05-29 10:29:12.441 - [sqlserver_pg][sqlserver -ag1] - Query snapshot row size completed: sqlserver -ag1(1262911e-dca9-4c94-93cf-597bee433e76) 
[INFO ] 2025-05-29 10:29:12.558 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-05-29 10:29:12.558 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[INFO ] 2025-05-29 10:29:12.558 - [sqlserver_pg][sqlserver -ag1] - Batch read completed. 
[TRACE] 2025-05-29 10:29:12.559 - [sqlserver_pg][sqlserver -ag1] - Incremental sync starting... 
[TRACE] 2025-05-29 10:29:12.559 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[TRACE] 2025-05-29 10:29:12.560 - [sqlserver_pg][sqlserver -ag1] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 10:29:12.560 - [sqlserver_pg][sqlserver -ag1] - Starting incremental sync using database log parser 
[INFO ] 2025-05-29 10:29:12.763 - [sqlserver_pg][sqlserver -ag1] - opened cdc tables: [RefreshToken, SourceOfRegion] 
[TRACE] 2025-05-29 10:29:18.119 - [sqlserver_pg][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-29 10:29:18.120 - [sqlserver_pg][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-05-29 10:29:18.262 - [sqlserver_pg][sqlserver -ag1] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-05-29 10:29:18.262 - [sqlserver_pg][sqlserver -ag1] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-05-29 11:09:16.291 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] running status set to false 
[TRACE] 2025-05-29 11:09:16.891 - [sqlserver_pg][sqlserver -ag1] - Incremental sync completed 
[TRACE] 2025-05-29 11:09:19.300 - [sqlserver_pg][sqlserver -ag1] - PDK connector node stopped: HazelcastSourcePdkDataNode_1262911e-dca9-4c94-93cf-597bee433e76_1748485751655 
[TRACE] 2025-05-29 11:09:19.301 - [sqlserver_pg][sqlserver -ag1] - PDK connector node released: HazelcastSourcePdkDataNode_1262911e-dca9-4c94-93cf-597bee433e76_1748485751655 
[TRACE] 2025-05-29 11:09:19.301 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] schema data cleaned 
[TRACE] 2025-05-29 11:09:19.303 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] monitor closed 
[TRACE] 2025-05-29 11:09:19.303 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] close complete, cost 3014 ms 
[TRACE] 2025-05-29 11:09:19.304 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] running status set to false 
[TRACE] 2025-05-29 11:09:19.314 - [sqlserver_pg][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4_1748485751642 
[TRACE] 2025-05-29 11:09:19.314 - [sqlserver_pg][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4_1748485751642 
[TRACE] 2025-05-29 11:09:19.315 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] schema data cleaned 
[TRACE] 2025-05-29 11:09:19.315 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] monitor closed 
[TRACE] 2025-05-29 11:09:19.315 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] close complete, cost 11 ms 
[TRACE] 2025-05-29 11:09:26.903 - [sqlserver_pg] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-29 11:09:27.910 - [sqlserver_pg] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@1432ddc2 
[TRACE] 2025-05-29 11:09:27.915 - [sqlserver_pg] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@60973b17 
[TRACE] 2025-05-29 11:09:27.915 - [sqlserver_pg] - Stop task milestones: 6835768578d7e01e143c5201(sqlserver_pg)  
[TRACE] 2025-05-29 11:09:28.040 - [sqlserver_pg] - Stopped task aspect(s) 
[TRACE] 2025-05-29 11:09:28.041 - [sqlserver_pg] - Snapshot order controller have been removed 
[INFO ] 2025-05-29 11:09:28.110 - [sqlserver_pg] - Task stopped. 
[TRACE] 2025-05-29 11:09:28.111 - [sqlserver_pg] - Remove memory task client succeed, task: sqlserver_pg[6835768578d7e01e143c5201] 
[TRACE] 2025-05-29 11:09:28.111 - [sqlserver_pg] - Destroy memory task client cache succeed, task: sqlserver_pg[6835768578d7e01e143c5201] 
[TRACE] 2025-05-29 11:11:21.423 - [sqlserver_pg] - Task initialization... 
[TRACE] 2025-05-29 11:11:21.630 - [sqlserver_pg] - Start task milestones: 6835768578d7e01e143c5201(sqlserver_pg) 
[INFO ] 2025-05-29 11:11:21.740 - [sqlserver_pg] - Loading table structure completed 
[TRACE] 2025-05-29 11:11:21.741 - [sqlserver_pg] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-29 11:11:21.846 - [sqlserver_pg] - The engine receives sqlserver_pg task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-29 11:11:21.846 - [sqlserver_pg] - Task started 
[TRACE] 2025-05-29 11:11:21.905 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] start preload schema,table counts: 1 
[TRACE] 2025-05-29 11:11:21.906 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] start preload schema,table counts: 1 
[TRACE] 2025-05-29 11:11:21.906 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] preload schema finished, cost 0 ms 
[TRACE] 2025-05-29 11:11:21.906 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] preload schema finished, cost 0 ms 
[INFO ] 2025-05-29 11:11:22.329 - [sqlserver_pg][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-05-29 11:11:22.329 - [sqlserver_pg][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-29 11:11:22.330 - [sqlserver_pg][local_pg] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-29 11:11:22.388 - [sqlserver_pg][local_pg] - Apply table structure to target database 
[TRACE] 2025-05-29 11:11:22.389 - [sqlserver_pg][local_pg] - The table SourceOfRegion has already exist. 
[INFO ] 2025-05-29 11:11:22.643 - [sqlserver_pg][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-29 11:11:22.643 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" read batch size: 100 
[TRACE] 2025-05-29 11:11:22.644 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" event queue capacity: 200 
[TRACE] 2025-05-29 11:11:22.644 - [sqlserver_pg][sqlserver -ag1] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-29 11:11:23.194 - [sqlserver_pg][sqlserver -ag1] - Use existing stream offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 11:11:23.276 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from 1 tables 
[TRACE] 2025-05-29 11:11:23.286 - [sqlserver_pg][sqlserver -ag1] - Initial sync started 
[INFO ] 2025-05-29 11:11:23.286 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-05-29 11:11:23.487 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-05-29 11:11:23.493 - [sqlserver_pg][sqlserver -ag1] - Query snapshot row size completed: sqlserver -ag1(1262911e-dca9-4c94-93cf-597bee433e76) 
[INFO ] 2025-05-29 11:11:23.586 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-05-29 11:11:23.587 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[INFO ] 2025-05-29 11:11:23.587 - [sqlserver_pg][sqlserver -ag1] - Batch read completed. 
[TRACE] 2025-05-29 11:11:23.588 - [sqlserver_pg][sqlserver -ag1] - Incremental sync starting... 
[TRACE] 2025-05-29 11:11:23.589 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[TRACE] 2025-05-29 11:11:23.590 - [sqlserver_pg][sqlserver -ag1] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 11:11:23.590 - [sqlserver_pg][sqlserver -ag1] - Starting incremental sync using database log parser 
[INFO ] 2025-05-29 11:11:23.764 - [sqlserver_pg][sqlserver -ag1] - opened cdc tables: [RefreshToken, SourceOfRegion] 
[INFO ] 2025-05-29 11:11:24.117 - [sqlserver_pg][sqlserver -ag1] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-05-29 11:11:24.323 - [sqlserver_pg][sqlserver -ag1] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-05-29 11:11:24.372 - [sqlserver_pg][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 1 ms 
[INFO ] 2025-05-29 11:11:24.372 - [sqlserver_pg][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-05-29 11:13:36.324 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] running status set to false 
[TRACE] 2025-05-29 11:13:36.530 - [sqlserver_pg][sqlserver -ag1] - Incremental sync completed 
[TRACE] 2025-05-29 11:13:39.337 - [sqlserver_pg][sqlserver -ag1] - PDK connector node stopped: HazelcastSourcePdkDataNode_1262911e-dca9-4c94-93cf-597bee433e76_1748488282165 
[TRACE] 2025-05-29 11:13:39.338 - [sqlserver_pg][sqlserver -ag1] - PDK connector node released: HazelcastSourcePdkDataNode_1262911e-dca9-4c94-93cf-597bee433e76_1748488282165 
[TRACE] 2025-05-29 11:13:39.338 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] schema data cleaned 
[TRACE] 2025-05-29 11:13:39.360 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] monitor closed 
[TRACE] 2025-05-29 11:13:39.360 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] close complete, cost 3018 ms 
[TRACE] 2025-05-29 11:13:39.361 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] running status set to false 
[TRACE] 2025-05-29 11:13:39.368 - [sqlserver_pg][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4_1748488282205 
[TRACE] 2025-05-29 11:13:39.368 - [sqlserver_pg][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4_1748488282205 
[TRACE] 2025-05-29 11:13:39.368 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] schema data cleaned 
[TRACE] 2025-05-29 11:13:39.369 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] monitor closed 
[TRACE] 2025-05-29 11:13:39.369 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] close complete, cost 8 ms 
[TRACE] 2025-05-29 11:13:48.085 - [sqlserver_pg] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-29 11:13:48.925 - [sqlserver_pg] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@415f8c06 
[TRACE] 2025-05-29 11:13:48.926 - [sqlserver_pg] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1e8913b6 
[TRACE] 2025-05-29 11:13:49.056 - [sqlserver_pg] - Stop task milestones: 6835768578d7e01e143c5201(sqlserver_pg)  
[TRACE] 2025-05-29 11:13:49.056 - [sqlserver_pg] - Stopped task aspect(s) 
[TRACE] 2025-05-29 11:13:49.061 - [sqlserver_pg] - Snapshot order controller have been removed 
[INFO ] 2025-05-29 11:13:49.061 - [sqlserver_pg] - Task stopped. 
[TRACE] 2025-05-29 11:13:49.117 - [sqlserver_pg] - Remove memory task client succeed, task: sqlserver_pg[6835768578d7e01e143c5201] 
[TRACE] 2025-05-29 11:13:49.118 - [sqlserver_pg] - Destroy memory task client cache succeed, task: sqlserver_pg[6835768578d7e01e143c5201] 
[TRACE] 2025-05-29 14:16:44.116 - [sqlserver_pg] - Task initialization... 
[TRACE] 2025-05-29 14:16:44.221 - [sqlserver_pg] - Start task milestones: 6835768578d7e01e143c5201(sqlserver_pg) 
[INFO ] 2025-05-29 14:16:44.221 - [sqlserver_pg] - Loading table structure completed 
[TRACE] 2025-05-29 14:16:44.336 - [sqlserver_pg] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-29 14:16:44.340 - [sqlserver_pg] - The engine receives sqlserver_pg task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-29 14:16:44.356 - [sqlserver_pg] - Task started 
[TRACE] 2025-05-29 14:16:44.356 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] start preload schema,table counts: 3 
[TRACE] 2025-05-29 14:16:44.356 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] start preload schema,table counts: 3 
[TRACE] 2025-05-29 14:16:44.356 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] preload schema finished, cost 0 ms 
[TRACE] 2025-05-29 14:16:44.558 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] preload schema finished, cost 0 ms 
[INFO ] 2025-05-29 14:16:44.800 - [sqlserver_pg][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-05-29 14:16:44.801 - [sqlserver_pg][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-29 14:16:44.801 - [sqlserver_pg][local_pg] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-29 14:16:44.824 - [sqlserver_pg][local_pg] - Apply table structure to target database 
[TRACE] 2025-05-29 14:16:44.825 - [sqlserver_pg][local_pg] - The table SourceOfRegion has already exist. 
[INFO ] 2025-05-29 14:16:44.963 - [sqlserver_pg][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-29 14:16:44.964 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" read batch size: 100 
[TRACE] 2025-05-29 14:16:44.964 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" event queue capacity: 200 
[TRACE] 2025-05-29 14:16:45.141 - [sqlserver_pg][sqlserver -ag1] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-29 14:16:45.141 - [sqlserver_pg][sqlserver -ag1] - building CT table for table SourceOfRegion3 
[INFO ] 2025-05-29 14:16:46.770 - [sqlserver_pg][sqlserver -ag1] - building CT table for table SourceOfRegion2 
[INFO ] 2025-05-29 14:16:47.322 - [sqlserver_pg][sqlserver -ag1] - Use existing stream offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 14:16:47.395 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from 3 tables 
[TRACE] 2025-05-29 14:16:47.402 - [sqlserver_pg][sqlserver -ag1] - Initial sync started 
[INFO ] 2025-05-29 14:16:47.402 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-05-29 14:16:47.602 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-05-29 14:16:47.807 - [sqlserver_pg][sqlserver -ag1] - Query snapshot row size completed: sqlserver -ag1(1262911e-dca9-4c94-93cf-597bee433e76) 
[INFO ] 2025-05-29 14:16:47.885 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion has been completed batch read 
[INFO ] 2025-05-29 14:16:47.885 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from table: SourceOfRegion3 
[TRACE] 2025-05-29 14:16:47.886 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion3 is going to be initial synced 
[INFO ] 2025-05-29 14:16:48.179 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion3 has been completed batch read 
[INFO ] 2025-05-29 14:16:48.179 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from table: SourceOfRegion2 
[TRACE] 2025-05-29 14:16:48.382 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion2 is going to be initial synced 
[INFO ] 2025-05-29 14:16:48.489 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion2 has been completed batch read 
[TRACE] 2025-05-29 14:16:48.489 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[INFO ] 2025-05-29 14:16:48.490 - [sqlserver_pg][sqlserver -ag1] - Batch read completed. 
[TRACE] 2025-05-29 14:16:48.490 - [sqlserver_pg][sqlserver -ag1] - Incremental sync starting... 
[TRACE] 2025-05-29 14:16:48.490 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[TRACE] 2025-05-29 14:16:48.491 - [sqlserver_pg][sqlserver -ag1] - Starting stream read, table list: [SourceOfRegion, SourceOfRegion3, SourceOfRegion2], offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 14:16:48.491 - [sqlserver_pg][sqlserver -ag1] - Starting incremental sync using database log parser 
[INFO ] 2025-05-29 14:16:48.658 - [sqlserver_pg][sqlserver -ag1] - opened cdc tables: [RefreshToken, SourceOfRegion, SourceOfRegion3, SourceOfRegion2] 
[INFO ] 2025-05-29 14:16:49.400 - [sqlserver_pg][sqlserver -ag1] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-05-29 14:16:49.401 - [sqlserver_pg][sqlserver -ag1] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion, SourceOfRegion3, SourceOfRegion2], data change syncing 
[TRACE] 2025-05-29 14:16:49.439 - [sqlserver_pg][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[TRACE] 2025-05-29 14:16:49.439 - [sqlserver_pg][local_pg] - Process after table "SourceOfRegion3" initial sync finished, cost: 0 ms 
[TRACE] 2025-05-29 14:16:49.439 - [sqlserver_pg][local_pg] - Process after table "SourceOfRegion2" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-29 14:16:49.644 - [sqlserver_pg][local_pg] - Process after all table(s) initial sync are finished，table number: 3 
[TRACE] 2025-05-29 14:20:37.837 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] running status set to false 
[TRACE] 2025-05-29 14:20:38.043 - [sqlserver_pg][sqlserver -ag1] - Incremental sync completed 
[TRACE] 2025-05-29 14:20:40.846 - [sqlserver_pg][sqlserver -ag1] - PDK connector node stopped: HazelcastSourcePdkDataNode_1262911e-dca9-4c94-93cf-597bee433e76_1748499404634 
[TRACE] 2025-05-29 14:20:40.847 - [sqlserver_pg][sqlserver -ag1] - PDK connector node released: HazelcastSourcePdkDataNode_1262911e-dca9-4c94-93cf-597bee433e76_1748499404634 
[TRACE] 2025-05-29 14:20:40.847 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] schema data cleaned 
[TRACE] 2025-05-29 14:20:40.848 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] monitor closed 
[TRACE] 2025-05-29 14:20:40.848 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] close complete, cost 3013 ms 
[TRACE] 2025-05-29 14:20:40.848 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] running status set to false 
[TRACE] 2025-05-29 14:20:40.858 - [sqlserver_pg][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4_1748499404672 
[TRACE] 2025-05-29 14:20:40.858 - [sqlserver_pg][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4_1748499404672 
[TRACE] 2025-05-29 14:20:40.858 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] schema data cleaned 
[TRACE] 2025-05-29 14:20:40.858 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] monitor closed 
[TRACE] 2025-05-29 14:20:40.858 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] close complete, cost 10 ms 
[TRACE] 2025-05-29 14:20:46.131 - [sqlserver_pg] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-29 14:20:46.940 - [sqlserver_pg] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@621b1412 
[TRACE] 2025-05-29 14:20:46.944 - [sqlserver_pg] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@466b2b0c 
[TRACE] 2025-05-29 14:20:46.944 - [sqlserver_pg] - Stop task milestones: 6835768578d7e01e143c5201(sqlserver_pg)  
[TRACE] 2025-05-29 14:20:47.064 - [sqlserver_pg] - Stopped task aspect(s) 
[TRACE] 2025-05-29 14:20:47.064 - [sqlserver_pg] - Snapshot order controller have been removed 
[INFO ] 2025-05-29 14:20:47.064 - [sqlserver_pg] - Task stopped. 
[TRACE] 2025-05-29 14:20:47.113 - [sqlserver_pg] - Remove memory task client succeed, task: sqlserver_pg[6835768578d7e01e143c5201] 
[TRACE] 2025-05-29 14:20:47.116 - [sqlserver_pg] - Destroy memory task client cache succeed, task: sqlserver_pg[6835768578d7e01e143c5201] 
[TRACE] 2025-05-29 14:21:00.625 - [sqlserver_pg] - Task initialization... 
[TRACE] 2025-05-29 14:21:00.737 - [sqlserver_pg] - Start task milestones: 6835768578d7e01e143c5201(sqlserver_pg) 
[INFO ] 2025-05-29 14:21:00.737 - [sqlserver_pg] - Loading table structure completed 
[TRACE] 2025-05-29 14:21:00.820 - [sqlserver_pg] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-29 14:21:00.820 - [sqlserver_pg] - The engine receives sqlserver_pg task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-29 14:21:00.866 - [sqlserver_pg] - Task started 
[TRACE] 2025-05-29 14:21:00.866 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] start preload schema,table counts: 3 
[TRACE] 2025-05-29 14:21:00.866 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] start preload schema,table counts: 3 
[TRACE] 2025-05-29 14:21:00.867 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] preload schema finished, cost 0 ms 
[TRACE] 2025-05-29 14:21:00.867 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] preload schema finished, cost 0 ms 
[INFO ] 2025-05-29 14:21:01.327 - [sqlserver_pg][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-05-29 14:21:01.327 - [sqlserver_pg][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-29 14:21:01.327 - [sqlserver_pg][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-29 14:21:01.345 - [sqlserver_pg][local_pg] - Apply table structure to target database 
[TRACE] 2025-05-29 14:21:01.345 - [sqlserver_pg][local_pg] - The table SourceOfRegion has already exist. 
[TRACE] 2025-05-29 14:21:01.413 - [sqlserver_pg][local_pg] - The table SourceOfRegion3 has already exist. 
[TRACE] 2025-05-29 14:21:01.413 - [sqlserver_pg][local_pg] - The table SourceOfRegion2 has already exist. 
[INFO ] 2025-05-29 14:21:01.548 - [sqlserver_pg][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-29 14:21:01.549 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" read batch size: 100 
[TRACE] 2025-05-29 14:21:01.549 - [sqlserver_pg][sqlserver -ag1] - Source node "sqlserver -ag1" event queue capacity: 200 
[TRACE] 2025-05-29 14:21:01.549 - [sqlserver_pg][sqlserver -ag1] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-29 14:21:01.706 - [sqlserver_pg][sqlserver -ag1] - Use existing stream offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 14:21:01.780 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from 3 tables 
[TRACE] 2025-05-29 14:21:01.784 - [sqlserver_pg][sqlserver -ag1] - Initial sync started 
[INFO ] 2025-05-29 14:21:01.784 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-05-29 14:21:01.987 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-05-29 14:21:02.014 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-05-29 14:21:02.014 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-05-29 14:21:02.026 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-05-29 14:21:02.026 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-05-29 14:21:02.035 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-05-29 14:21:02.035 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-05-29 14:21:02.044 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-05-29 14:21:02.044 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-05-29 14:21:02.057 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-05-29 14:21:02.057 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-05-29 14:21:02.062 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-05-29 14:21:02.062 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-05-29 14:21:02.266 - [sqlserver_pg][sqlserver -ag1] - Query snapshot row size completed: sqlserver -ag1(1262911e-dca9-4c94-93cf-597bee433e76) 
[INFO ] 2025-05-29 14:21:02.364 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion has been completed batch read 
[INFO ] 2025-05-29 14:21:02.364 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from table: SourceOfRegion3 
[TRACE] 2025-05-29 14:21:02.364 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion3 is going to be initial synced 
[TRACE] 2025-05-29 14:21:02.466 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-05-29 14:21:02.466 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-05-29 14:21:02.478 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-05-29 14:21:02.478 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-05-29 14:21:02.491 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-05-29 14:21:02.491 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-05-29 14:21:02.501 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-05-29 14:21:02.501 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-05-29 14:21:02.511 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-05-29 14:21:02.512 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-05-29 14:21:02.517 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-05-29 14:21:02.517 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion3' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-05-29 14:21:02.908 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion3 has been completed batch read 
[INFO ] 2025-05-29 14:21:02.909 - [sqlserver_pg][sqlserver -ag1] - Starting batch read from table: SourceOfRegion2 
[TRACE] 2025-05-29 14:21:02.909 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion2 is going to be initial synced 
[TRACE] 2025-05-29 14:21:03.021 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-05-29 14:21:03.022 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-05-29 14:21:03.034 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-05-29 14:21:03.034 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-05-29 14:21:03.045 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-05-29 14:21:03.045 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-05-29 14:21:03.057 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-05-29 14:21:03.057 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-05-29 14:21:03.066 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-05-29 14:21:03.066 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-05-29 14:21:03.071 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-05-29 14:21:03.072 - [sqlserver_pg][local_pg] - Table 'SourceOfRegion2' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-05-29 14:21:03.358 - [sqlserver_pg][sqlserver -ag1] - Table SourceOfRegion2 has been completed batch read 
[TRACE] 2025-05-29 14:21:03.359 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[INFO ] 2025-05-29 14:21:03.359 - [sqlserver_pg][sqlserver -ag1] - Batch read completed. 
[TRACE] 2025-05-29 14:21:03.359 - [sqlserver_pg][sqlserver -ag1] - Incremental sync starting... 
[TRACE] 2025-05-29 14:21:03.359 - [sqlserver_pg][sqlserver -ag1] - Initial sync completed 
[TRACE] 2025-05-29 14:21:03.359 - [sqlserver_pg][sqlserver -ag1] - Starting stream read, table list: [SourceOfRegion, SourceOfRegion3, SourceOfRegion2], offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 14:21:03.359 - [sqlserver_pg][sqlserver -ag1] - Starting incremental sync using database log parser 
[INFO ] 2025-05-29 14:21:03.562 - [sqlserver_pg][sqlserver -ag1] - opened cdc tables: [RefreshToken, SourceOfRegion, SourceOfRegion3, SourceOfRegion2] 
[INFO ] 2025-05-29 14:21:04.149 - [sqlserver_pg][sqlserver -ag1] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-05-29 14:21:04.151 - [sqlserver_pg][sqlserver -ag1] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion, SourceOfRegion3, SourceOfRegion2], data change syncing 
[TRACE] 2025-05-29 14:21:04.306 - [sqlserver_pg][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[TRACE] 2025-05-29 14:21:04.306 - [sqlserver_pg][local_pg] - Process after table "SourceOfRegion2" initial sync finished, cost: 0 ms 
[TRACE] 2025-05-29 14:21:04.306 - [sqlserver_pg][local_pg] - Process after table "SourceOfRegion3" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-29 14:21:04.507 - [sqlserver_pg][local_pg] - Process after all table(s) initial sync are finished，table number: 3 
[TRACE] 2025-05-29 14:21:37.379 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] running status set to false 
[TRACE] 2025-05-29 14:21:38.198 - [sqlserver_pg][sqlserver -ag1] - Incremental sync completed 
[TRACE] 2025-05-29 14:21:40.391 - [sqlserver_pg][sqlserver -ag1] - PDK connector node stopped: HazelcastSourcePdkDataNode_1262911e-dca9-4c94-93cf-597bee433e76_1748499661201 
[TRACE] 2025-05-29 14:21:40.391 - [sqlserver_pg][sqlserver -ag1] - PDK connector node released: HazelcastSourcePdkDataNode_1262911e-dca9-4c94-93cf-597bee433e76_1748499661201 
[TRACE] 2025-05-29 14:21:40.392 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] schema data cleaned 
[TRACE] 2025-05-29 14:21:40.392 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] monitor closed 
[TRACE] 2025-05-29 14:21:40.392 - [sqlserver_pg][sqlserver -ag1] - Node sqlserver -ag1[1262911e-dca9-4c94-93cf-597bee433e76] close complete, cost 3014 ms 
[TRACE] 2025-05-29 14:21:40.407 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] running status set to false 
[TRACE] 2025-05-29 14:21:40.407 - [sqlserver_pg][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4_1748499661181 
[TRACE] 2025-05-29 14:21:40.407 - [sqlserver_pg][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4_1748499661181 
[TRACE] 2025-05-29 14:21:40.407 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] schema data cleaned 
[TRACE] 2025-05-29 14:21:40.408 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] monitor closed 
[TRACE] 2025-05-29 14:21:40.408 - [sqlserver_pg][local_pg] - Node local_pg[0572de1d-affd-4b6c-a2ab-f33a0cdb6bd4] close complete, cost 15 ms 
[TRACE] 2025-05-29 14:21:47.169 - [sqlserver_pg] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-29 14:21:48.174 - [sqlserver_pg] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@32c6570e 
[TRACE] 2025-05-29 14:21:48.178 - [sqlserver_pg] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@15560181 
[TRACE] 2025-05-29 14:21:48.179 - [sqlserver_pg] - Stop task milestones: 6835768578d7e01e143c5201(sqlserver_pg)  
[TRACE] 2025-05-29 14:21:48.297 - [sqlserver_pg] - Stopped task aspect(s) 
[TRACE] 2025-05-29 14:21:48.297 - [sqlserver_pg] - Snapshot order controller have been removed 
[INFO ] 2025-05-29 14:21:48.297 - [sqlserver_pg] - Task stopped. 
[TRACE] 2025-05-29 14:21:48.337 - [sqlserver_pg] - Remove memory task client succeed, task: sqlserver_pg[6835768578d7e01e143c5201] 
[TRACE] 2025-05-29 14:21:48.340 - [sqlserver_pg] - Destroy memory task client cache succeed, task: sqlserver_pg[6835768578d7e01e143c5201] 
