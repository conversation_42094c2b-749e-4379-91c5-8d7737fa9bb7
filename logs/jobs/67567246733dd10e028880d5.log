[INFO ] 2024-12-09 12:30:10.008 - [R02TableEditorTask] - Start task milestones: 67567246733dd10e028880d5(R02TableEditorTask) 
[INFO ] 2024-12-09 12:30:10.009 - [R02TableEditorTask] - Task initialization... 
[INFO ] 2024-12-09 12:30:10.284 - [R02TableEditorTask] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-09 12:30:10.284 - [R02TableEditorTask] - The engine receives R02TableEditorTask task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-09 12:30:10.376 - [R02TableEditorTask][AutoTestMongo] - Node AutoTestMongo[8cc83385-ab1e-4607-9201-678edb8d0356] start preload schema,table counts: 1 
[INFO ] 2024-12-09 12:30:10.377 - [R02TableEditorTask][AutoTestMongo] - Node AutoTestMongo[8cc83385-ab1e-4607-9201-678edb8d0356] preload schema finished, cost 0 ms 
[INFO ] 2024-12-09 12:30:10.385 - [R02TableEditorTask][AutoTestMySQL] - Node AutoTestMySQL[fe741cda-413f-4038-9fb1-b2fd87084055] start preload schema,table counts: 1 
[INFO ] 2024-12-09 12:30:10.386 - [R02TableEditorTask][AutoTestMySQL] - Node AutoTestMySQL[fe741cda-413f-4038-9fb1-b2fd87084055] preload schema finished, cost 0 ms 
[INFO ] 2024-12-09 12:30:10.395 - [R02TableEditorTask][Table Editor] - Node Table Editor[83d1da87-9f2a-452a-9ec6-0a1939639580] start preload schema,table counts: 1 
[INFO ] 2024-12-09 12:30:10.395 - [R02TableEditorTask][Table Editor] - Node Table Editor[83d1da87-9f2a-452a-9ec6-0a1939639580] preload schema finished, cost 0 ms 
[INFO ] 2024-12-09 12:30:10.602 - [R02TableEditorTask][Table Editor] - Node table_rename_processor(Table Editor: 83d1da87-9f2a-452a-9ec6-0a1939639580) enable batch process 
[INFO ] 2024-12-09 12:30:10.869 - [R02TableEditorTask][AutoTestMongo] - Source node "AutoTestMongo" read batch size: 100 
[INFO ] 2024-12-09 12:30:10.870 - [R02TableEditorTask][AutoTestMongo] - Source node "AutoTestMongo" event queue capacity: 200 
[INFO ] 2024-12-09 12:30:11.071 - [R02TableEditorTask][AutoTestMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-09 12:30:11.164 - [R02TableEditorTask][AutoTestMongo] - batch offset found: {},stream offset found: {"cdcOffset":1733718610,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-09 12:30:11.165 - [R02TableEditorTask][AutoTestMongo] - Initial sync started 
[INFO ] 2024-12-09 12:30:11.165 - [R02TableEditorTask][AutoTestMongo] - Starting batch read, table name: R02TableEditor 
[INFO ] 2024-12-09 12:30:11.165 - [R02TableEditorTask][AutoTestMongo] - Table R02TableEditor is going to be initial synced 
[INFO ] 2024-12-09 12:30:11.171 - [R02TableEditorTask][AutoTestMongo] - Query snapshot row size completed: AutoTestMongo(8cc83385-ab1e-4607-9201-678edb8d0356) 
[INFO ] 2024-12-09 12:30:11.193 - [R02TableEditorTask][AutoTestMongo] - Table [R02TableEditor] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-09 12:30:11.193 - [R02TableEditorTask][AutoTestMongo] - Initial sync completed 
[INFO ] 2024-12-09 12:30:11.196 - [R02TableEditorTask][AutoTestMongo] - Incremental sync starting... 
[INFO ] 2024-12-09 12:30:11.196 - [R02TableEditorTask][AutoTestMongo] - Initial sync completed 
[INFO ] 2024-12-09 12:30:11.204 - [R02TableEditorTask][AutoTestMongo] - Starting stream read, table list: [R02TableEditor], offset: {"cdcOffset":1733718610,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-09 12:30:11.269 - [R02TableEditorTask][AutoTestMongo] - Connector MongoDB incremental start succeed, tables: [R02TableEditor], data change syncing 
[INFO ] 2024-12-09 12:30:11.273 - [R02TableEditorTask][AutoTestMySQL] - Node(AutoTestMySQL) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-09 12:30:11.274 - [R02TableEditorTask][AutoTestMySQL] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-09 12:30:11.336 - [R02TableEditorTask][AutoTestMySQL] - Table "autoTest.R02TableEditor" exists, skip auto create table 
[INFO ] 2024-12-09 12:30:11.338 - [R02TableEditorTask][AutoTestMySQL] - The table R02TableEditor has already exist. 
[INFO ] 2024-12-09 12:30:20.459 - [R02TableEditorTask][AutoTestMongo] - Node AutoTestMongo[8cc83385-ab1e-4607-9201-678edb8d0356] running status set to false 
[INFO ] 2024-12-09 12:30:20.479 - [R02TableEditorTask][AutoTestMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode_8cc83385-ab1e-4607-9201-678edb8d0356_1733718610658 
[INFO ] 2024-12-09 12:30:20.485 - [R02TableEditorTask][AutoTestMongo] - PDK connector node released: HazelcastSourcePdkDataNode_8cc83385-ab1e-4607-9201-678edb8d0356_1733718610658 
[INFO ] 2024-12-09 12:30:20.485 - [R02TableEditorTask][AutoTestMongo] - Node AutoTestMongo[8cc83385-ab1e-4607-9201-678edb8d0356] schema data cleaned 
[INFO ] 2024-12-09 12:30:20.486 - [R02TableEditorTask][AutoTestMongo] - Node AutoTestMongo[8cc83385-ab1e-4607-9201-678edb8d0356] monitor closed 
[INFO ] 2024-12-09 12:30:20.488 - [R02TableEditorTask][AutoTestMongo] - Node AutoTestMongo[8cc83385-ab1e-4607-9201-678edb8d0356] close complete, cost 45 ms 
[INFO ] 2024-12-09 12:30:20.488 - [R02TableEditorTask][Table Editor] - Node Table Editor[83d1da87-9f2a-452a-9ec6-0a1939639580] running status set to false 
[INFO ] 2024-12-09 12:30:20.492 - [R02TableEditorTask][Table Editor] - Node Table Editor[83d1da87-9f2a-452a-9ec6-0a1939639580] schema data cleaned 
[INFO ] 2024-12-09 12:30:20.492 - [R02TableEditorTask][Table Editor] - Node Table Editor[83d1da87-9f2a-452a-9ec6-0a1939639580] monitor closed 
[INFO ] 2024-12-09 12:30:20.493 - [R02TableEditorTask][Table Editor] - Node Table Editor[83d1da87-9f2a-452a-9ec6-0a1939639580] close complete, cost 5 ms 
[INFO ] 2024-12-09 12:30:20.589 - [R02TableEditorTask][AutoTestMySQL] - Node AutoTestMySQL[fe741cda-413f-4038-9fb1-b2fd87084055] running status set to false 
[INFO ] 2024-12-09 12:30:20.594 - [R02TableEditorTask][AutoTestMySQL] - PDK connector node stopped: HazelcastTargetPdkDataNode_fe741cda-413f-4038-9fb1-b2fd87084055_1733718610671 
[INFO ] 2024-12-09 12:30:20.595 - [R02TableEditorTask][AutoTestMySQL] - PDK connector node released: HazelcastTargetPdkDataNode_fe741cda-413f-4038-9fb1-b2fd87084055_1733718610671 
[INFO ] 2024-12-09 12:30:20.595 - [R02TableEditorTask][AutoTestMySQL] - Node AutoTestMySQL[fe741cda-413f-4038-9fb1-b2fd87084055] schema data cleaned 
[INFO ] 2024-12-09 12:30:20.595 - [R02TableEditorTask][AutoTestMySQL] - Node AutoTestMySQL[fe741cda-413f-4038-9fb1-b2fd87084055] monitor closed 
[INFO ] 2024-12-09 12:30:20.802 - [R02TableEditorTask][AutoTestMySQL] - Node AutoTestMySQL[fe741cda-413f-4038-9fb1-b2fd87084055] close complete, cost 101 ms 
[INFO ] 2024-12-09 12:30:21.209 - [R02TableEditorTask][AutoTestMongo] - Incremental sync completed 
[INFO ] 2024-12-09 12:30:25.350 - [R02TableEditorTask] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-09 12:30:25.353 - [R02TableEditorTask] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6623e7cf 
[INFO ] 2024-12-09 12:30:25.353 - [R02TableEditorTask] - Stop task milestones: 67567246733dd10e028880d5(R02TableEditorTask)  
[INFO ] 2024-12-09 12:30:25.471 - [R02TableEditorTask] - Stopped task aspect(s) 
[INFO ] 2024-12-09 12:30:25.471 - [R02TableEditorTask] - Snapshot order controller have been removed 
[INFO ] 2024-12-09 12:30:25.564 - [R02TableEditorTask] - Remove memory task client succeed, task: R02TableEditorTask[67567246733dd10e028880d5] 
[INFO ] 2024-12-09 12:30:25.564 - [R02TableEditorTask] - Destroy memory task client cache succeed, task: R02TableEditorTask[67567246733dd10e028880d5] 
