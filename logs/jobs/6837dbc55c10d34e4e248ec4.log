[TRACE] 2025-05-29 12:01:05.862 - [任务 3] - Task initialization... 
[TRACE] 2025-05-29 12:01:05.934 - [任务 3] - Start task milestones: 6837dbc55c10d34e4e248ec4(任务 3) 
[INFO ] 2025-05-29 12:01:05.934 - [任务 3] - Loading table structure completed 
[TRACE] 2025-05-29 12:01:06.108 - [任务 3] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-05-29 12:01:06.109 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-29 12:01:06.128 - [任务 3] - Task started 
[TRACE] 2025-05-29 12:01:06.128 - [任务 3][sybase_sa -test_db] - Node sybase_sa -test_db[b299026f-6e83-4f3b-a365-a86cdd43674a] start preload schema,table counts: 1 
[TRACE] 2025-05-29 12:01:06.128 - [任务 3][sqlserver -ag1] - Node sqlserver -ag1[64e7fa68-5adc-4daf-a53f-a34c4de372e0] start preload schema,table counts: 1 
[TRACE] 2025-05-29 12:01:06.129 - [任务 3][sybase_sa -test_db] - Node sybase_sa -test_db[b299026f-6e83-4f3b-a365-a86cdd43674a] preload schema finished, cost 0 ms 
[TRACE] 2025-05-29 12:01:06.129 - [任务 3][sqlserver -ag1] - Node sqlserver -ag1[64e7fa68-5adc-4daf-a53f-a34c4de372e0] preload schema finished, cost 0 ms 
[INFO ] 2025-05-29 12:01:06.508 - [任务 3][sybase_sa -test_db] - Sink connector(sybase_sa -test_db) initialization completed 
[TRACE] 2025-05-29 12:01:06.508 - [任务 3][sybase_sa -test_db] - Node(sybase_sa -test_db) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-29 12:01:06.509 - [任务 3][sybase_sa -test_db] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-29 12:01:06.712 - [任务 3][sybase_sa -test_db] - Apply table structure to target database 
[INFO ] 2025-05-29 12:01:06.740 - [任务 3][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-05-29 12:01:06.740 - [任务 3][sqlserver -ag1] - Source node "sqlserver -ag1" read batch size: 100 
[TRACE] 2025-05-29 12:01:06.740 - [任务 3][sqlserver -ag1] - Source node "sqlserver -ag1" event queue capacity: 200 
[TRACE] 2025-05-29 12:01:06.740 - [任务 3][sqlserver -ag1] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-29 12:01:07.098 - [任务 3][sqlserver -ag1] - Use existing stream offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 12:01:07.098 - [任务 3][sqlserver -ag1] - Starting batch read from 1 tables 
[TRACE] 2025-05-29 12:01:07.101 - [任务 3][sqlserver -ag1] - Initial sync started 
[INFO ] 2025-05-29 12:01:07.101 - [任务 3][sqlserver -ag1] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-05-29 12:01:07.188 - [任务 3][sqlserver -ag1] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-05-29 12:01:07.188 - [任务 3][sqlserver -ag1] - Query snapshot row size completed: sqlserver -ag1(64e7fa68-5adc-4daf-a53f-a34c4de372e0) 
[INFO ] 2025-05-29 12:01:07.294 - [任务 3][sqlserver -ag1] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-05-29 12:01:07.294 - [任务 3][sqlserver -ag1] - Initial sync completed 
[INFO ] 2025-05-29 12:01:07.294 - [任务 3][sqlserver -ag1] - Batch read completed. 
[TRACE] 2025-05-29 12:01:07.295 - [任务 3][sqlserver -ag1] - Incremental sync starting... 
[TRACE] 2025-05-29 12:01:07.295 - [任务 3][sqlserver -ag1] - Initial sync completed 
[TRACE] 2025-05-29 12:01:07.295 - [任务 3][sqlserver -ag1] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000002400000C600004","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-05-29 12:01:07.295 - [任务 3][sqlserver -ag1] - Starting incremental sync using database log parser 
[INFO ] 2025-05-29 12:01:07.498 - [任务 3][sqlserver -ag1] - opened cdc tables: [RefreshToken, SourceOfRegion] 
[INFO ] 2025-05-29 12:01:07.654 - [任务 3][sqlserver -ag1] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-05-29 12:01:07.681 - [任务 3][sqlserver -ag1] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-05-29 12:01:07.681 - [任务 3][sybase_sa -test_db] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-29 12:01:07.681 - [任务 3][sybase_sa -test_db] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-05-29 12:13:46.703 - [任务 3][sqlserver -ag1] - Node sqlserver -ag1[64e7fa68-5adc-4daf-a53f-a34c4de372e0] running status set to false 
[TRACE] 2025-05-29 12:13:47.319 - [任务 3][sqlserver -ag1] - Incremental sync completed 
[TRACE] 2025-05-29 12:13:49.638 - [任务 3][sqlserver -ag1] - PDK connector node stopped: HazelcastSourcePdkDataNode_64e7fa68-5adc-4daf-a53f-a34c4de372e0_1748491266399 
[TRACE] 2025-05-29 12:13:49.638 - [任务 3][sqlserver -ag1] - PDK connector node released: HazelcastSourcePdkDataNode_64e7fa68-5adc-4daf-a53f-a34c4de372e0_1748491266399 
[TRACE] 2025-05-29 12:13:49.639 - [任务 3][sqlserver -ag1] - Node sqlserver -ag1[64e7fa68-5adc-4daf-a53f-a34c4de372e0] schema data cleaned 
[TRACE] 2025-05-29 12:13:49.640 - [任务 3][sqlserver -ag1] - Node sqlserver -ag1[64e7fa68-5adc-4daf-a53f-a34c4de372e0] monitor closed 
[TRACE] 2025-05-29 12:13:49.640 - [任务 3][sqlserver -ag1] - Node sqlserver -ag1[64e7fa68-5adc-4daf-a53f-a34c4de372e0] close complete, cost 3014 ms 
[TRACE] 2025-05-29 12:13:49.739 - [任务 3][sybase_sa -test_db] - Node sybase_sa -test_db[b299026f-6e83-4f3b-a365-a86cdd43674a] running status set to false 
[TRACE] 2025-05-29 12:13:49.739 - [任务 3][sybase_sa -test_db] - PDK connector node stopped: HazelcastTargetPdkDataNode_b299026f-6e83-4f3b-a365-a86cdd43674a_1748491266433 
[TRACE] 2025-05-29 12:13:49.740 - [任务 3][sybase_sa -test_db] - PDK connector node released: HazelcastTargetPdkDataNode_b299026f-6e83-4f3b-a365-a86cdd43674a_1748491266433 
[TRACE] 2025-05-29 12:13:49.740 - [任务 3][sybase_sa -test_db] - Node sybase_sa -test_db[b299026f-6e83-4f3b-a365-a86cdd43674a] schema data cleaned 
[TRACE] 2025-05-29 12:13:49.740 - [任务 3][sybase_sa -test_db] - Node sybase_sa -test_db[b299026f-6e83-4f3b-a365-a86cdd43674a] monitor closed 
[TRACE] 2025-05-29 12:13:49.741 - [任务 3][sybase_sa -test_db] - Node sybase_sa -test_db[b299026f-6e83-4f3b-a365-a86cdd43674a] close complete, cost 100 ms 
[TRACE] 2025-05-29 12:13:56.665 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-29 12:13:57.670 - [任务 3] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@3fd9816e 
[TRACE] 2025-05-29 12:13:57.672 - [任务 3] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6e329259 
[TRACE] 2025-05-29 12:13:57.673 - [任务 3] - Stop task milestones: 6837dbc55c10d34e4e248ec4(任务 3)  
[TRACE] 2025-05-29 12:13:57.794 - [任务 3] - Stopped task aspect(s) 
[TRACE] 2025-05-29 12:13:57.795 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2025-05-29 12:13:57.795 - [任务 3] - Task stopped. 
[TRACE] 2025-05-29 12:13:57.859 - [任务 3] - Remove memory task client succeed, task: 任务 3[6837dbc55c10d34e4e248ec4] 
[TRACE] 2025-05-29 12:13:58.065 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[6837dbc55c10d34e4e248ec4] 
