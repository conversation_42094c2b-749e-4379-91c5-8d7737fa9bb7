[INFO ] 2024-12-09 19:04:09.946 - [R06TimeCalcTask] - Start task milestones: 6756ce97f3d6246751110704(R06TimeCalcTask) 
[INFO ] 2024-12-09 19:04:10.153 - [R06TimeCalcTask] - Task initialization... 
[INFO ] 2024-12-09 19:04:11.341 - [R06TimeCalcTask] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-12-09 19:04:11.507 - [R06TimeCalcTask] - The engine receives R06TimeCalcTask task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-09 19:04:12.351 - [R06TimeCalcTask] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-09 19:04:12.469 - [R06TimeCalcTask] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@68d491b 
[INFO ] 2024-12-09 19:04:12.469 - [R06TimeCalcTask] - Stop task milestones: 6756ce97f3d6246751110704(R06TimeCalcTask)  
[INFO ] 2024-12-09 19:04:12.504 - [R06TimeCalcTask] - Stopped task aspect(s) 
[INFO ] 2024-12-09 19:04:12.504 - [R06TimeCalcTask] - Snapshot order controller have been removed 
[INFO ] 2024-12-09 19:04:12.575 - [R06TimeCalcTask] - Remove memory task client succeed, task: R06TimeCalcTask[6756ce97f3d6246751110704] 
[INFO ] 2024-12-09 19:04:12.575 - [R06TimeCalcTask] - Destroy memory task client cache succeed, task: R06TimeCalcTask[6756ce97f3d6246751110704] 
