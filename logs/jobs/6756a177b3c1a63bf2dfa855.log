[INFO ] 2024-12-09 15:51:35.468 - [R01NoProcessorTask] - Task initialization... 
[INFO ] 2024-12-09 15:51:35.470 - [R01NoProcessorTask] - Start task milestones: 6756a177b3c1a63bf2dfa855(R01NoProcessorTask) 
[INFO ] 2024-12-09 15:51:37.503 - [R01NoProcessorTask] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-12-09 15:51:37.664 - [R01NoProcessorTask] - The engine receives R01NoProcessorTask task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-09 15:51:37.906 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[9ded299a-3126-432e-a45b-bce34c6cc20a] start preload schema,table counts: 1 
[INFO ] 2024-12-09 15:51:37.906 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[9ded299a-3126-432e-a45b-bce34c6cc20a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-09 15:51:38.110 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[d2b234f9-9d1e-4b4d-be75-fb9587d30bf7] start preload schema,table counts: 1 
[INFO ] 2024-12-09 15:51:38.110 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[d2b234f9-9d1e-4b4d-be75-fb9587d30bf7] preload schema finished, cost 0 ms 
[INFO ] 2024-12-09 15:51:38.594 - [R01NoProcessorTask][AutoTestMongo] - Source node "AutoTestMongo" read batch size: 100 
[INFO ] 2024-12-09 15:51:38.594 - [R01NoProcessorTask][AutoTestMongo] - Source node "AutoTestMongo" event queue capacity: 200 
[INFO ] 2024-12-09 15:51:38.595 - [R01NoProcessorTask][AutoTestMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-09 15:51:38.597 - [R01NoProcessorTask][AutoTestMongo] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-09 15:51:39.802 - [R01NoProcessorTask][AutoTestMySQL] - Node(AutoTestMySQL) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-09 15:51:39.866 - [R01NoProcessorTask][AutoTestMySQL] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-09 15:51:40.037 - [R01NoProcessorTask][AutoTestMySQL] - Table "autoTest.R01NoProcessor" exists, skip auto create table 
[INFO ] 2024-12-09 15:51:40.037 - [R01NoProcessorTask][AutoTestMySQL] - The table R01NoProcessor has already exist. 
[INFO ] 2024-12-09 15:51:40.317 - [R01NoProcessorTask][AutoTestMongo] - Initial sync started 
[INFO ] 2024-12-09 15:51:40.320 - [R01NoProcessorTask][AutoTestMongo] - Starting batch read, table name: R01NoProcessor 
[INFO ] 2024-12-09 15:51:40.374 - [R01NoProcessorTask][AutoTestMongo] - Table R01NoProcessor is going to be initial synced 
[INFO ] 2024-12-09 15:51:40.374 - [R01NoProcessorTask][AutoTestMongo] - Query snapshot row size completed: AutoTestMongo(9ded299a-3126-432e-a45b-bce34c6cc20a) 
[INFO ] 2024-12-09 15:51:40.579 - [R01NoProcessorTask][AutoTestMongo] - Table [R01NoProcessor] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-09 15:51:40.582 - [R01NoProcessorTask][AutoTestMongo] - Initial sync completed 
[INFO ] 2024-12-09 15:51:46.034 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[9ded299a-3126-432e-a45b-bce34c6cc20a] running status set to false 
[INFO ] 2024-12-09 15:51:46.065 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[d2b234f9-9d1e-4b4d-be75-fb9587d30bf7] running status set to false 
[INFO ] 2024-12-09 15:51:46.177 - [R01NoProcessorTask][AutoTestMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode_9ded299a-3126-432e-a45b-bce34c6cc20a_1733730698227 
[INFO ] 2024-12-09 15:51:46.178 - [R01NoProcessorTask][AutoTestMongo] - PDK connector node released: HazelcastSourcePdkDataNode_9ded299a-3126-432e-a45b-bce34c6cc20a_1733730698227 
[INFO ] 2024-12-09 15:51:46.179 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[9ded299a-3126-432e-a45b-bce34c6cc20a] schema data cleaned 
[INFO ] 2024-12-09 15:51:46.217 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[9ded299a-3126-432e-a45b-bce34c6cc20a] monitor closed 
[INFO ] 2024-12-09 15:51:46.217 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[9ded299a-3126-432e-a45b-bce34c6cc20a] close complete, cost 206 ms 
[INFO ] 2024-12-09 15:51:46.238 - [R01NoProcessorTask][AutoTestMySQL] - PDK connector node stopped: HazelcastTargetPdkDataNode_d2b234f9-9d1e-4b4d-be75-fb9587d30bf7_1733730698236 
[INFO ] 2024-12-09 15:51:46.238 - [R01NoProcessorTask][AutoTestMySQL] - PDK connector node released: HazelcastTargetPdkDataNode_d2b234f9-9d1e-4b4d-be75-fb9587d30bf7_1733730698236 
[INFO ] 2024-12-09 15:51:46.238 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[d2b234f9-9d1e-4b4d-be75-fb9587d30bf7] schema data cleaned 
[INFO ] 2024-12-09 15:51:46.239 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[d2b234f9-9d1e-4b4d-be75-fb9587d30bf7] monitor closed 
[INFO ] 2024-12-09 15:51:46.239 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[d2b234f9-9d1e-4b4d-be75-fb9587d30bf7] close complete, cost 220 ms 
[INFO ] 2024-12-09 15:51:47.545 - [R01NoProcessorTask] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-09 15:51:47.545 - [R01NoProcessorTask] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@40d8fe81 
[INFO ] 2024-12-09 15:51:47.748 - [R01NoProcessorTask] - Stop task milestones: 6756a177b3c1a63bf2dfa855(R01NoProcessorTask)  
[INFO ] 2024-12-09 15:51:47.760 - [R01NoProcessorTask] - Stopped task aspect(s) 
[INFO ] 2024-12-09 15:51:47.761 - [R01NoProcessorTask] - Snapshot order controller have been removed 
[INFO ] 2024-12-09 15:51:47.918 - [R01NoProcessorTask] - Remove memory task client succeed, task: R01NoProcessorTask[6756a177b3c1a63bf2dfa855] 
[INFO ] 2024-12-09 15:51:47.919 - [R01NoProcessorTask] - Destroy memory task client cache succeed, task: R01NoProcessorTask[6756a177b3c1a63bf2dfa855] 
