[TRACE] 2025-01-20 14:16:02.042 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Task initialization... 
[TRACE] 2025-01-20 14:16:02.043 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Start task milestones: 678dea170dcc7213047a70cd(t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728) 
[INFO ] 2025-01-20 14:16:02.247 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Loading table structure completed 
[TRACE] 2025-01-20 14:16:02.253 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-01-20 14:16:02.286 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - The engine receives t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-01-20 14:16:02.356 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Task started 
[TRACE] 2025-01-20 14:16:02.434 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Node qa_pg_15_partition_1736997187904_1988[e7dfad96-ebeb-4926-a6c1-a0c6465eff82] start preload schema,table counts: 1 
[TRACE] 2025-01-20 14:16:02.435 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Node qa_sqlserver_2019_partition_1736997187904_1988[7db83e77-91c8-4aab-8f7a-6cc5a28819d2] start preload schema,table counts: 1 
[TRACE] 2025-01-20 14:16:02.435 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Node qa_pg_15_partition_1736997187904_1988[e7dfad96-ebeb-4926-a6c1-a0c6465eff82] preload schema finished, cost 0 ms 
[TRACE] 2025-01-20 14:16:02.436 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Node qa_sqlserver_2019_partition_1736997187904_1988[7db83e77-91c8-4aab-8f7a-6cc5a28819d2] preload schema finished, cost 0 ms 
[INFO ] 2025-01-20 14:16:02.465 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Enable partition table support for source database 
[INFO ] 2025-01-20 14:16:03.215 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Sink connector(qa_sqlserver_2019_partition_1736997187904_1988) initialization completed 
[TRACE] 2025-01-20 14:16:03.216 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Node(qa_sqlserver_2019_partition_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-01-20 14:16:03.216 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-20 14:16:03.220 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Enable partition table support for target database 
[INFO ] 2025-01-20 14:16:03.220 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-20 14:16:03.258 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Source connector(qa_pg_15_partition_1736997187904_1988) initialization completed 
[TRACE] 2025-01-20 14:16:03.258 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Source node "qa_pg_15_partition_1736997187904_1988" read batch size: 500 
[TRACE] 2025-01-20 14:16:03.259 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Source node "qa_pg_15_partition_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-01-20 14:16:03.259 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-01-20 14:16:03.459 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Will create master partition table [i91_6_t] to target, init sub partition list: [] 
[WARN ] 2025-01-20 14:16:03.544 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-01-20 14:16:03.544 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Unable find any partition info, can not create partition table, will create it before subpartition created. 
[INFO ] 2025-01-20 14:16:03.563 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - new logical replication slot created, slotName:tapdata_cdc_853f6936_08e6_495a_a9ed_4b78c6a867f6 
[INFO ] 2025-01-20 14:16:03.563 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-01-20 14:16:03.565 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-01-20 14:16:03.566 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2025-01-20 14:16:03.607 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-01-20 14:16:03.607 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Initial sync started 
[INFO ] 2025-01-20 14:16:03.608 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Starting batch read from table: i91_6_t 
[TRACE] 2025-01-20 14:16:03.608 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Table i91_6_t is going to be initial synced 
[TRACE] 2025-01-20 14:16:03.630 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Query snapshot row size completed: qa_pg_15_partition_1736997187904_1988(e7dfad96-ebeb-4926-a6c1-a0c6465eff82) 
[INFO ] 2025-01-20 14:16:03.630 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Table i91_6_t has been completed batch read 
[TRACE] 2025-01-20 14:16:03.630 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-01-20 14:16:03.630 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Batch read completed. 
[TRACE] 2025-01-20 14:16:03.631 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Incremental sync starting... 
[TRACE] 2025-01-20 14:16:03.631 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Initial sync completed 
[TRACE] 2025-01-20 14:16:03.631 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Starting stream read, table list: [i91_6_t, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-01-20 14:16:03.631 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Starting incremental sync using database log parser 
[WARN ] 2025-01-20 14:16:03.647 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-01-20 14:16:03.647 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Using an existing logical replication slot, slotName:tapdata_cdc_853f6936_08e6_495a_a9ed_4b78c6a867f6 
[TRACE] 2025-01-20 14:16:04.048 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Connector PostgreSQL incremental start succeed, tables: [i91_6_t, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2025-01-20 14:17:13.578 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Found new table(s): [i91_6_t_2023_q1, i91_6_t_2023_q2, i91_6_t_2023_q3, i91_6_t_2023_q4, i91_6_t] 
[TRACE] 2025-01-20 14:17:13.748 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Load new table(s) schema finished, loaded schema count: 4 
[TRACE] 2025-01-20 14:17:13.788 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@6de46d09: {"partitionMasterTableId":"i91_6_t","table":{"comment":"","id":"i91_6_t_2023_q1","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"name":"i91_6_t_2023_q1_pkey","primary":true,"unique":true}],"lastUpdate":1737353833750,"maxPKPos":2,"maxPos":3,"name":"i91_6_t_2023_q1","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":8,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-07-01') TO ('2023-10-01')","tableName":"i91_6_t_2023_q3","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-10-01') TO ('2024-01-01')","tableName":"i91_6_t_2023_q4","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-04-01')","tableName":"i91_6_t_2023_q1","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-04-01') TO ('2023-07-01')","tableName":"i91_6_t_2023_q2","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"i91_6_t"},"tableId":"i91_6_t_2023_q1","type":206} 
[TRACE] 2025-01-20 14:17:13.788 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Sync sub table's [i91_6_t_2023_q1] create table ddl, will add update master table [i91_6_t] metadata 
[TRACE] 2025-01-20 14:17:13.830 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_i91_6_t_2023_q1_678de9ec0dcc7213047a7015_678dea170dcc7213047a70cd 
[TRACE] 2025-01-20 14:17:13.830 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Create new table schema transform finished: TapTable id i91_6_t_2023_q1 name i91_6_t_2023_q1 storageEngine null charset null number of fields 3 
[TRACE] 2025-01-20 14:17:13.834 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@27a9a217: {"partitionMasterTableId":"i91_6_t","table":{"comment":"","id":"i91_6_t_2023_q2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"name":"i91_6_t_2023_q2_pkey","primary":true,"unique":true}],"lastUpdate":1737353833830,"maxPKPos":2,"maxPos":3,"name":"i91_6_t_2023_q2","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":8,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-07-01') TO ('2023-10-01')","tableName":"i91_6_t_2023_q3","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-10-01') TO ('2024-01-01')","tableName":"i91_6_t_2023_q4","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-04-01')","tableName":"i91_6_t_2023_q1","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-04-01') TO ('2023-07-01')","tableName":"i91_6_t_2023_q2","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"i91_6_t"},"tableId":"i91_6_t_2023_q2","type":206} 
[TRACE] 2025-01-20 14:17:13.834 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Sync sub table's [i91_6_t_2023_q2] create table ddl, will add update master table [i91_6_t] metadata 
[TRACE] 2025-01-20 14:17:13.863 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_i91_6_t_2023_q2_678de9ec0dcc7213047a7015_678dea170dcc7213047a70cd 
[TRACE] 2025-01-20 14:17:13.863 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Create new table schema transform finished: TapTable id i91_6_t_2023_q2 name i91_6_t_2023_q2 storageEngine null charset null number of fields 3 
[TRACE] 2025-01-20 14:17:13.866 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@59a34702: {"partitionMasterTableId":"i91_6_t","table":{"comment":"","id":"i91_6_t_2023_q3","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"name":"i91_6_t_2023_q3_pkey","primary":true,"unique":true}],"lastUpdate":1737353833863,"maxPKPos":2,"maxPos":3,"name":"i91_6_t_2023_q3","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":8,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-07-01') TO ('2023-10-01')","tableName":"i91_6_t_2023_q3","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-10-01') TO ('2024-01-01')","tableName":"i91_6_t_2023_q4","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-04-01')","tableName":"i91_6_t_2023_q1","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-04-01') TO ('2023-07-01')","tableName":"i91_6_t_2023_q2","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"i91_6_t"},"tableId":"i91_6_t_2023_q3","type":206} 
[TRACE] 2025-01-20 14:17:13.866 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Sync sub table's [i91_6_t_2023_q3] create table ddl, will add update master table [i91_6_t] metadata 
[TRACE] 2025-01-20 14:17:13.883 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_i91_6_t_2023_q3_678de9ec0dcc7213047a7015_678dea170dcc7213047a70cd 
[TRACE] 2025-01-20 14:17:13.883 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Create new table schema transform finished: TapTable id i91_6_t_2023_q3 name i91_6_t_2023_q3 storageEngine null charset null number of fields 3 
[TRACE] 2025-01-20 14:17:13.886 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@7456bc75: {"partitionMasterTableId":"i91_6_t","table":{"comment":"","id":"i91_6_t_2023_q4","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"name":"i91_6_t_2023_q4_pkey","primary":true,"unique":true}],"lastUpdate":1737353833883,"maxPKPos":2,"maxPos":3,"name":"i91_6_t_2023_q4","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":8,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-07-01') TO ('2023-10-01')","tableName":"i91_6_t_2023_q3","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-10-01') TO ('2024-01-01')","tableName":"i91_6_t_2023_q4","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-04-01')","tableName":"i91_6_t_2023_q1","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-04-01') TO ('2023-07-01')","tableName":"i91_6_t_2023_q2","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"i91_6_t"},"tableId":"i91_6_t_2023_q4","type":206} 
[TRACE] 2025-01-20 14:17:13.886 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Sync sub table's [i91_6_t_2023_q4] create table ddl, will add update master table [i91_6_t] metadata 
[TRACE] 2025-01-20 14:17:13.913 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_i91_6_t_2023_q4_678de9ec0dcc7213047a7015_678dea170dcc7213047a70cd 
[TRACE] 2025-01-20 14:17:13.913 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Create new table schema transform finished: TapTable id i91_6_t_2023_q4 name i91_6_t_2023_q4 storageEngine null charset null number of fields 3 
[TRACE] 2025-01-20 14:17:14.114 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[TRACE] 2025-01-20 14:17:14.185 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Incremental sync completed 
[TRACE] 2025-01-20 14:17:14.475 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@6de46d09: {"partitionMasterTableId":"i91_6_t","table":{"comment":"","id":"i91_6_t_2023_q1","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"name":"i91_6_t_2023_q1_pkey","primary":true,"unique":true}],"lastUpdate":1737353833750,"maxPKPos":2,"maxPos":3,"name":"i91_6_t_2023_q1","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(20,8)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":8,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-07-01') TO ('2023-10-01')","tableName":"i91_6_t_2023_q3","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-10-01') TO ('2024-01-01')","tableName":"i91_6_t_2023_q4","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-04-01')","tableName":"i91_6_t_2023_q1","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-04-01') TO ('2023-07-01')","tableName":"i91_6_t_2023_q2","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"i91_6_t"},"tableId":"i91_6_t_2023_q1","type":206}). Wait for all previous events to be processed 
[INFO ] 2025-01-20 14:17:14.476 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Starting batch read from 4 new tables 
[INFO ] 2025-01-20 14:17:14.477 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Starting batch read from table: i91_6_t_2023_q1 
[TRACE] 2025-01-20 14:17:14.477 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Table i91_6_t_2023_q1 is going to be initial synced 
[INFO ] 2025-01-20 14:17:14.507 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Table i91_6_t_2023_q1 has been completed batch read 
[INFO ] 2025-01-20 14:17:14.507 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Starting batch read from table: i91_6_t_2023_q2 
[TRACE] 2025-01-20 14:17:14.507 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Table i91_6_t_2023_q2 is going to be initial synced 
[INFO ] 2025-01-20 14:17:14.544 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Table i91_6_t_2023_q2 has been completed batch read 
[INFO ] 2025-01-20 14:17:14.545 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Starting batch read from table: i91_6_t_2023_q3 
[TRACE] 2025-01-20 14:17:14.545 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Table i91_6_t_2023_q3 is going to be initial synced 
[TRACE] 2025-01-20 14:17:14.577 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Query snapshot row size completed: qa_pg_15_partition_1736997187904_1988(e7dfad96-ebeb-4926-a6c1-a0c6465eff82) 
[INFO ] 2025-01-20 14:17:14.577 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Table i91_6_t_2023_q3 has been completed batch read 
[INFO ] 2025-01-20 14:17:14.577 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Starting batch read from table: i91_6_t_2023_q4 
[TRACE] 2025-01-20 14:17:14.577 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Table i91_6_t_2023_q4 is going to be initial synced 
[INFO ] 2025-01-20 14:17:14.593 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Table i91_6_t_2023_q4 has been completed batch read 
[TRACE] 2025-01-20 14:17:14.593 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-01-20 14:17:14.593 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Batch read completed. 
[TRACE] 2025-01-20 14:17:14.594 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Incremental sync starting... 
[TRACE] 2025-01-20 14:17:14.594 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Initial sync completed 
[TRACE] 2025-01-20 14:17:14.594 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Starting stream read, table list: [i91_6_t_2023_q4, i91_6_t_2023_q3, i91_6_t, i91_6_t_2023_q2, i91_6_t_2023_q1, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":12156633120,\"lsn_commit\":12156632824,\"lsn\":12156633120,\"txId\":5448846,\"ts_usec\":1737353833147318}"} 
[INFO ] 2025-01-20 14:17:14.594 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Starting incremental sync using database log parser 
[WARN ] 2025-01-20 14:17:14.595 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-01-20 14:17:14.797 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Using an existing logical replication slot, slotName:tapdata_cdc_853f6936_08e6_495a_a9ed_4b78c6a867f6 
[TRACE] 2025-01-20 14:17:14.797 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Connector PostgreSQL incremental start succeed, tables: [i91_6_t_2023_q4, i91_6_t_2023_q3, i91_6_t, i91_6_t_2023_q2, i91_6_t_2023_q1, _tapdata_heartbeat_table], data change syncing 
[TRACE] 2025-01-20 14:17:18.363 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@6de46d09: {"partitionMasterTableId":"i91_6_t","table":{"comment":"","id":"i91_6_t_2023_q1","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"name":"i91_6_t_2023_q1_pkey","primary":true,"unique":true}],"lastUpdate":1737353833750,"maxPKPos":2,"maxPos":3,"name":"i91_6_t_2023_q1","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(20,8)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":8,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-07-01') TO ('2023-10-01')","tableName":"i91_6_t_2023_q3","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-10-01') TO ('2024-01-01')","tableName":"i91_6_t_2023_q4","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-04-01')","tableName":"i91_6_t_2023_q1","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-04-01') TO ('2023-07-01')","tableName":"i91_6_t_2023_q2","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"i91_6_t"},"tableId":"i91_6_t_2023_q1","type":206}) 
[TRACE] 2025-01-20 14:17:18.364 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@27a9a217: {"partitionMasterTableId":"i91_6_t","table":{"comment":"","id":"i91_6_t_2023_q2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"name":"i91_6_t_2023_q2_pkey","primary":true,"unique":true}],"lastUpdate":1737353833830,"maxPKPos":2,"maxPos":3,"name":"i91_6_t_2023_q2","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(20,8)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":8,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-07-01') TO ('2023-10-01')","tableName":"i91_6_t_2023_q3","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-10-01') TO ('2024-01-01')","tableName":"i91_6_t_2023_q4","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-04-01')","tableName":"i91_6_t_2023_q1","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-04-01') TO ('2023-07-01')","tableName":"i91_6_t_2023_q2","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"i91_6_t"},"tableId":"i91_6_t_2023_q2","type":206}). Wait for all previous events to be processed 
[TRACE] 2025-01-20 14:17:21.354 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Will create sub partition table [i91_6_t_2023_q1] to target, master table is: i91_6_t 
[TRACE] 2025-01-20 14:17:24.571 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@27a9a217: {"partitionMasterTableId":"i91_6_t","table":{"comment":"","id":"i91_6_t_2023_q2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"name":"i91_6_t_2023_q2_pkey","primary":true,"unique":true}],"lastUpdate":1737353833830,"maxPKPos":2,"maxPos":3,"name":"i91_6_t_2023_q2","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(20,8)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":8,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-07-01') TO ('2023-10-01')","tableName":"i91_6_t_2023_q3","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-10-01') TO ('2024-01-01')","tableName":"i91_6_t_2023_q4","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-04-01')","tableName":"i91_6_t_2023_q1","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-04-01') TO ('2023-07-01')","tableName":"i91_6_t_2023_q2","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"i91_6_t"},"tableId":"i91_6_t_2023_q2","type":206}) 
[TRACE] 2025-01-20 14:17:24.573 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@59a34702: {"partitionMasterTableId":"i91_6_t","table":{"comment":"","id":"i91_6_t_2023_q3","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"name":"i91_6_t_2023_q3_pkey","primary":true,"unique":true}],"lastUpdate":1737353833863,"maxPKPos":2,"maxPos":3,"name":"i91_6_t_2023_q3","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(20,8)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":8,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-07-01') TO ('2023-10-01')","tableName":"i91_6_t_2023_q3","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-10-01') TO ('2024-01-01')","tableName":"i91_6_t_2023_q4","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-04-01')","tableName":"i91_6_t_2023_q1","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-04-01') TO ('2023-07-01')","tableName":"i91_6_t_2023_q2","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"i91_6_t"},"tableId":"i91_6_t_2023_q3","type":206}). Wait for all previous events to be processed 
[TRACE] 2025-01-20 14:17:27.610 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Will create sub partition table [i91_6_t_2023_q2] to target, master table is: i91_6_t 
[TRACE] 2025-01-20 14:17:27.685 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: Table model: TapTable id i91_6_t_2023_q2 name i91_6_t_2023_q2 storageEngine null charset null number of fields 3io.tapdata.entity.event.ddl.table.TapCreateTableEvent@1cf9e19c: {"partitionMasterTableId":"i91_6_t","table":{"ancestorsName":"i91_6_t_2023_q2","id":"i91_6_t_2023_q2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"sale_date"}],"name":"i91_6_t_pkey","primary":true,"unique":true}],"lastUpdate":1737353652739,"maxPKPos":2,"maxPos":3,"name":"i91_6_t_2023_q2","nameFieldMap":{"amount":{"autoInc":false,"autoincrement":"NO","columnPosition":3,"createSource":"auto","dataType":"decimal(20,8)","dataTypeTemp":"decimal(20,8)","deleted":false,"fieldName":"amount","id":"678de9b30dcc7213047a6f5a_i91_6_t_amount","isNullable":false,"name":"amount","nullable":false,"originalDataType":"numeric","originalFieldName":"amount","partitionKey":false,"pos":3,"previousDataType":"numeric","previousFieldName":"amount","primaryKey":false,"primaryKeyPos":0,"primaryKeyPosition":0,"pureDataType":"numeric","source":"auto","sourceDbType":"SQL Server","tapType":{"fixed":true,"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":8,"type":8},"unique":false,"virtual":false},"sale_date":{"autoInc":false,"autoincrement":"NO","columnPosition":2,"createSource":"auto","dataType":"date","dataTypeTemp":"date","deleted":false,"fieldName":"sale_date","id":"678de9b30dcc7213047a6f5a_i91_6_t_sale_date","isNullable":false,"name":"sale_date","nullable":false,"originalDataType":"date","originalFieldName":"sale_date","partitionKey":false,"pos":2,"previousDataType":"date","previousFieldName":"sale_date","primaryKey":true,"primaryKeyPos":2,"primaryKeyPosition":2,"pureDataType":"date","source":"auto","sourceDbType":"SQL Server","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"unique":false,"virtual":false},"id":{"autoInc":false,"autoincrement":"NO","columnPosition":1,"createSource":"auto","dataType":"int","dataTypeTemp":"int","deleted":false,"fieldName":"id","id":"678de9b30dcc7213047a6f5a_i91_6_t_id","isNullable":false,"name":"id","nullable":false,"originalDataType":"integer","originalFieldName":"id","partitionKey":false,"pos":1,"previousDataType":"integer","previousFieldName":"id","primaryKey":true,"primaryKeyPos":1,"primaryKeyPosition":1,"pureDataType":"integer","source":"auto","sourceDbType":"SQL Server","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"unique":false,"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"sale_date"},{"fieldAsc":true,"name":"id"}],"indexMap":{"sale_date":{"fieldAsc":true,"name":"sale_date"},"id":{"fieldAsc":true,"name":"id"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-07-01') TO ('2023-10-01')","tableName":"i91_6_t_2023_q3","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-10-01') TO ('2024-01-01')","tableName":"i91_6_t_2023_q4","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-10-01'","value":"'2023-10-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-04-01')","tableName":"i91_6_t_2023_q1","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-04-01') TO ('2023-07-01')","tableName":"i91_6_t_2023_q2","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-04-01'","value":"'2023-04-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-07-01'","value":"'2023-07-01'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"i91_6_t"},"tableId":"i91_6_t_2023_q2","time":1737353847569,"type":206}
 
[ERROR] 2025-01-20 14:17:27.685 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - target write record(s) failed <-- Error Message -->
target write record(s) failed

<-- Simple Stack Trace -->
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: Duplicate range boundary values are not allowed in partition function boundary values list. The boundary value being added is already present at ordinal 2 of the boundary value list.
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1662)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.doExecuteStatement(SQLServerStatement.java:898)
	com.microsoft.sqlserver.jdbc.SQLServerStatement$StmtExecCmd.doExecute(SQLServerStatement.java:793)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	...

<-- Full Stack Trace -->
Table model: TapTable id i91_6_t_2023_q2 name i91_6_t_2023_q2 storageEngine null charset null number of fields 3
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:465)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeCreateTableFunction(HazelcastTargetPdkDataNode.java:803)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:615)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$33(HazelcastTargetPdkDataNode.java:595)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1761)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:925)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:843)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:192)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:124)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: code: 0 | message: Update partition function failed, sql: ALTER PARTITION FUNCTION partition_function_i91_6_t() SPLIT RANGE ('2023-04-01'), message: Duplicate range boundary values are not allowed in partition function boundary values list. The boundary value being added is already present at ordinal 2 of the boundary value list.
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createSubPartitionTable$16(HazelcastTargetPdkBaseNode.java:364)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doCreateTable(HazelcastTargetPdkBaseNode.java:325)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createSubPartitionTable(HazelcastTargetPdkBaseNode.java:362)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:430)
	... 17 more
Caused by: code: 0 | message: Update partition function failed, sql: ALTER PARTITION FUNCTION partition_function_i91_6_t() SPLIT RANGE ('2023-04-01'), message: Duplicate range boundary values are not allowed in partition function boundary values list. The boundary value being added is already present at ordinal 2 of the boundary value list.
	at io.tapdata.connector.mssql.MssqlConnector.createSubPartitionTable(MssqlConnector.java:283)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$14(HazelcastTargetPdkBaseNode.java:371)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:77)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$15(HazelcastTargetPdkBaseNode.java:370)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 22 more
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: Duplicate range boundary values are not allowed in partition function boundary values list. The boundary value being added is already present at ordinal 2 of the boundary value list.
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1662)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.doExecuteStatement(SQLServerStatement.java:898)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement$StmtExecCmd.doExecute(SQLServerStatement.java:793)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.execute(SQLServerStatement.java:766)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.execute(JdbcContext.java:155)
	at io.tapdata.connector.mssql.MssqlConnector.createSubPartitionTable(MssqlConnector.java:281)
	... 31 more

[TRACE] 2025-01-20 14:17:27.700 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Job suspend in error handle 
[TRACE] 2025-01-20 14:17:27.700 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Node qa_pg_15_partition_1736997187904_1988[e7dfad96-ebeb-4926-a6c1-a0c6465eff82] running status set to false 
[TRACE] 2025-01-20 14:17:27.732 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Incremental sync completed 
[TRACE] 2025-01-20 14:17:27.732 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_e7dfad96-ebeb-4926-a6c1-a0c6465eff82_1737353834199 
[TRACE] 2025-01-20 14:17:27.734 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_e7dfad96-ebeb-4926-a6c1-a0c6465eff82_1737353834199 
[TRACE] 2025-01-20 14:17:27.734 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Node qa_pg_15_partition_1736997187904_1988[e7dfad96-ebeb-4926-a6c1-a0c6465eff82] schema data cleaned 
[TRACE] 2025-01-20 14:17:27.739 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Node qa_pg_15_partition_1736997187904_1988[e7dfad96-ebeb-4926-a6c1-a0c6465eff82] monitor closed 
[TRACE] 2025-01-20 14:17:27.739 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_pg_15_partition_1736997187904_1988] - Node qa_pg_15_partition_1736997187904_1988[e7dfad96-ebeb-4926-a6c1-a0c6465eff82] close complete, cost 36 ms 
[TRACE] 2025-01-20 14:17:27.753 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Node qa_sqlserver_2019_partition_1736997187904_1988[7db83e77-91c8-4aab-8f7a-6cc5a28819d2] running status set to false 
[TRACE] 2025-01-20 14:17:27.753 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_7db83e77-91c8-4aab-8f7a-6cc5a28819d2_1737353763097 
[TRACE] 2025-01-20 14:17:27.753 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_7db83e77-91c8-4aab-8f7a-6cc5a28819d2_1737353763097 
[TRACE] 2025-01-20 14:17:27.753 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Node qa_sqlserver_2019_partition_1736997187904_1988[7db83e77-91c8-4aab-8f7a-6cc5a28819d2] schema data cleaned 
[TRACE] 2025-01-20 14:17:27.754 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Node qa_sqlserver_2019_partition_1736997187904_1988[7db83e77-91c8-4aab-8f7a-6cc5a28819d2] monitor closed 
[TRACE] 2025-01-20 14:17:27.754 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728][qa_sqlserver_2019_partition_1736997187904_1988] - Node qa_sqlserver_2019_partition_1736997187904_1988[7db83e77-91c8-4aab-8f7a-6cc5a28819d2] close complete, cost 14 ms 
[INFO ] 2025-01-20 14:17:31.925 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Task [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-01-20 14:17:31.943 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-01-20 14:17:31.944 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@58577e05 
[TRACE] 2025-01-20 14:17:32.068 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Stop task milestones: 678dea170dcc7213047a70cd(t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728)  
[TRACE] 2025-01-20 14:17:32.068 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Stopped task aspect(s) 
[TRACE] 2025-01-20 14:17:32.070 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Snapshot order controller have been removed 
[INFO ] 2025-01-20 14:17:32.071 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Task stopped. 
[TRACE] 2025-01-20 14:17:32.095 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Remove memory task client succeed, task: t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728[678dea170dcc7213047a70cd] 
[TRACE] 2025-01-20 14:17:32.099 - [t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728] - Destroy memory task client cache succeed, task: t_91.6-pgPartitionMssqlPartitionCreateSubTableAfterTaskRunning_1736997187904_1988-1737353728[678dea170dcc7213047a70cd] 
