[TRACE] 2025-05-26 17:49:10.240 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Start task milestones: 683439150425e46ff01ee816(Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588) 
[INFO ] 2025-05-26 17:49:10.303 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Loading table structure completed 
[TRACE] 2025-05-26 17:49:10.341 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-26 17:49:10.361 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - The engine receives Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-26 17:49:10.380 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Task started 
[TRACE] 2025-05-26 17:49:10.394 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ac0b2f5-edaa-48e0-997d-49611adf0c06] start preload schema,table counts: 1 
[TRACE] 2025-05-26 17:49:10.394 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7ddd1077-de93-4a12-b218-83810e645da2] start preload schema,table counts: 1 
[TRACE] 2025-05-26 17:49:10.394 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ac0b2f5-edaa-48e0-997d-49611adf0c06] preload schema finished, cost 0 ms 
[TRACE] 2025-05-26 17:49:10.394 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7ddd1077-de93-4a12-b218-83810e645da2] preload schema finished, cost 0 ms 
[INFO ] 2025-05-26 17:49:10.935 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Sink connector(_tapdata_heartbeat_table) initialization completed 
[TRACE] 2025-05-26 17:49:10.935 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-26 17:49:11.127 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Apply table structure to target database 
[INFO ] 2025-05-26 17:49:11.127 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Source connector(_tapdata_heartbeat_table) initialization completed 
[TRACE] 2025-05-26 17:49:11.127 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[TRACE] 2025-05-26 17:49:11.127 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[TRACE] 2025-05-26 17:49:11.127 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-26 17:49:11.127 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Use existing stream offset: {"syncStage":null,"beginTimes":1748252951127,"lastTimes":1748252951127,"lastTN":0,"tableStats":{}} 
[INFO ] 2025-05-26 17:49:11.159 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Starting batch read from 1 tables 
[INFO ] 2025-05-26 17:49:11.159 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Starting batch read from table: _tapdata_heartbeat_table 
[TRACE] 2025-05-26 17:49:11.159 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Query snapshot row size completed: _tapdata_heartbeat_table(6ac0b2f5-edaa-48e0-997d-49611adf0c06) 
[INFO ] 2025-05-26 17:49:11.159 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2025-05-26 17:49:11.159 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2025-05-26 17:49:11.159 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Table _tapdata_heartbeat_table has been completed batch read 
[INFO ] 2025-05-26 17:49:11.159 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Batch read completed. 
[TRACE] 2025-05-26 17:49:11.159 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1748252951127,"lastTimes":1748252951127,"lastTN":0,"tableStats":{}} 
[INFO ] 2025-05-26 17:49:11.159 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Starting incremental sync using database log parser 
[INFO ] 2025-05-26 17:49:11.159 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[TRACE] 2025-05-26 17:49:11.183 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[TRACE] 2025-05-26 17:49:12.974 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Process after table "_tapdata_heartbeat_table" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-26 17:49:12.975 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-05-26 19:01:33.002 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Exception skipping - The current exception does not match the skip exception strategy, message: The client connection was terminated by the mongodb server 
[ERROR] 2025-05-26 19:01:33.023 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - The client connection was terminated by the mongodb server <-- Error Message -->
The client connection was terminated by the mongodb server

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=************:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketReadException: Exception receiving message}, caused by {java.net.SocketException: Connection reset}}]
	com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:184)
	com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:143)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:100)
	com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:285)
	...

<-- Full Stack Trace -->
com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=************:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketReadException: Exception receiving message}, caused by {java.net.SocketException: Connection reset}}]
	at io.tapdata.mongodb.MongodbExceptionCollector.collectTerminateByServer(MongodbExceptionCollector.java:53)
	at io.tapdata.mongodb.MongodbExceptionCollector.throwWriteExIfNeed(MongodbExceptionCollector.java:33)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1349)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:55)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:969)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:887)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:865)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:838)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:791)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:681)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:760)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:812)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:759)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=************:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketReadException: Exception receiving message}, caused by {java.net.SocketException: Connection reset}}]
	at com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:184)
	at com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	at com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:143)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:100)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:285)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:201)
	at com.mongodb.client.internal.MongoCollectionImpl.executeBulkWrite(MongoCollectionImpl.java:447)
	at com.mongodb.client.internal.MongoCollectionImpl.bulkWrite(MongoCollectionImpl.java:428)
	at io.tapdata.mongodb.writer.MongodbWriter.write(MongodbWriter.java:138)
	at io.tapdata.mongodb.writer.MongodbWriter.writeRecord(MongodbWriter.java:101)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1345)
	... 30 more

[TRACE] 2025-05-26 19:01:33.023 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Job suspend in error handle 
[TRACE] 2025-05-26 19:01:33.060 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ac0b2f5-edaa-48e0-997d-49611adf0c06] running status set to false 
[INFO ] 2025-05-26 19:01:33.060 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Stop connector 
[TRACE] 2025-05-26 19:01:33.064 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode_6ac0b2f5-edaa-48e0-997d-49611adf0c06_1748252951033 
[TRACE] 2025-05-26 19:01:33.064 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode_6ac0b2f5-edaa-48e0-997d-49611adf0c06_1748252951033 
[TRACE] 2025-05-26 19:01:33.064 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ac0b2f5-edaa-48e0-997d-49611adf0c06] schema data cleaned 
[TRACE] 2025-05-26 19:01:33.064 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ac0b2f5-edaa-48e0-997d-49611adf0c06] monitor closed 
[TRACE] 2025-05-26 19:01:33.064 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ac0b2f5-edaa-48e0-997d-49611adf0c06] close complete, cost 4 ms 
[TRACE] 2025-05-26 19:01:33.064 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7ddd1077-de93-4a12-b218-83810e645da2] running status set to false 
[TRACE] 2025-05-26 19:01:33.077 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode_7ddd1077-de93-4a12-b218-83810e645da2_1748252950847 
[TRACE] 2025-05-26 19:01:33.077 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode_7ddd1077-de93-4a12-b218-83810e645da2_1748252950847 
[TRACE] 2025-05-26 19:01:33.077 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7ddd1077-de93-4a12-b218-83810e645da2] schema data cleaned 
[TRACE] 2025-05-26 19:01:33.078 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7ddd1077-de93-4a12-b218-83810e645da2] monitor closed 
[TRACE] 2025-05-26 19:01:33.078 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7ddd1077-de93-4a12-b218-83810e645da2] close complete, cost 13 ms 
[INFO ] 2025-05-26 19:01:33.442 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Task [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-05-26 19:01:38.451 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Task [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-26 19:01:38.654 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-26 19:01:39.456 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@6368876 
[TRACE] 2025-05-26 19:01:39.458 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@ae6c6e4 
[TRACE] 2025-05-26 19:01:39.458 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Stop task milestones: 683439150425e46ff01ee816(Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588)  
[TRACE] 2025-05-26 19:01:39.587 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Stopped task aspect(s) 
[TRACE] 2025-05-26 19:01:39.587 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Snapshot order controller have been removed 
[INFO ] 2025-05-26 19:01:39.587 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Task stopped. 
[TRACE] 2025-05-26 19:01:39.618 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Remove memory task client succeed, task: Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588[683439150425e46ff01ee816] 
[TRACE] 2025-05-26 19:01:39.618 - [Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588] - Destroy memory task client cache succeed, task: Heartbeat-qa_mongodb_repl_42240_share_1742442514217_8588[683439150425e46ff01ee816] 
