package io.tapdata.connector.oracle.datasource;

import com.google.protobuf.Empty;
import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NegotiationType;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.tapdata.data.*;
import org.junit.Ignore;

import java.util.Iterator;

@Ignore
@Deprecated
public class TestOracleLogDecoder {

    private static final ManagedChannel channel = NettyChannelBuilder.forAddress("*************", 50051)
            .negotiationType(NegotiationType.PLAINTEXT).build();
    private static final io.tapdata.data.OracleRedoLogServerGrpc.OracleRedoLogServerBlockingStub blockingStub = io.tapdata.data.OracleRedoLogServerGrpc.newBlockingStub(channel);

    private static final MessageHeader messageHeader = MessageHeader.newBuilder().setProtocolVersion(1).build();
    private TaskHandleRequest taskHandleRequest = TaskHandleRequest.newBuilder()
            .setHeader(messageHeader)
            .setId("123")
            .build();

    @Ignore
    public void testGetPingMethod() {
        PingRequest pingRequest = PingRequest.newBuilder().setHeader(messageHeader).setCode(12).build();
        System.out.println(blockingStub.ping(pingRequest));
    }

    @Ignore
    public void testGetServerInfoMethod() {
        ServerInfoResponse serverInfoResponse = blockingStub.serverInfo(Empty.newBuilder().build());
        System.out.println(serverInfoResponse);
    }

    @Ignore
    public void testGetCreateRedologTaskMethod() {
        SourceTable sourceTable = SourceTable.newBuilder().setName("TestAsm").build();
        ReaderSource source = ReaderSource.newBuilder().setSchema("TAPDATA")
                .setSyncWaitTimeMS(5000).setType(ReaderType.ONLINE)
                .addSourceDBConnection(SourceConnection.newBuilder()
                        .setHost("*************")
                        .setPort(1521)
                        .setName("tap19c")
                        .setUsername("TAPDATA")
                        .setPassword("Gotapd8#").build())
                .addSourceASMConnection(SourceConnection.newBuilder()
                        .setHost("*************")
                        .setPort(1521)
                        .setName("+ASM")
                        .setUsername("TAPDATA")
                        .setPassword("Gotapd8#").build())
                .addSourceTable(sourceTable).build();

        //开启任务
        RedologRequest redologRequest = RedologRequest.newBuilder()
                .setHeader(MessageHeader.newBuilder().setProtocolVersion(1).build())
                .setId("123")
                .setSource(source)
                .setTarget(WriterTarget.newBuilder().setType(WriterType.GRPC).build())
                .build();

        ControlResponse response = blockingStub.createRedologTask(redologRequest);
        System.out.println(response.getCode());
        System.out.println(response);
    }

    @Ignore
    public void testGetDeleteRedologTaskMethod() {
    }

    @Ignore
    public void testGetPauseRedologTaskMethod() {
    }

    @Ignore
    public void testGetResumeRedologTaskMethod() {
    }

    @Ignore
    public void testGetListRedologTaskStatesMethod() {
    }

    @Ignore
    public void testGetGetRedologTaskStateMethod() {

        GetRedologTaskStateResponse redologTaskState = blockingStub.getRedologTaskState(taskHandleRequest);
        System.out.println(redologTaskState);
    }

    @Ignore
    public void testGetPullRedoLogMethod() {
        //拉取日志
        Iterator<PullRedoLogResponse> logResponseIterator = blockingStub.pullRedoLog(taskHandleRequest);
        while (logResponseIterator.hasNext()) {
            PullRedoLogResponse redoLogResponse = logResponseIterator.next();
            System.out.println(redoLogResponse);
            System.out.println("----------------------------------------------------");
        }
    }

    @Ignore
    public void stopPull() {
//        blockingStub.pauseRedologTask(taskHandleRequest);
        blockingStub.deleteRedologTask(taskHandleRequest);
        channel.shutdown();
    }
}
