package io.tapdata.connector.oracle.cdc.grpc;

import io.grpc.ManagedChannel;
import io.tapdata.common.cdc.ILogMiner;
import io.tapdata.data.MessageHeader;
import io.tapdata.data.PullRedoLogResponse;

public interface ILogMinerLogDecoder extends ILogMiner {

    MessageHeader getMessageHeader();

    ManagedChannel getManagedChannel();

    String getTaskHandleRequestID();

    void onPullRedoLogResponseReceive(PullRedoLogResponse pullRedoLogResponse) throws Throwable;

}
