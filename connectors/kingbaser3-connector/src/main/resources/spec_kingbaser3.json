{"properties": {"name": "KingBaseES-R3", "icon": "icons/kingbaser3.png", "doc": "${doc}", "id": "kingbaser3", "tags": ["Database"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}], "connection": {"type": "object", "properties": {"host": {"required": true, "type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 1}, "port": {"required": true, "type": "string", "title": "${port}", "default": 54321, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-index": 2}, "database": {"required": true, "type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 3}, "schema": {"required": true, "type": "string", "title": "${schema}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_owner", "x-index": 4}, "extParams": {"type": "string", "title": "${extParams}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "additionalString", "x-index": 5}, "user": {"type": "string", "title": "${user}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 6}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 7}, "logPluginName": {"required": true, "type": "string", "title": "${logPluginName}", "default": "pgoutput", "x-decorator": "FormItem", "x-component": "Select", "apiServerKey": "logPlugin", "x-index": 8, "enum": [{"label": "DECODERBUFS", "value": "decoderbufs"}, {"label": "WAL2JSON", "value": "wal2j<PERSON>"}, {"label": "WAL2JSONRDS", "value": "wal2json_rds"}, {"label": "WAL2JSONSTREMING", "value": "wal2json_streaming"}, {"label": "WAL2JSONRDSSTREAMING", "value": "wal2json_rds_streaming"}, {"label": "PGOUTPUT", "value": "pgoutput"}]}}}}, "messages": {"default": "en_US", "en_US": {"doc": "docs/kingbaser3_en_US.md", "host": "Host", "port": "Port", "database": "database", "schema": "schema", "extParams": "extParams", "user": "user", "password": "password", "logPluginName": "logPluginName"}, "zh_CN": {"doc": "docs/kingbaser3_zh_CN.md", "host": "地址", "port": "端口", "database": "数据库", "schema": "模型", "extParams": "额外参数", "user": "账号", "password": "密码", "logPluginName": "日志插件"}, "zh_TW": {"doc": "docs/kingbaser3_zh_TW.md", "host": "地址", "port": "端口", "database": "數據庫", "schema": "模型", "extParams": "額外參數", "user": "賬號", "password": "密碼", "logPluginName": "日誌插件"}}, "dataTypes": {"smallint": {"bit": 16, "priority": 3, "value": [-32768, 32767], "to": "TapNumber"}, "integer": {"bit": 32, "priority": 1, "value": [-2147483648, 2147483647], "to": "TapNumber"}, "bigint": {"bit": 64, "priority": 3, "value": [-9223372036854775808, 9223372036854775807], "to": "TapNumber"}, "numeric[($precision,$scale)]": {"precision": [1, 1000], "scale": [0, 1000], "fixed": true, "preferPrecision": 20, "preferScale": 8, "priority": 1, "to": "TapNumber"}, "real": {"bit": 32, "priority": 2, "precision": [1, 6], "scale": [0, 6], "fixed": false, "to": "TapNumber"}, "double precision": {"priority": 2, "precision": [1, 17], "preferPrecision": 11, "preferScale": 4, "scale": [0, 17], "fixed": false, "to": "TapNumber"}, "character[($byte)]": {"byte": 10485760, "priority": 2, "preferByte": 255, "fixed": true, "to": "TapString"}, "character varying[($byte)]": {"byte": 10485760, "priority": 1, "defaultByte": 10485760, "preferByte": 2000, "to": "TapString"}, "text": {"byte": "4g", "priority": 2, "to": "TapString"}, "bytea": {"byte": "4g", "priority": 1, "to": "TapBinary"}, "bit[($byte)]": {"byte": 64, "defaultByte": 1, "priority": 3, "fixed": true, "queryOnly": true, "to": "TapBinary"}, "bit varying[($byte)]": {"byte": 64, "defaultByte": 64, "priority": 1, "queryOnly": true, "to": "TapBinary"}, "boolean": {"bit": 1, "priority": 1, "to": "TapBoolean"}, "date": {"range": ["0001-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd", "priority": 1, "to": "TapDate"}, "interval": {"range": ["-99:59:59", "99:59:59"], "pattern": "HH:mm:ss", "queryOnly": true, "to": "TapTime"}, "timestamp[($fraction)] without time zone": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "withTimeZone": false, "defaultFraction": 6, "priority": 1, "to": "TapDateTime"}, "timestamp[($fraction)] with time zone": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "withTimeZone": true, "defaultFraction": 6, "priority": 2, "to": "TapDateTime"}, "time[($fraction)] without time zone": {"range": ["00:00:00", "23:59:59"], "pattern": "HH:mm:ss", "fraction": [0, 6], "withTimeZone": false, "defaultFraction": 6, "priority": 1, "to": "TapTime"}, "time[($fraction)] with time zone": {"range": ["00:00:00", "23:59:59"], "pattern": "HH:mm:ss", "fraction": [0, 6], "withTimeZone": true, "defaultFraction": 6, "priority": 2, "to": "TapTime"}, "point": {"to": "TapBinary", "queryOnly": true}, "line": {"to": "TapBinary", "queryOnly": true}, "lseg": {"to": "TapBinary", "queryOnly": true}, "box": {"to": "TapBinary", "queryOnly": true}, "path": {"to": "TapBinary", "queryOnly": true}, "polygon": {"to": "TapBinary", "queryOnly": true}, "circle": {"to": "TapBinary", "queryOnly": true}, "cidr": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "inet": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "macaddr": {"to": "TapString", "queryOnly": true}, "uuid": {"to": "TapString", "preferByte": 256, "queryOnly": true}, "xml": {"to": "TapString", "queryOnly": true}, "json": {"to": "TapString", "queryOnly": true}, "tsvector": {"to": "TapString", "queryOnly": true}, "tsquery": {"to": "TapString", "queryOnly": true}, "oid": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "regproc": {"to": "TapString", "preferByte": 255, "queryOnly": true}, "regprocedure": {"to": "TapString", "queryOnly": true}, "regoper": {"to": "TapString", "queryOnly": true}, "regoperator": {"to": "TapString", "queryOnly": true}, "regclass": {"to": "TapString", "queryOnly": true}, "regtype": {"to": "TapString", "queryOnly": true}, "regconfig": {"to": "TapString", "queryOnly": true}, "regdictionary": {"to": "TapString", "queryOnly": true}}}