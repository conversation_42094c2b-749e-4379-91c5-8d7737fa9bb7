# HttpReceiverConnector

## 1 配置须知

- 表名称
  
- 服务URL访问地址

- 服务令牌过期时间（单位秒）

- 供应商列表
  
- 数据处理脚本

### 1.1 表名称

    表名称建议不要使用特殊字符，诸如：！@#￥%……&*（）...

### 1.2 服务URL访问地址

服务URL地址为固定值，您可以使用服务URL向当前数据源推送数据，推送的数据将作为增量数据写入到您配置好的表中。

出于安全性考虑，服务URL采用PartVariable方式填入访问秘钥，形如：
    
    假设服务URL为：http://0.0.0.0:8080/api/{access_token}
    
    假设access_token为：54656fafr465dafas1av1g5d65
    
    你的完整服务URL为：http://0.0.0.0:8080/api/54656fafr465dafas1av1g5d65

因此在推送数据时：

如果是首次推送，需要调用 ***服务令牌获取链接*** 来获取服务令牌

如果是服务令牌过期了，也需要调用 ***服务令牌获取链接*** 来获取服务令牌

关于***服务令牌获取链接***，请参考下面的说明。

### 1.3 服务令牌过期时间（单位秒）

服务令牌过期时间，默认3600秒，表示通过使用 ***服务令牌获取链接*** 获取的服务令牌的使用有效期，过期后将需要使用 ***服务令牌获取链接*** 来重新获取服务令牌

值得注意的是，针对不同的供应商，每次生成的 ***服务令牌获取链接*** 都是按照生成时刻的过期时间来配置的，举个例子：
  
  
假如Gavin需要为自己名下的多个供应商配置数据推送连接，要求各方推送数据到某张表中：

（1）第一步，Gavin配置了一个3600秒的过期时间，然后添加一个新的供应商，并为当前供应商生成了一个 ***服务令牌获取链接***，此时当前供应商使用这个 ***服务令牌获取链接*** 获取到的服务令牌有效期都是3600秒；

（2）第二步，Gavin修改了过期时间为7200秒，同样为添加一个新的供应商，并为当前供应商生成了一个 ***服务令牌获取链接***，此时当前供应商使用这个 ***服务令牌获取链接*** 获取到的服务令牌有效期都是7200秒；

（3）值得注意的是修改后的过期时间并不影响历史生成的 ***服务令牌获取链接***：
    在上诉例子中是如何体现的呢？ 那么就是Gavin先后生成的两个 ***服务令牌获取链接*** 中过期时间各自独立，即两者的过期时间分别是3600秒和7200秒。

### 1.4 供应商列表

#### 1.4.1 供应商ID

这个值需要用户手动输入，输入后用于区分供应商，可在处理脚本中通过参数 ***supplierId*** 来区分，例如：

    您添加了一个供应商，并设置了供应商ID为 ‘Gavin’ ，那么这个供应商推送数据来后，数据处理脚本中，supplierId的值将为 ‘Gavin’

输入供应商ID后，方可为对应的供应商生成 ***服务令牌获取链接***，

保存当前配置后，新增的供应商信息将生效，

对应的供应商即可使用当前 ***服务令牌获取链接*** 来获取服务令牌，

获取服务令牌后可以拼入到 ***服务URL访问地址*** 中来进行数据推送


#### 1.4.2 服务令牌获取链接

服务令牌获取链接是用于首次进行数据推送时获取服务令牌以及服务令牌过期后重新获取服务令牌，

每添加一个服务供应商，在输入供应商ID后可一键生成对应的服务令牌获取链接，

服务令牌获取链接是个长时连接，有效期99年，如不再需要您可以手动删除。

### 1.3 数据处理脚本

    因为每个第三方平台的消息推送体系都有各自的规则，推送过来的数据也是各有千秋，

    因此需要您根据您的需求来使用此脚本灵活取用对应的数据；

    因此，数据处理脚本是用来处理第三方平台推送过来的消息，从消息中取出对应需要的数据并以指定规则返回。
    
    例如：

    （1）某平台以WebHook推送过来一个事件：

```json
{
  "eventType": "ADD_MESSAGE",
  "time": 1256467862232000,
  "message": {
    "title": "This is sample message",
    "context": "Sample message for everyone.",
    "sender": "Zhang San",
    "to": "Li Si",
    "time": 1256467862232000
  },
  "sender": {
    "name": "Zhang San",
    "id": "12354-5664-2130-45-460",
    "dept": "OpenDept"
  }
}
```

    （2）如果对上诉消息的需求是我们仅需要message中的数据，因此，数据处理脚本可以是：

```js
function handleEvent(eventData, supplierId) {
    let eventType = eventData.eventType;
    //判断事件类型，转换为统一事件类型标识（i：新增，u：修改，d：删除）
    let type = "i";
    switch(eventType){
        case "ADD_MESSAGE" : type = "i";break;
        case "UPDATE_MESSAGE": type = "u";break;
        case "DELETE_MESSAGE": type = "d";break;
    }
    return {
        "before": eventData.message, //事件发生前的数据，删除类型的事件此值是必填
        "after": eventData.message,  //事件发生后的结果，新增、修改类型的事件此值为必填
        "opType": type,              //事件的类型，i：新增，u：修改，d：删除
        "time": eventData.time       //事件发生的时间点，值类型为事件戳
    }
}
```

    （3）经过数据处理脚本，您将得到如下数据，并在任务中以下数据为入库的最终数据：

```json
{
  "title": "This is sample message",
  "context": "Sample message for everyone.",
  "sender": "Zhang San",
  "to": "Li Si",
  "time": 1256467862232000
}
```

## 2 关于试运行

    1. 您需要保证您在第三方平台使用这里的服务URL配置且已配置好了一个有效的消息推送服务。

    2. 您点击试运行前可以看到历史消息中的某一条或指定条数，且可使用这些数据进行试运行，并观察试运行结果；

    3. 若您有是在第三方平台初次配置好消息推送服务，那么您可能存在没有历史消息数据的情况，
    此时，您可以前往第三方平台通过在平台操作相应的数据来触发第三方平台的消息推送，
    此时在消息服务配置有效的情况下您可以在历史数据中获取相应的数据用于试运行；   
    
    4. 当然，如果您已知或者可以通过一些方式获取第三方平台推送过来的消息数据的结果，
    您不妨直接手动构建相应的消息数据来试运行您的数据处理脚本，以此来验证您的数据处理脚本是否符合您的预期。