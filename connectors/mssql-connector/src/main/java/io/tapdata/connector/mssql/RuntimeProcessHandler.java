package io.tapdata.connector.mssql;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.StringJoiner;

public class RuntimeProcessHandler {
    private static final boolean OSIsWin = Optional.ofNullable(System.getProperty("os.name")).orElse("").toLowerCase().contains("windows");
    boolean showErrorMsg;
    boolean showInputInfo;
    List<String> inputLines;
    List<String> commands;

    private final String commandSpiltChar;
    private final String[] commandArray;
    private static final InputResult EMPTY_RESULT = InputResult.create("Not any commands", "");

    public static class InputResult {
        String info;
        String error;
        public static InputResult create(String info, String error) {
            InputResult result = new InputResult();
            result.info = info;
            result.error = error;
            return result;
        }

        public String getError() {
            return error;
        }

        public String getInfo() {
            return info;
        }
    }
    public RuntimeProcessHandler(boolean showInputInfo, boolean showErrorMsg) {
        this(showInputInfo, showErrorMsg, new String[0]);
    }

    private RuntimeProcessHandler(boolean showInputInfo, boolean showErrorMsg, String... commands) {
        this.showInputInfo = showInputInfo;
        this.showErrorMsg = showErrorMsg;
        this.commands = new ArrayList<>();
        if (null != commands && commands.length != 0) {
            this.commands.addAll(Arrays.asList(commands));
        }
        commandSpiltChar = OSIsWin ? " && " : " ; ";
        String commandStart = OSIsWin ? "cmd" : "/bin/sh";
        String commandType = OSIsWin ? "/c" : "-c";
        commandArray = new String[]{commandStart, commandType, ""};
    }

    public RuntimeProcessHandler inputLines(String... lines) {
        if (null == this.inputLines) this.inputLines = new ArrayList<>();
        if (null == lines || lines.length <= 0) return this;
        for (String line : lines) {
            this.inputLines.add(line);
        }
        return this;
    }

    public RuntimeProcessHandler cleanInputLines() {
        this.inputLines = new ArrayList<>();
        return this;
    }

    public RuntimeProcessHandler commands(String... commands) {
        if (null == commands) this.commands = new ArrayList<>();
        if (null == commands || commands.length <= 0) return this;
        this.commands.addAll(Arrays.asList(commands));
        return this;
    }

    public RuntimeProcessHandler cleanCommands() {
        this.commands = new ArrayList<>();
        return this;
    }

    private void execInput(OutputStream outputStream) throws IOException {
        if (inputLines.isEmpty() || null == outputStream) return;
        BufferedOutputStream os = null;
        try {
            os = new BufferedOutputStream(outputStream);
            for (String line : inputLines) {
                os.write((Optional.ofNullable(line).orElse(" ") + "\n").getBytes(StandardCharsets.UTF_8));
            }
        } finally {
            if (null != os) {
                os.flush();
                os.close();
            }
        }
    }

    public InputResult executeCommand() throws IOException, InterruptedException {
        if (commands.isEmpty()) return EMPTY_RESULT;
        StringJoiner joiner = new StringJoiner(commandSpiltChar);
        for (String cmd : commands) {
            joiner.add(cmd);
        }
        commandArray[2] = joiner.toString();
        Process pro = Runtime.getRuntime().exec(commandArray);
        execInput(pro.getOutputStream());
        pro.waitFor();
        return InputResult.create(
                showInputInfo ? input(pro.getInputStream()) : " ",
                showErrorMsg ? input(pro.getErrorStream()) : " ");
    }

    public String input(InputStream pro) throws IOException {
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder();
        try {
            br = new BufferedReader(new InputStreamReader(pro));
            String line = null;
            while ((line = br.readLine()) != null) {
                sb.append(line).append("\n");
            }
        } catch (Exception e) {
            sb.append(e.getMessage()).append("\n");
        } finally {
            if (null != br) {
                br.close();
            }
        }
        return sb.toString();
    }
}
