{"properties": {"name": "Oceanbase(Oracle)", "icon": "icons/oceanbase.png", "id": "oceanbase_oracle", "doc": "${doc}", "tags": ["Database"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists", "just_insert"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}], "supportDDL": {"events": ["new_field_event", "alter_field_name_event", "alter_field_attributes_event", "drop_field_event"]}, "connection": {"type": "object", "properties": {"host": {"type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 1, "required": true}, "port": {"type": "string", "title": "${port}", "default": 2881, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-index": 2, "required": true}, "tenant": {"type": "string", "title": "${tenant}", "x-decorator": "FormItem", "x-component": "Input", "x-index": 3, "required": true}, "database": {"type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 4, "required": true}, "user": {"type": "string", "title": "${user}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 5, "required": true}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 6}, "extParams": {"type": "string", "title": "${extParams}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "extParams", "x-index": 7}, "rootServerList": {"required": true, "type": "string", "title": "${rootServerList}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "rootServerList", "x-decorator-props": {"tooltip": "${rootServerListTooltip}"}, "x-index": 8}, "rawLogServerHost": {"required": true, "type": "string", "title": "${rawLogServerHost}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "rawLogServerHost", "x-index": 9}, "rawLogServerPort": {"required": true, "type": "string", "default": 8190, "title": "${rawLogServerPort}", "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "rawLogServerPort", "x-index": 10}, "cdcUser": {"type": "string", "title": "${cdcUser}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "cdcUser", "default": "root@sys", "x-index": 11, "required": true}, "cdcPassword": {"type": "string", "title": "${cdcPassword}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "cdcPassword", "x-index": 12}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-index": 13, "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}}}, "node": {"type": "object", "properties": {"closeNotNull": {"type": "boolean", "title": "${closeNotNull}", "default": true, "x-index": 1, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${closeNotNullTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "hashSplit": {"type": "boolean", "title": "${hashSplit}", "default": false, "x-index": 10, "x-decorator": "FormItem", "x-component": "Switch", "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "maxSplit": {"required": true, "type": "string", "title": "${maxSplit}", "default": 16, "x-index": 12, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 2, "max": 1000000}, "x-reactions": [{"dependencies": ["$inputs", ".hashSplit"], "fulfill": {"state": {"display": "{{!$deps[0].length && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}, "batchReadThreadSize": {"required": true, "type": "string", "title": "${batchReadThreadSize}", "default": 8, "x-index": 13, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 1, "max": 32}, "x-reactions": [{"dependencies": ["$inputs", ".hashSplit"], "fulfill": {"state": {"display": "{{!$deps[0].length && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}}}}, "messages": {"default": "en_US", "en_US": {"host": "Host", "port": "Port", "tenant": "Tenant", "database": "database", "user": "user", "password": "password", "extParams": "Connection Parameter String", "rootServerList": "OceanBase Root Server List", "rootServerListTooltip": "format demo: *************:2882:2881,*************:2882:2881,*************:2882:2881", "rawLogServerHost": "Raw Log Server Host", "rawLogServerPort": "Raw Log Server Port", "cdcUser": "CDC User", "cdcPassword": "CDC Password", "timezone": "timezone", "closeNotNull": "Ignore <PERSON>", "closeNotNullTooltip": "When the switch is turned on, non empty restrictions of string type are discarded", "doc": "docs/oceanbase_oracle_en_US.md", "hashSplit": "Single Table Concurrent Read", "maxSplit": "Max Split Count for Single Table", "batchReadThreadSize": "Single Table Concurrent Read Thread Size"}, "zh_CN": {"host": "地址", "port": "端口", "tenant": "租户", "database": "数据库", "user": "账号", "password": "密码", "extParams": "连接参数", "rootServerList": "OceanBase多节点列表", "rootServerListTooltip": "格式案例：*************:2882:2881,*************:2882:2881,*************:2882:2881", "rawLogServerHost": "日志服务器地址", "rawLogServerPort": "日志服务器端口", "cdcUser": "CDC账号", "cdcPassword": "CDC密码", "timezone": "时区", "closeNotNull": "忽略NotNull", "closeNotNullTooltip": "开关打开时会将字符串类型的非空限制丢弃", "doc": "docs/oceanbase_oracle_zh_CN.md", "hashSplit": "单表并发读", "maxSplit": "单表最大分片数", "batchReadThreadSize": "单表并发读线程数"}, "zh_TW": {"host": "地址", "port": "端口", "tenant": "租戶", "database": "數據庫", "user": "賬號", "password": "密碼", "extParams": "連接參數", "rootServerList": "OceanBase多节点列表", "rootServerListTooltip": "格式案例：*************:2882:2881,*************:2882:2881,*************:2882:2881", "rawLogServerHost": "日誌服務器地址", "rawLogServerPort": "日誌服務器端口", "cdcUser": "CDC賬號", "cdcPassword": "CDC密碼", "timezone": "時區", "closeNotNull": "忽略NotNull", "closeNotNullTooltip": "開關打開時會將字串類型的非空限制丟棄", "doc": "docs/oceanbase_oracle_zh_TW.md", "hashSplit": "單表並發讀", "maxSplit": "單表最大分片數", "batchReadThreadSize": "單表並發讀線程數"}}, "dataTypes": {"CHAR[($byte)]": {"byte": 2000, "priority": 1, "defaultByte": 1, "fixed": true, "to": "TapString"}, "NCHAR[($byte)]": {"byte": 1000, "priority": 2, "defaultByte": 1, "byteRatio": 3, "queryOnly": true, "fixed": true, "to": "TapString"}, "VARCHAR2[($byte)]": {"byte": 4000, "priority": 1, "preferByte": 2000, "to": "TapString"}, "NVARCHAR2[($byte)]": {"byte": 2000, "priority": 2, "queryOnly": true, "byteRatio": 3, "to": "TapString"}, "INTEGER": {"defaultPrecision": 38, "priority": 1, "to": "TapNumber"}, "NUMBER(*,$scale)": {"scale": [-84, 127], "precision": [1, 38], "fixed": true, "preferPrecision": 20, "defaultPrecision": 38, "preferScale": 8, "defaultScale": 0, "priority": 1, "to": "TapNumber"}, "NUMBER[($precision,$scale)]": {"precision": [1, 38], "scale": [-84, 127], "fixed": true, "preferPrecision": 20, "defaultPrecision": 38, "preferScale": 8, "defaultScale": 0, "priority": 1, "to": "TapNumber"}, "NUMBER($precision)": {"precision": [1, 38], "fixed": true, "preferPrecision": 20, "defaultPrecision": 38, "priority": 1, "to": "TapNumber"}, "FLOAT[($precision)]": {"precision": [1, 126], "scale": 125, "preferScale": 8, "fixed": false, "defaultPrecision": 126, "priority": 2, "to": "TapNumber"}, "BINARY_FLOAT": {"value": ["-3.402823466E+38", "3.402823466E+38"], "scale": 37, "preferScale": 8, "preferPrecision": 12, "priority": 3, "queryOnly": true, "to": "TapNumber"}, "BINARY_DOUBLE": {"value": ["-1.7976931348623157E+308", "1.7976931348623157E+308"], "preferPrecision": 20, "preferScale": 8, "scale": 307, "priority": 3, "to": "TapNumber"}, "DATE": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "defaultFraction": 0, "pattern": "yyyy-MM-dd HH:mm:ss", "priority": 1, "to": "TapDateTime"}, "TIMESTAMP[($fraction)]": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 9], "defaultFraction": 6, "withTimeZone": false, "priority": 2, "to": "TapDateTime"}, "TIMESTAMP[($fraction)] WITH TIME ZONE": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 9], "defaultFraction": 6, "priority": 2, "withTimeZone": true, "to": "TapDateTime"}, "TIMESTAMP[($fraction)] WITH LOCAL TIME ZONE": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 9], "withTimeZone": true, "defaultFraction": 6, "priority": 2, "to": "TapDateTime"}, "INTERVAL YEAR[($num)] TO MONTH": {"queryOnly": true, "to": "TapString"}, "INTERVAL DAY[($num)] TO SECOND(6)": {"queryOnly": true, "to": "TapString"}, "CLOB": {"byte": "4g", "pkEnablement": false, "priority": 2, "to": "TapString"}, "NCLOB": {"byte": "4g", "queryOnly": true, "priority": 3, "to": "TapString"}, "BLOB": {"byte": "4g", "pkEnablement": false, "priority": 2, "to": "TapBinary"}, "BFILE": {"queryOnly": true, "pkEnablement": false, "to": "TapBinary"}, "RAW($byte)": {"queryOnly": true, "to": "TapBinary"}, "LONG RAW": {"queryOnly": true, "to": "TapBinary"}}}